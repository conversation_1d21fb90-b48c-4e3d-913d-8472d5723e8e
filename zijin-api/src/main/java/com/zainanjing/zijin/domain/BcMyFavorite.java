package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 我的收藏对象 bc_my_favorite
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@NoArgsConstructor
@SuperBuilder
@TableName(value = "bc_my_favorite")
public class BcMyFavorite implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 收藏时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 收藏品id,收藏节目则此处对应节目的主键id
     */
    private Long favoriteId;

    /**
     * 收藏品类型:1---节目2---专辑;3---分集
     */
    private Integer type;

}
