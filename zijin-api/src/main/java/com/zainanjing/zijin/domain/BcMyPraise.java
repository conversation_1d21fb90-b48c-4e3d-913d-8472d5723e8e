package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 我的点赞对象 bc_my_praise
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@NoArgsConstructor
@SuperBuilder
@TableName(value = "bc_my_praise")
public class BcMyPraise implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 收藏时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 相关主键ID
     */
    private Long praiseId;

    /**
     * 被点赞uid
     */
    private Long toUserId;

    /**
     * 点赞类型:1---主播动态 2---爆品; 3---精品节目
     */
    private Integer type;

    /**
     * 是否已读 1是0否
     */
    private Integer isRead;

}
