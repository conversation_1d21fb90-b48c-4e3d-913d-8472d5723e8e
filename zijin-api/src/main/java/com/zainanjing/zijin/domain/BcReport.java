package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 举报对象 bc_report
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@NoArgsConstructor
@SuperBuilder
@TableName(value = "bc_report")
public class BcReport implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId
    private Long id;

    /**
     * $column.columnComment
     */
    private Date createTime;

    /**
     * $column.columnComment
     */
    private Date updateTime;

    /**
     * 是否删除 1是 0否
     */
    private Integer isDel;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * $column.columnComment
     */
    private String reportContent;

}
