package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcChoice;
import com.zainanjing.zijin.domain.BcChoiceAlbum;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.domain.BcMyFavorite;
import org.apache.ibatis.annotations.Param;

/**
 * 我的收藏Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface BcMyFavoriteMapper extends GenericMapper<BcMyFavorite> {

    IPage<BcForum> selectProgramListByUid(IPage<BcForum> page, @Param("uid") Long uid);

    IPage<BcChoiceAlbum> selectAlbumListByUid(IPage<BcChoiceAlbum> page, @Param("uid") Long uid);

    IPage<BcChoice> selectAlbumEpisodeListByUid(IPage<BcChoice> page, @Param("uid") Long uid);

}
