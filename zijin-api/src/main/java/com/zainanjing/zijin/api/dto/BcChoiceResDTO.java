package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcChoiceResDTO {
    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("album_id")
    private String albumId;
    private String name;
    private String description;
    private String logo;
    private String url;
    private String view;
    @JsonProperty("virtual_view")
    private String virtualView;
    private String duration;
    @JsonProperty("is_show")
    private String isShow;
    private String sort;
    @JsonProperty("share_icon")
    private String shareIcon;
    @JsonProperty("share_title")
    private String shareTitle;
    @JsonProperty("share_desc")
    private String shareDesc;
    private String type;
    private String status;
    @JsonProperty("total_view")
    private String totalView;
    @JsonProperty("comment_num")
    private String commentNum;
    @JsonProperty("is_comment")
    private String isComment;
    private Integer seconds;
    @JsonProperty("album_name")
    private String albumName;

}
