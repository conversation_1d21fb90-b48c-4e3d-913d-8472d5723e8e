package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.BcComment;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.domain.BcPost;
import com.zainanjing.zijin.domain.BcVerifyConfig;
import com.zainanjing.zijin.dto.SiteCollectDTO;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;


@RestController
@RequestMapping("/api-bc/bcPost")
public class PostController {
    @Autowired
    private IBcPostService bcPostService;
    @Autowired
    private IBcVerifyConfigService bcVerifyConfigService;
    @Autowired
    private BcExtMapper bcExtMapper;
    @Autowired
    private IBcCommentService bcCommentService;
    @Autowired
    private IBcTopicService bcTopicService;
    @Autowired
    private IBcForumService bcForumService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;


    @Anonymous
    @GetMapping("/list")
    public ResResult list(@RequestParam(value = "type", required = false) String type,
                          @RequestParam(value = "forumId", required = false) String forumId,
                          @RequestParam(value = "latestId", required = false) String latestId,
                          @RequestParam(value = "topicId", required = false) String topicId) {
        HashMap<String, Object> filterMap = new HashMap<>();
        String userId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        filterMap.put("uid", userId);
        //记录当前用户来查询屏蔽用户
        filterMap.put("sessionUid", userId);
        filterMap.put("status", ZijinConstants.CONSTANTS_0);
        if (ZijinConstants.POST_TYPE_TOPIC.equals(type)) {
            filterMap.put("topicId", topicId);
        }
        if (StrUtil.isNotEmpty(forumId)) {
            filterMap.put("forumId", forumId);
        }
        if (StrUtil.isNotEmpty(latestId)) {
            filterMap.put("latestId", latestId);
        }
        if (StrUtil.isEmpty(forumId)) {
            filterMap.put("isHost", ZijinConstants.CONSTANTS_0);
        } else {
            filterMap.put("isHost", ZijinConstants.CONSTANTS_1);
        }
        if (StrUtil.isNotEmpty(type)) {
            filterMap.put("type", type);
        }
        List<BcPost> bcPostList = bcPostService.searchIncludeImgAndComment(filterMap);
        this.getGolIsComment(bcPostList);

        String closeKey = RedisKey.CLOSE.getKey(userId);
        Set<Object> set = CommonUtil.redisKeysFind(closeKey);
        //屏蔽用户 friendUids
        List<String> friendUids = new ArrayList<String>();
        if (set != null && set.size() > 0) {
            StringBuffer printUids = new StringBuffer();//打印从redis获取屏蔽用户信息
            for (Object obj : set) {
                if (obj instanceof String) {
                    String key = (String) obj;
                    //nanjing_CLOSE_UID_FRIENDUID
                    String[] arr = key.split("_");
                    if (arr != null && arr.length > 0) {
                        friendUids.add(arr[arr.length - 1]);
                        printUids.append(arr[arr.length - 1]).append("  ");
                    }
                }
            }
        }
        List<BcPost> bcPosts = new ArrayList<>();
        for (BcPost bcPost : bcPostList) {
            if (!friendUids.contains(bcPost.getUid())) {
                bcPosts.add(bcPost);
            }
        }
        return ResResult.success(bcPosts);
    }


    @GetMapping("/replayList")
    public ResResult replayList(@RequestParam(value = "forumId") String forumId,
                                @RequestParam(value = "currentPage") Integer currentPage,
                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        HashMap<String, Object> filterMap = new HashMap<>();
        filterMap.put("forumId", forumId);
        filterMap.put("type", ZijinConstants.POST_TYPE_REPLAY);
        filterMap.put("isForReply", ZijinConstants.YES);
        //判断当前互动板块是否有直播
        BcForum bcForum = bcForumService.thisDateHaveLive(Long.parseLong(forumId));
        if (bcForum == null) {
            throw new ServiceException("节目版块信息不存在");
        }
        int start = (currentPage - 1) * pageSize;
        //设置是否是直播
        String liveDateStr = null;
        //当前互动区内有直播
        if (Integer.valueOf(ZijinConstants.YES).equals(bcForum.getIsLive())) {
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            liveDateStr = sdf.format(date);
            //设置分页 直播时会多出一条数据 故从数据库中捞取时应减掉一条数据 用以保存客户端获取时每页都是10条
            if (currentPage == 1) {
                pageSize = pageSize - 1;
            } else {
                start = start - 1;
            }
        }
        filterMap.put("orderStartDate", "desc");
        List<BcPost> bcPostList = bcPostService.search(filterMap, start, pageSize);

        if (Integer.valueOf(ZijinConstants.YES).equals(bcForum.getIsLive())) {
            BcPost bcPost = new BcPost();
            bcPost.setId(bcForum.getId() * (-1));
            bcPost.setStartDate(liveDateStr);
            bcPost.setFmName(bcForum.getFmName());
            bcPost.setUrl(bcForum.getUrl());
            bcPost.setIsAnonymous(0);
            bcPost.setUserName(null);
            bcPost.setProgramLogo(bcForum.getProgramLogo());
            bcPost.setForumId(bcForum.getId());
            bcPost.setForumName(bcForum.getProgramName());
            bcPostList.add(0, bcPost);
        }
        for (BcPost bcPost : bcPostList) {
            String imgUrl = bcPost.getImgUrl();
            Integer avaType = bcPost.getAvatarType();
            imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
            bcPost.setImgUrl(imgUrl);
            bcPost.setStartTime(bcForum.getStartTime());
            bcPost.setEndTime(bcForum.getEndTime());
            //设置当前回听状态为直播中
            if (StrUtil.isNotEmpty(liveDateStr) && bcPost.getStartDate().equals(liveDateStr)) {
                bcPost.setIsLive(Integer.valueOf(ZijinConstants.YES));
            }
        }
        return ResResult.success(bcPostList);
    }

    @Log(title = "视听-发帖", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @Transactional(rollbackFor = {Exception.class,}, propagation = Propagation.REQUIRED)
    public ResResult save(HttpServletRequest request, @RequestParam(value = "content", required = false) String content,
                          @RequestParam(value = "forumId", required = false) String forumId, @RequestParam(value = "bcImgIds", required = false) String bcImgIds,
                          @RequestParam(value = "isAnonymous", required = false) String isAnonymous, @RequestParam(value = "topicId", required = false) String topicId) {

        wangyiCheckUtil.checkText(content);

        Long uid = SecurityUtils.getUserId();
        BcPost bcPost = new BcPost();
        //获取IP地址
        String ip = IpUtils.getIpAddr(request);
        bcPost.setIp(ip);
        bcPost.setRegion(AddressUtils.getRealAddressByIP(ip));
        bcPost.setProgramId(1L);
        if (StrUtil.isEmpty(forumId)) {
            throw new ServiceException("节目id不能为空");
        }
        bcPost.setForumId(Long.valueOf(forumId));
        bcPost.setColor("0");
        bcPost.setSubject("");
        bcPost.setIsRec(Integer.valueOf(ZijinConstants.NO));
        bcPost.setIsTop(Integer.valueOf(ZijinConstants.NO));
        bcPost.setSeeLevel(Integer.valueOf(ZijinConstants.NO));
        bcPost.setContent(content == null ? "" : content);
        bcPost.setIsAnonymous(isAnonymous == null ? 0 : Integer.parseInt(isAnonymous));

        String type = ZijinConstants.POST_TYPE_COMMON;
        if (StrUtil.isNotEmpty(topicId)) {
            type = ZijinConstants.POST_TYPE_TOPIC;
        } else {
            topicId = "0";
        }
        bcPost.setType(Integer.valueOf(type));
        bcPost.setTopicId(Long.valueOf(topicId));
        if (!ObjUtil.isNotNull(uid)) {
            bcPost.setUid(uid);
        }
        bcPost.setSort(Long.valueOf(ZijinConstants.CONSTANTS_0));
        //浏览数 评论数 点赞数
        bcPost.setClickNum(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        bcPost.setCommentNum(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        bcPost.setPraiseNum(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        bcPost.setCategory(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        bcPost.setUrl("");
        bcPost.setCreateTime(CommonUtil.getTimestamp());
        bcPost.setUpdateTime(CommonUtil.getTimestamp());
        bcPost.setStatus(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        bcPost.setIsComment(ZijinConstants.YES_COMMENT);
        saveOnAudit(bcPost);

        List<String> sensitiveWords = bcExtMapper.getSensitiveWords();
        if (StrUtil.isNotEmpty(content)) {
            for (String string : sensitiveWords) {
                if (content.contains(string)) {
                    throw new ServiceException("内容包含敏感词");
                }
            }
        }
        int i = bcPostService.savePost(bcPost, bcImgIds);
        if (i > 0) {
            applicationEventPublisher.publishEvent(new CommonEvent(bcPost, EventAction.builder()
                    .operator(SecurityUtils.getLoginUser())
                    .type(BusinessType.INSERT)
                    .build()));
        }
        return ResResult.success(i);
    }

    @PostMapping("/delete")
    @Transactional(rollbackFor = {Exception.class,}, propagation = Propagation.REQUIRED)
    public void delete(@RequestParam(value = "id") String id) {
        bcPostService.update(
                new UpdateWrapper<BcPost>().eq("id", id).set("status", Integer.valueOf(ZijinConstants.CONSTANTS_1)));
        //删帖子下回复
        bcCommentService.update(
                new UpdateWrapper<BcComment>().eq("post_id", Long.valueOf(id)).set("status", Integer.valueOf(ZijinConstants.CONSTANTS_1)));
    }

    @GetMapping("/findById")
    public ResResult findById(@RequestParam(value = "postId") String postId) {

        Long uid = SecurityUtils.getUserId();
        BcPost bcPost = bcPostService.findIncludeImgs(postId, String.valueOf(uid));
        if (bcPost == null) {
            throw new ServiceException("帖子不存在");
        }
        //如果是话题，浏览量+1
        if (bcPost.getType().equals(Integer.parseInt(ZijinConstants.POST_TYPE_TOPIC))) {
            bcTopicService.addTopicView(bcPost);
        }
        bcPost.setClickNum(CommonUtil.redisCommonFind(ZijinConstants.BCPOST + "_" + ZijinConstants.CLICKNUM + "_" + bcPost.getId()).intValue());
        bcPost.setPraiseNum(CommonUtil.redisCommonFind(ZijinConstants.BCPOST + "_" + ZijinConstants.PRAISENUM + "_" + bcPost.getId()).intValue());
        //帖子是否点赞 判断是否有点赞数
        Integer praiseNumber = bcPost.getPraiseNum();
        bcPost.setIsPraise(praiseNumber > 0 ? 1 : 0);
        //查询评论
        Map<String, Object> filterMap = new HashMap<String, Object>();
        filterMap.put("postId", postId);
        filterMap.put("uid", uid);
        filterMap.put("status", ZijinConstants.CONSTANTS_0);
        //记录当前用户来查询屏蔽用户
        filterMap.put("sessionUid", uid);
        List<BcComment> bcCommentList = bcCommentService.search(filterMap);
        filterMap.put("clickNum", "add");
        filterMap.remove("uid");
        //更新点击量
        CommonUtil.redisCommonAdd(ZijinConstants.BCPOST + "_" + ZijinConstants.CLICKNUM + "_" + bcPost.getId());
        bcPost.setBcCommentList(bcCommentList);
        //是否收藏
        if (null != uid) {
            queryIsCollect(String.valueOf(uid), bcPost);
        }
        return ResResult.success(bcPost);
    }


    public void getGolIsComment(List<BcPost> bcPostList) {

        BcVerifyConfig bcVerifyConfig = bcVerifyConfigService.getOne(new QueryWrapper<BcVerifyConfig>()
                .eq("verify_type", ZijinConstants.INTERACT_TOPIC_KEY));
        if (bcVerifyConfig == null) {
            return;
        }
        Integer isComment = bcVerifyConfig.getIsComment();
        if (isComment.equals(Integer.valueOf(ZijinConstants.CONSTANTS_0))) {
            for (BcPost bcPost : bcPostList) {
                bcPost.setIsComment(1);
            }
        }
    }

    private void saveOnAudit(BcPost bcPost) {
        Long resId = null;
        BcVerifyConfig bcVerifyConfig = new BcVerifyConfig();
        if (bcPost.getType().equals(Integer.parseInt(ZijinConstants.POST_TYPE_COMMON))) {
            resId = bcPost.getForumId();
            bcVerifyConfig = bcVerifyConfigService.getOne(new QueryWrapper<BcVerifyConfig>()
                    .eq("verify_type", ZijinConstants.INTERACT_GENERAL_KEY));
        } else {
            resId = bcPost.getTopicId();
            bcVerifyConfig = bcVerifyConfigService.getOne(new QueryWrapper<BcVerifyConfig>()
                    .eq("verify_type", ZijinConstants.INTERACT_TOPIC_KEY));
        }
        if (null != bcVerifyConfig) {
            Integer isAudit = bcVerifyConfig.getIsAudit();
            //0 不审核
            if (Integer.valueOf(ZijinConstants.CONSTANTS_0).equals(isAudit)) {
                //可以直接发出去
                bcPost.setStatus(Integer.valueOf(ZijinConstants.CONSTANTS_0));
            } else {
                //需要审核
                bcPost.setStatus(Integer.valueOf(ZijinConstants.BC_COMMENT_NEED_AUDIT));
            }
        }
    }

    private void queryIsCollect(String uid, BcPost bcPost) {
        Map<Object, Object> filterMap = new HashMap<Object, Object>();
        filterMap.put("ctype", Integer.parseInt(ZijinConstants.FORUM_COLLECT));
        filterMap.put("siteMemberId", Integer.parseInt(uid));
        filterMap.put("collectObjectId", bcPost.getForumId());
        IPage<SiteCollectDTO> siteCollectList = bcExtMapper.searchSiteCollect(Page.of(1, 1, false), filterMap);
        if (CollUtil.isEmpty(siteCollectList.getRecords())) {
            bcPost.setIsCollect(Integer.valueOf(ZijinConstants.NO));
        } else {
            bcPost.setIsCollect(Integer.valueOf(ZijinConstants.YES));
            bcPost.setCollectedid(siteCollectList.getRecords().get(0).getSiteCollectId());
        }
    }
}
