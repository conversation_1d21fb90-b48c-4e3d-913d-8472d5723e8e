package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcHotSearch;
import com.zainanjing.zijin.service.IBcHotSearchService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 搜索接口
 *
 * <AUTHOR>
 * @date 2020/12/10
 */
@RestController
@RequestMapping({"/api-bc/Search","/api-bc/search"})
public class SearchController {

    @Resource
    private IBcHotSearchService bcHotSearchService;

    /**
     * 获取热词列表
     *
     * @param currentPage 当前页码
     * @param pageSize    每页大小
     * @return 热词列表
     */
    @GetMapping("/getHotSearchList")
    public ResResult getHotSearchList(@RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<BcHotSearch> page = bcHotSearchService.lambdaQuery().page(Page.of(currentPage, pageSize, false));
        if (CollUtil.isEmpty(page.getRecords())) {
            return ResResult.success(List.of());
        }
        List<Map<String, String>> list = new ArrayList<>();
        for (BcHotSearch bcHotSearch : page.getRecords()) {
            list.add(Map.of("content", bcHotSearch.getSearchContent()));
        }
        return ResResult.success(list);
    }

    /**
     * 关键字搜索 TODO 未发现入口，先不做
     * @param searchKey 搜索内容
     * @param userId 用户ID
     * @param searchType 搜索类型:0—全部；1—节目；2—电台；3—主播；4—专辑 5-爆品
     * @return 搜索结果
     */
//    @GetMapping("/keyWord")
//    public ResResult keyWord(@RequestParam("searchKey") String searchKey,
//                         @RequestParam(required = false) Integer userId,
//                         @RequestParam(defaultValue = "0") Integer searchType) {
//        try {
//            if (StrUtil.isEmpty(searchKey)) {
//                return ResultUtil.error("searchKey不能为空");
//            }
//
//            Map<String, Object> params = new HashMap<>();
//            params.put("searchKey", searchKey.trim());
//            params.put("userId", userId != null ? userId : 0);
//            params.put("searchType", searchType);
//
//            Map<String, Object> result = searchService.keyWord(params);
//            return ResultUtil.success(result);
//        } catch (Exception e) {
//            return ResultUtil.error(e.getMessage());
//        }
//    }
}