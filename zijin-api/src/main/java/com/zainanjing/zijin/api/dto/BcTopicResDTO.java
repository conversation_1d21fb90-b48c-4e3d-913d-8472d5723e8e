package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcTopicResDTO {
    private Long id;
    @JsonProperty("create_time")
    private String createTime;
    private String name;
    private String description;
    @JsonProperty("cover_thumb")
    private String coverThumb;
    private Integer view;
    @JsonProperty("forum_id")
    private Long forumId;
    @JsonProperty("fm_id")
    private Long fmId;
    @JsonProperty("program_id")
    private Long programId;
    @JsonProperty("topic_date")
    private String topicDate;
    @JsonProperty("reply_num")
    private Integer replyNum;
    @JsonProperty("forum_name")
    private String forumName;
    @JsonProperty("fm_name")
    private String fmName;
    @JsonProperty("can_top_post")
    private Integer canTopPost;

    @JsonProperty("is_comment")
    private Integer isComment;

    private String content;

    public Integer getCanTopPost() {
        return canTopPost == null ? 0 : canTopPost;
    }
}
