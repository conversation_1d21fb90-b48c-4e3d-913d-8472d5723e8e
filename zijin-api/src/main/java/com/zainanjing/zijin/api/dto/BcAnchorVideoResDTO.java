package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
public class BcAnchorVideoResDTO {
    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("update_time")
    private String updateTime;
    @JsonProperty("anchor_id")
    private String anchorId;
    @JsonProperty("video_name")
    private String videoName;
    @JsonProperty("video_desc")
    private String videoDesc;
    @JsonProperty("video_duration")
    private String videoDuration;
    @JsonProperty("play_url")
    private String playUrl;
    @JsonProperty("cover_url")
    private String coverUrl;
    @JsonProperty("share_img_url")
    private String shareImgUrl;
    @JsonProperty("is_show")
    private String isShow;
    @JsonProperty("sort_no")
    private String sortNo;
}
