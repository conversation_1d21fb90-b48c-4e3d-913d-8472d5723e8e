package com.zainanjing.zijin.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcProgramTvLiveType;
import com.zainanjing.zijin.service.IBcProgramTvLiveTypeService;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 电视直播分类接口
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@RestController
@RequestMapping("/api-bc/tvLiveType")
public class ProgramTvLiveTypeController {

    @Resource
    private IBcProgramTvLiveTypeService bcProgramTvLiveTypeService;

    @Anonymous
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public ResResult getTvLiveTypeList(
            @RequestParam(value = "currentPage", defaultValue = "1") String currentPage,
            @RequestParam(value = "pageSize", defaultValue = "100") String pageSize) {
        List<BcProgramTvLiveType> bcProgramTvLiveTypeList = bcProgramTvLiveTypeService.list(
                new QueryWrapper<BcProgramTvLiveType>().eq("is_show", "1").eq("status", "0"));

        if (!CollectionUtils.isEmpty(bcProgramTvLiveTypeList)) {
            for (BcProgramTvLiveType bcProgramTvLiveType : bcProgramTvLiveTypeList) {
                bcProgramTvLiveType.setLogo(OSSUtil.getImageURL(bcProgramTvLiveType.getLogo()));
            }
        }
        return ResResult.success(bcProgramTvLiveTypeList);
    }
}