package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.DateUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.convenience.support.APISystemConfig;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.zijin.api.dto.BcAnchorResDTO;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.dto.GdmmUsersDTO;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分享数据接口
 */
@RestController
@RequestMapping("/api-bc/share")
public class ShareController {

    @Resource
    private IBcForumService bcForumService;

    @Resource
    private IBcProgramListService bcProgramListService;

    @Resource
    private IBcAnchorRelationService bcAnchorRelationService;

    @Resource
    private IBcAnchorService bcAnchorService;

    @Resource
    private IBcPostService bcPostService;

    @Resource
    private IBcFmService bcFmService;

    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcZiJinImgService bcZiJinImgService;

    @Resource
    private IBcCommentService bcCommentService;

    @Resource
    private IBcProgramLiveService bcProgramLiveService;

    @Resource
    private IBcProgramTvLiveService bcProgramTvLiveService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 分享成功回调
     */
    @Anonymous
    @PostMapping("/shareCallback")
    public void shareCallback(@RequestParam("resourceId") Long resourceId, @RequestParam("businessType") String businessType) {
        if (SecurityUtils.getLoginUser() != null) {
            //TODO 考虑通过获取时优化, 可以通过传分享地址？
            applicationEventPublisher.publishEvent(new CommonEvent(Map.of("resId", resourceId, "businessType", businessType), EventAction.builder()
                    .operator(SecurityUtils.getLoginUser())
                    .type(BusinessType.SHARE)
                    .build()));
        }
    }

    /**
     * 分享页跳转
     */
    @Anonymous
    @GetMapping("/radio/programDetail")
    public void programDetail(@RequestParam("resourceId") String resourceId, @RequestParam(value = "appscheme", required = false) String appscheme, HttpServletResponse response) {
        response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY); // 设置 301 状态码
        response.setHeader("Location", APISystemConfig.adminUrl + "/h5/radio/radioDetail.html?resourceId=" + resourceId + "&appscheme=" + appscheme);
    }

    /**
     * 分享页跳转
     */
    @Anonymous
    @GetMapping("/choicealbum/detail")
    public void choicealbumDetail(@RequestParam("resourceId") String resourceId, @RequestParam(value = "appscheme", required = false) String appscheme, HttpServletResponse response) {
        response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY); // 设置 301 状态码
        response.setHeader("Location", APISystemConfig.adminUrl + "/h5/choicealbum/videoDetail.html?resourceId=" + resourceId + "&appscheme=" + appscheme);
    }

    @Anonymous
    @GetMapping("/choicealbum/list")
    public void choicealbumVideoList(@RequestParam("resourceId") String resourceId, @RequestParam(value = "appscheme", required = false) String appscheme, HttpServletResponse response) {
        response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY); // 设置 301 状态码
        response.setHeader("Location", APISystemConfig.adminUrl + "/h5/choicealbum/videoList.html?resourceId=" + resourceId + "&appscheme=" + appscheme);
    }


    /**
     * 分享页跳转
     */
    @Anonymous
    @GetMapping("/live/detail")
    public void liveDetail(@RequestParam("resourceId") String resourceId, @RequestParam(value = "appscheme", required = false) String appscheme, HttpServletResponse response) {
        response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY); // 设置 301 状态码
        response.setHeader("Location", APISystemConfig.adminUrl + "/h5/live/detail.html?resourceId=" + resourceId + "&appscheme=" + appscheme);
    }

    /**
     * 分享页跳转
     */
    @Anonymous
    @GetMapping("/live/detailTv")
    public void liveDetailTv(@RequestParam("resourceId") String resourceId, @RequestParam(value = "appscheme", required = false) String appscheme, HttpServletResponse response) {
        response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY); // 设置 301 状态码
        response.setHeader("Location", APISystemConfig.adminUrl + "/h5/live/detailTv.html?resourceId=" + resourceId + "&appscheme=" + appscheme);
    }


    /**
     * 获取分享数据
     */
    @Anonymous
    @GetMapping("/radioById")
    public ResResult radio(@RequestParam("bcId") Long bcId) {

        BcForum bcForum = bcForumService.getById(bcId);
        if (ObjUtil.isNotNull(bcForum)) {
            BcProgramList bcProgramList = bcProgramListService.getById(bcForum.getProgramId());


            AtomicReference<String> repalyData = new AtomicReference<>(null);
            Map<String, Object> programData = new HashMap<>();
            programData.put("name", bcForum.getName());
            programData.put("logo", bcForum.getLogo());
            programData.put("description", bcForum.getDescription());
            if (bcProgramList != null) {
                List<BcAnchorRelation> bcAnchorRelations = bcAnchorRelationService.lambdaQuery().eq(BcAnchorRelation::getBcId, bcProgramList.getId()).list();
                if (CollUtil.isNotEmpty(bcAnchorRelations)) {
                    List<BcAnchor> anchorInfos = bcAnchorService.lambdaQuery().in(BcAnchor::getUserId, bcAnchorRelations.stream().map(BcAnchorRelation::getAnchorId).collect(Collectors.toSet()))
                            .select(BcAnchor::getAnchorName, BcAnchor::getAnchorImgUrl).list();
                    List<BcAnchorResDTO> bcAnchorResDTOS = BcApiConverter.convertAnchors(anchorInfos);
                    programData.put("anchorNames", bcAnchorResDTOS);
                }
            }

            LocalTime nowTime = LocalTime.now();

            if (nowTime.isAfter(LocalTime.parse(bcProgramList.getStartTime(), DateTimeFormatter.ofPattern("HH:mm")))
                    && nowTime.isBefore(LocalTime.parse(bcProgramList.getEndTime(), DateTimeFormatter.ofPattern("HH:mm")))) {
                //直播
                programData.put("isLive", 1);
                bcFmService.lambdaQuery().eq(BcFm::getId, bcForum.getFmId()).oneOpt().ifPresent(bcFm -> {
                    repalyData.set(bcFm.getUrl());
                });
            } else {
                programData.put("isLive", 2);
                bcPostService.lambdaQuery()
                        .eq(BcPost::getForumId, bcId).eq(BcPost::getType, 3).eq(BcPost::getStatus, 0)
                        .select(BcPost::getUrl)
                        .oneOpt().ifPresent(bcPost -> {
                            repalyData.set(bcPost.getUrl());
                            programData.put("isLive", 0);
                        });
            }

            List<BcPost> bcPosts = bcPostService.lambdaQuery().eq(BcPost::getForumId, bcId).eq(BcPost::getType, 1).eq(BcPost::getStatus, 0)
                    .orderByDesc(BcPost::getId)
                    .list();
            Map<String, Object> data = new HashMap<>();
            if (CollUtil.isNotEmpty(bcPosts)) {
                List<Long> uids = bcPosts.stream().map(BcPost::getUid).collect(Collectors.toList());
                Map<Long, String> imgs = bcZiJinImgService.lambdaQuery().in(BcZiJinImg::getPostId, bcPosts.stream().map(BcPost::getId).toList()).eq(BcZiJinImg::getType, 1)
                        .orderByDesc(BcZiJinImg::getId)
                        .list()
                        .stream().collect(Collectors.groupingBy(BcZiJinImg::getPostId, Collectors.mapping(x -> OSSUtil.getImageURL(x.getImgUrl()), Collectors.joining(","))));

                List<BcComment> bcComments = bcCommentService.lambdaQuery().in(BcComment::getPostId, bcPosts.stream().map(BcPost::getId).toList()).eq(BcComment::getType, 1).eq(BcComment::getStatus, 0)
                        .eq(BcComment::getParentId, 0)
                        .orderByDesc(BcComment::getId)
                        .select(BcComment::getPostId, BcComment::getContent, BcComment::getUid)
                        .list();
                Map<Long, List<BcComment>> commentsMaps = bcComments.stream().collect(Collectors.groupingBy(BcComment::getPostId, Collectors.toList()));
                uids.addAll(bcComments.stream().map(BcComment::getUid).collect(Collectors.toList()));
                final Map<Long, GdmmUsersDTO> gdmmUsersMap = new HashMap<>();
                if (CollUtil.isNotEmpty(uids)) {
                    List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uids", uids));
                    gdmmUsersMap.putAll(gdmmUsersDTOS.stream().collect(Collectors.toMap(GdmmUsersDTO::getUserId, Function.identity())));
                }
                List<Map<String, Object>> postList = bcPosts.stream().map(bcPost -> {
                    Map<String, Object> postMap = new HashMap<>();
                    postMap.put("content", bcPost.getContent());
                    postMap.put("contentImg", imgs.getOrDefault(bcPost.getId(), ""));
                    postMap.put("create_time", DateUtil.format(DateUtil.date(1000L * bcPost.getCreateTime()),"yyyy.MM.DD HH:mm"));
                    postMap.put("type", bcPost.getType());
                    postMap.put("user_name", gdmmUsersMap.getOrDefault(bcPost.getUid(), new GdmmUsersDTO()).getUserName());//用户
                    postMap.put("headImgUrl", OSSUtil.getImageURL(gdmmUsersMap.getOrDefault(bcPost.getUid(), new GdmmUsersDTO()).getAvatar()));
                    postMap.put("commentData", commentsMaps.getOrDefault(bcPost.getId(), List.of()).stream().map(bcComment -> {
                        Map<String, Object> commentMap = new HashMap<>();
                        commentMap.put("content", bcComment.getContent());
                        commentMap.put("user_name", gdmmUsersMap.getOrDefault(bcComment.getUid(), new GdmmUsersDTO()).getUserName());
                        return commentMap;
                    }).limit(2).collect(Collectors.toList()));
                    return postMap;
                }).collect(Collectors.toList());
                data.put("postData", postList);
            } else {
                data.put("postData", List.of());
            }

            data.put("programData", programData);
            data.put("repalyData", repalyData.get());
            return ResResult.success(data);
        }
        return ResResult.success();
    }

    @Anonymous
    @GetMapping("/liveById")
    public ResResult liveById(@RequestParam("resourceId") Long resourceId) {
        BcProgramLive programLive = bcProgramLiveService.lambdaQuery().eq(BcProgramLive::getId, resourceId)
                .select(BcProgramLive::getDescription, BcProgramLive::getName, BcProgramLive::getVideoType, BcProgramLive::getUrl, BcProgramLive::getTecentVideoUrl, BcProgramLive::getIsLive, BcProgramLive::getStartTime, BcProgramLive::getEndTime, BcProgramLive::getBanner)
                .one();
        if (programLive != null) {
            bcProgramLiveService.lambdaUpdate().eq(BcProgramLive::getId, resourceId).setIncrBy(BcProgramLive::getView, 1).update();
            programLive.setBanner(OSSUtil.getImageURL(programLive.getBanner()));
        }
        return ResResult.success(programLive);
    }

    @Anonymous
    @GetMapping("/liveTvById")
    public ResResult liveTvById(@RequestParam("resourceId") Long resourceId) {
        BcProgramTvLive programTvLive = bcProgramTvLiveService.lambdaQuery().eq(BcProgramTvLive::getId, resourceId)
                .select(BcProgramTvLive::getDescription, BcProgramTvLive::getName, BcProgramTvLive::getUrl, BcProgramTvLive::getIsLive, BcProgramTvLive::getStartTime, BcProgramTvLive::getEndTime, BcProgramTvLive::getLogo)
                .one();
        if (programTvLive != null) {
            programTvLive.setLogo(OSSUtil.getImageURL(programTvLive.getLogo()));
            bcProgramTvLiveService.lambdaUpdate().eq(BcProgramTvLive::getId, resourceId).setIncrBy(BcProgramTvLive::getView, 1).update();
        }
        return ResResult.success(programTvLive);
    }

}