package com.zainanjing.zijin.api.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcProgramTvLive;
import com.zainanjing.zijin.mapper.BcProgramTvLiveMapper;
import com.zainanjing.zijin.service.IBcProgramTvLiveService;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 电视直播接口
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@RestController
@RequestMapping("/api-bc/tvLive")
public class ProgramTvLiveController {

    @Resource
    private IBcProgramTvLiveService bcProgramTvLiveService;

    @Resource
    private BcProgramTvLiveMapper bcProgramTvLiveMapper;

    @Anonymous
    @RequestMapping(value = "/findById", method = {RequestMethod.GET, RequestMethod.POST})
    public ResResult findById(@RequestParam(value = "id") String id) {

        return ResResult.success(bcProgramTvLiveService.findById(id));
    }

    @Anonymous
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public ResResult getTvLiveList(
            @RequestParam(value = "typeId") String typeId,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize) {

        IPage<BcProgramTvLive> bcProgramTvLiveList = bcProgramTvLiveMapper.searchProgramTvLive(Page.of(currentPage, pageSize),
                Map.of("typeId", typeId,
                        "now", CommonUtil.getTimestamp()));

        if (!CollectionUtils.isEmpty(bcProgramTvLiveList.getRecords())) {
            for (BcProgramTvLive bcProgramTvLive : bcProgramTvLiveList.getRecords()) {
                bcProgramTvLive.setLogo(OSSUtil.getImageURL(bcProgramTvLive.getLogo()));
                bcProgramTvLive.setShareImg(OSSUtil.getImageURL(bcProgramTvLive.getShareImg()));
                //2023-10-30 西宁电视新增默认值
                bcProgramTvLive.setVideoType(1);
            }
        }
        return ResResult.success(bcProgramTvLiveList);
    }
}