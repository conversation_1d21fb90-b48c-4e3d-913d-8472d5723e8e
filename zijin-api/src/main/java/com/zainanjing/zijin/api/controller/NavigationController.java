package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcNavigationConfig;
import com.zainanjing.zijin.service.IBcNavigationConfigService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 导航接口
 *
 * <AUTHOR>
 * @date 2020/12/25
 */
@RestController
@RequestMapping({"/api-bc/Navigation", "/api-bc/navigation"})
public class NavigationController {

    @Resource
    private IBcNavigationConfigService bcNavigationConfigService;

    /**
     * 获取导航
     *
     * @param enName 英文名称
     * @param type   类型：1-普通导航，2-特殊导航
     * @return 导航列表
     */
    @Anonymous
    @GetMapping("/navigation")
    public ResResult navigation(@RequestParam(value = "enName", required = false) String enName,
                                @RequestParam(value = "type", defaultValue = "1") Integer type) {
        Long parentId = null;
        if (type == 1 && StrUtil.isNotBlank(enName)) {
            Optional<BcNavigationConfig> parent = bcNavigationConfigService.lambdaQuery().eq(BcNavigationConfig::getEnName, enName)
                    .eq(BcNavigationConfig::getParentId, 0)
                    .eq(BcNavigationConfig::getType, 1)
                    .eq(BcNavigationConfig::getIsShow, 1)
                    .eq(BcNavigationConfig::getIsDel, 0)
                    .select(BcNavigationConfig::getId).oneOpt();
            if (parent.isPresent()) {
                parentId = parent.get().getId();
            }
        }
        List<BcNavigationConfig> list = bcNavigationConfigService.lambdaQuery()
                .eq(BcNavigationConfig::getType, type)
                .eq(parentId != null, BcNavigationConfig::getParentId, parentId)
                .eq(BcNavigationConfig::getIsShow, 1)
                .eq(BcNavigationConfig::getIsDel, 0)
                .select(BcNavigationConfig::getId, BcNavigationConfig::getEnName, BcNavigationConfig::getCnName)
                .orderByDesc(BcNavigationConfig::getSort)
                .orderByAsc(BcNavigationConfig::getId)
                .list();
        return ResResult.success(BcApiConverter.convertNavigationConfigs(list));
    }
}