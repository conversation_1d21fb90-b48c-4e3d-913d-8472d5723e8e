package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.domain.BcProgramList;
import com.zainanjing.zijin.dto.SiteCollectDTO;
import com.zainanjing.zijin.service.IBcForumService;
import com.zainanjing.zijin.service.IBcProgramListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;


@RestController
@RequestMapping("/api-bc/bcForum")
public class ForumController {
    @Autowired
    private IBcForumService bcForumService;

    @Autowired
    private IBcProgramListService bcProgramListService;

    @Value("${city-enums.LOCATION}")
    private String city;

    @Anonymous
    @GetMapping("/list")
    public ResResult list(@RequestParam(value = "isRec", required = false) String isRec,
                          @RequestParam(value = "fmId", required = false) String fmId,
                          @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer currentPage,
                          @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        if ("1".equals(isRec)) {
            //表示取推荐的数据
            paramMap.put("isRec", isRec);
        }
        paramMap.put("fmId", fmId);
        Calendar calendar = Calendar.getInstance();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        paramMap.put("currDate", sdf.format(date));
        int currWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (currWeek == 0) {
            currWeek = 7;
        }
        paramMap.put("currWeek", currWeek + "");
        List<BcForum> bcForumList = new ArrayList<BcForum>();
        List<BcForum> bcForumList_t = new ArrayList<BcForum>();

        IPage<BcForum> bcForumList_ = bcForumService.selectOptionalList(Page.of(currentPage, pageSize, false), paramMap);
        if (!CollectionUtils.isEmpty(bcForumList_.getRecords())) {
            for (BcForum bcForum : bcForumList_.getRecords()) {
                List<BcAnchor> anchorByForumId = bcForumService.findAnchorByForumId(bcForum.getId());
                bcForum.setBcAnchorList(anchorByForumId);
                //兰州特殊处理 正在直播的不置顶
                if ("lanzhou".equals(city)) {
                    bcForumList.add(bcForum);
                } else {
                    if (bcForum.getIsLive() == 1) {
                        bcForumList.add(bcForum);
                    } else {
                        bcForumList_t.add(bcForum);
                    }
                }
            }
            bcForumList.addAll(bcForumList_t);
        }
        return ResResult.success(bcForumList);
    }

    @Anonymous
    @GetMapping("/findById")
    public ResResult findById(@RequestParam(value = "id") String id) {
        Long uid = SecurityUtils.getUserId();
        if (ObjUtil.isNull(uid)) {
            uid = 0L;
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        //获取当前系统时间戳  以及当前星期几
        Calendar calendar = Calendar.getInstance();
        //currDate   应该是19：00这种
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        paramMap.put("currDate", sdf.format(date));
        int currWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (currWeek == 0) {
            currWeek = 7;
        }
        paramMap.put("currWeek", currWeek);
        paramMap.put("forumId", id);
        BcForum bcForum = bcForumService.getById(id);
        if (bcForum != null) {
            //获取关联主播
            List<BcAnchor> anchorByForumId = bcForumService.findAnchorByForumId(bcForum.getId());
            anchorByForumId.removeIf(Objects::isNull);
            bcForum.setBcAnchorList(anchorByForumId);
            bcForum.setIsThisAnchor(Integer.valueOf(ZijinConstants.NO));
            if (!anchorByForumId.isEmpty()) {
                for (BcAnchor bcAnchor : anchorByForumId) {
                    if (bcAnchor.getUserId().equals(uid)) {
                        bcForum.setIsThisAnchor(Integer.valueOf(ZijinConstants.YES));
                        break;
                    }
                }
            }
            bcForum.setCurrDate((int) (date.getTime() / 1000));
            //设置收藏夹信息
            configCollect(bcForum, id, String.valueOf(uid));
            paramMap.put("fmId", bcForum.getFmId());
            paramMap.put("id", bcForum.getProgramId());
            List<BcProgramList> bcProgramList = bcProgramListService.selectOptionList(paramMap, 0, 1);
            if (bcProgramList != null && !bcProgramList.isEmpty()) {
                //表示当前时间段   该讨论版存在直播节目
                bcForum.setIsLive(Integer.valueOf(ZijinConstants.YES));
                bcForum.setBcReplayListSize(bcForum.getBcReplayListSize() + 1);
            } else {
                //表示当前时间段 该讨论版不存在直播节目
                bcForum.setIsLive(Integer.valueOf(ZijinConstants.NO));
            }
        }
        return ResResult.success(bcForum);
    }

    private void configCollect(BcForum bf, String forumId, String uid) {
        Map<Object, Object> filterMap = new HashMap<>();
        filterMap.put("ctype", Integer.parseInt(ZijinConstants.FORUM_COLLECT));
        filterMap.put("siteMemberId", Integer.parseInt(uid));
        filterMap.put("collectObjectId", Integer.parseInt(forumId));
        IPage<SiteCollectDTO> siteCollectList = bcForumService.searchSiteCollect(Page.of(1, 1, false), filterMap);
        if (siteCollectList.getRecords().isEmpty()) {
            bf.setIsCollect(Integer.valueOf(ZijinConstants.NO));
        } else {
            bf.setIsCollect(Integer.valueOf(ZijinConstants.YES));
            SiteCollectDTO siteCollect = siteCollectList.getRecords().get(0);
            bf.setCollectedid(siteCollect.getSiteCollectId());
        }
    }
}
