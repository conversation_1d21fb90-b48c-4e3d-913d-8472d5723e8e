package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.api.support.ResUtil;
import com.zainanjing.zijin.domain.BcProgramLive;
import com.zainanjing.zijin.domain.BcProgramLiveType;
import com.zainanjing.zijin.service.IBcProgramLiveService;
import com.zainanjing.zijin.service.IBcProgramLiveTypeService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播相关接口
 */
@RestController
@RequestMapping("/api-bc/live")
public class LiveController {

    @Resource
    private IBcProgramLiveTypeService bcProgramLiveTypeService;

    @Resource
    private IBcProgramLiveService bcProgramLiveService;

    /**
     * 获取直播分类列表
     */
    @Anonymous
    @GetMapping("/getTypeList")
    public ResResult getTypeList() {
        List<BcProgramLiveType> list = bcProgramLiveTypeService.lambdaQuery()
                .eq(BcProgramLiveType::getStatus, 0)
                .eq(BcProgramLiveType::getIsShow, 1)
                .eq(BcProgramLiveType::getIsRec, 1)
                .orderByDesc(BcProgramLiveType::getSort)
                .list();
        return ResResult.success(BcApiConverter.convertProgramLiveTypes(list));
    }

    /**
     * 获取直播列表
     */
    @Anonymous
    @GetMapping("/getList")
    public String getList(
            @RequestParam(value = "playType", required = false) String playType,
            @RequestParam(value = "typeId", required = false) Integer typeId,
            @RequestParam(value = "channelId", required = false) Integer channelId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Long now = System.currentTimeMillis() / 1000;
        List<BcProgramLive> list = bcProgramLiveService.lambdaQuery()
                .eq(BcProgramLive::getIsLive, 1)
                .eq(BcProgramLive::getIsShow, 1)
                .eq(BcProgramLive::getStatus, 0)
                .eq(typeId != null, BcProgramLive::getTypeId, typeId)
                .eq(channelId != null, BcProgramLive::getChannelId, channelId)
                .ge(StrUtil.equals(playType, "preview"), BcProgramLive::getStartTime, now)
                .le(StrUtil.equals(playType, "living"), BcProgramLive::getStartTime, now)
                .ge(StrUtil.equals(playType, "living"), BcProgramLive::getEndTime, now)
                .le(StrUtil.equals(playType, "review"), BcProgramLive::getEndTime, now)
                .orderByDesc(BcProgramLive::getLiveOrder)
                .orderByDesc(BcProgramLive::getId)
                .list();
        return ResUtil.toRes(ResResult.success(list));
    }

    /**
     * 获取单个直播信息
     */
    @Anonymous
    @GetMapping("/getOneInfo")
    public String getOneInfo(@RequestParam("id") Integer id) {
        return ResUtil.toRes(ResResult.success(bcProgramLiveService.getById(id)));
    }

    /**
     * 更新观看人数
     */
    @Anonymous
    @RequestMapping(value = "/updateViews", method = {RequestMethod.GET,RequestMethod.POST})
    public ResResult updateViews(@RequestParam("id") Integer id) {
        bcProgramLiveService.lambdaUpdate().setIncrBy(BcProgramLive::getActualViewTimes, 1).eq(BcProgramLive::getId, id).update();
        return ResResult.success();
    }

//    /**
//     * 腾讯云直播结束回调 TODO 腾讯回调地址需要对接处理
//     */
//    @Anonymous
//    @PostMapping("/tencentLiveCallback")
//    public ResResult tencentLiveCallback(
//            @RequestParam("streamId") String streamId,
//            @RequestParam("videoUrl") String videoUrl) {
//        try {
//            liveService.tencentLiveCallback(streamId, videoUrl);
//            return ResResult.success();
//        } catch (Exception e) {
//            return ResResult.error(e.getMessage());
//        }
//    }
}