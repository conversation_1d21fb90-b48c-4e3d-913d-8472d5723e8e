package com.zainanjing.zijin.api.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 前台首页菜单管理
 *
 * <AUTHOR>
 * @date 2020/12/8
 */
@RestController
@RequestMapping("/api-bc/system")
public class SystemController {

    /**
     * 获取导航栏接口 TODO 暂不需要
     * @return 导航栏列表
     */
//    @GetMapping("/getLiveCatList")
//    public ResResult getLiveCatList() {
//        try {
//            Map<String, Object> params = new HashMap<>();
//            params.put("field", "sort");
//            params.put("direction", "Desc");
//            params.put("is_show", 1);
//            params.put("status", 1);
//
//            Map<String, Object> result = livecatService.getLivecatList(params);
//            return ResultUtil.success(result.get("list"));
//        } catch (Exception e) {
//            return ResultUtil.error(e.getMessage());
//        }
//    }
}