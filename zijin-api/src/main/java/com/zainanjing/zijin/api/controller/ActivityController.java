package com.zainanjing.zijin.api.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.zijin.api.dto.BcActivityResDTO;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcActivity;
import com.zainanjing.zijin.service.IBcActivityService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 活动相关接口
 */
@RestController
@RequestMapping({"/api-bc/Activity","/api-bc/activity"})
public class ActivityController {

    @Resource
    private IBcActivityService activityService;

    /**
     * 获取爆品列表
     */
    @Anonymous
    @GetMapping("/getActivityList")
    public ResResult getActivityList(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<BcActivityResDTO> resPage = activityService.lambdaQuery().eq(BcActivity::getIsDel, 0).orderByDesc(BcActivity::getId)
                .page(PageDTO.of(page, pageSize)).convert(bcActivity -> BcApiConverter.convert(bcActivity));
        return ResResult.success(resPage.getRecords());
    }

    /**
     * 活动信息统计
     */
    @Anonymous
    @PostMapping("/activityCount")
    public ResResult activityCount(@RequestParam("activityId") Integer activityId) {
        boolean result = activityService.lambdaUpdate().eq(BcActivity::getId, activityId).setIncrBy(BcActivity::getUserNum, 1).update();
        return result ? ResResult.success() : ResResult.error();
    }
}