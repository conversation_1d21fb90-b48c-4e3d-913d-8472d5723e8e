package com.zainanjing.zijin.api.converter;

import com.ruoyi.common.utils.DateUtil;
import com.ruoyi.common.utils.NumberUtil;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.zijin.api.dto.*;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.dto.BcGoodsDTO;

import java.util.ArrayList;
import java.util.List;

public class BcApiConverter {
    public static BcActivityResDTO convert(BcActivity bcActivity) {
        return new BcActivityResDTO(bcActivity);
    }

    public static BcAnchorResDTO convert(BcAnchor bcAnchor) {
        return BcAnchorResDTO.builder()
                .id(bcAnchor.getId())
                .createTime(bcAnchor.getCreateTime())
                .updateTime(bcAnchor.getUpdateTime())
                .channelId(bcAnchor.getChannelId())
                .userId(bcAnchor.getUserId())
                .bcId(bcAnchor.getBcId())
                .anchorName(bcAnchor.getAnchorName())
                .isTalk(bcAnchor.getIsTalk())
                .isAudit(bcAnchor.getIsAudit())
                .anchorDesc(bcAnchor.getAnchorDesc())
                .anchorImgUrl(bcAnchor.getAnchorImgUrl())
                .shareImgUrl(bcAnchor.getShareImgUrl())
                .fileDuration(bcAnchor.getFileDuration())
                .isShow(bcAnchor.getIsShow())
                .listSortNo(bcAnchor.getListSortNo())
                .isRecMain(bcAnchor.getIsRecMain())
                .mainSortNo(bcAnchor.getMainSortNo())
                .topSortType(bcAnchor.getTopSortType())
                .topSortNo(bcAnchor.getTopSortNo())
                .fansNum(bcAnchor.getFansNum())
                .build();
    }

    public static BcAnchorRadioRecommendResDTO convertAnchorRadioRecommend(BcAnchor bcAnchor) {
        return BcAnchorRadioRecommendResDTO.builder()
                .anchorId(bcAnchor.getId())
                .anchorName(bcAnchor.getAnchorName())
                .anchorLogoUrl(bcAnchor.getAnchorImgUrl())
                .build();
    }

    public static BcGoodsResDTO convert(BcGoodsDTO bcGoods) {
        if (bcGoods == null) {
            return null;
        }
        return BcGoodsResDTO.builder()
                .id(StrUtil.toStringOrNull(bcGoods.getId()))
                .createTime(DateUtil.format(bcGoods.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcGoods.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .linkType(bcGoods.getLinkType())
                .linkModule(bcGoods.getLinkModule())
                .linkTo(bcGoods.getLinkTo())
                .goodsResId(bcGoods.getGoodsResId())
                .imgLink(bcGoods.getImgLink())
                .showPosition(bcGoods.getShowPosition())
                .forumId(StrUtil.toStringOrNull(bcGoods.getForumId()))
                .anchorId(StrUtil.toStringOrNull(bcGoods.getAnchorId()))
                .isRecommandAnchor(bcGoods.getIsRecommandAnchor())
                .isShow(bcGoods.getIsShow())
                .isExplain(StrUtil.toStringOrNull(bcGoods.getIsExplain()))
                .sortNo(StrUtil.toStringOrNull(bcGoods.getSortNo()))
                .isDel(StrUtil.toStringOrNull(bcGoods.getIsDel()))
                .goodsId(bcGoods.getGoodsId())
                .goodsDesc(bcGoods.getGoodsDesc())
                .goodsName(bcGoods.getGoodsName())
                .goodsSellPrice(bcGoods.getGoodsSellPrice())
                .goodsPrice(bcGoods.getGoodsPrice())
                .goodsImgUrl(bcGoods.getGoodsImgUrl())
                .orderCnt(bcGoods.getOrderCnt())
                .build();
    }

    public static BcAnchorNewsResDTO convert(BcAnchorNews bcAnchorNews) {
        return BcAnchorNewsResDTO.builder()
                .id(StrUtil.toStringOrNull(bcAnchorNews.getId()))
                .createTime(DateUtil.format(bcAnchorNews.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcAnchorNews.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .isDel(StrUtil.toStringOrNull(bcAnchorNews.getIsDel()))
                .anchorId(StrUtil.toStringOrNull(bcAnchorNews.getAnchorId()))
                .newsContent(bcAnchorNews.getNewsContent())
                .coverFileUrl(bcAnchorNews.getCoverFileUrl())
                .fileUrl(bcAnchorNews.getFileUrl())
                .fileDuration(StrUtil.toStringOrNull(bcAnchorNews.getFileDuration()))
                .visibleType(StrUtil.toStringOrNull(bcAnchorNews.getVisibleType()))
                .soureType(StrUtil.toStringOrNull(bcAnchorNews.getSoureType()))
                .compNum(StrUtil.toStringOrNull(bcAnchorNews.getCompNum()))
                .isShow(StrUtil.toStringOrNull(bcAnchorNews.getIsShow()))
                .anchorName(bcAnchorNews.getAnchorName())
                .anchorImgUrl(bcAnchorNews.getAnchorImgUrl())
                .avatar(OSSUtil.getImageURL(bcAnchorNews.getAnchorImgUrl()))
                .newsCount(NumberUtil.parseInt(bcAnchorNews.getConmmentNumb()))
                .build();
    }

    public static List<BcAnchorNewsResDTO> convertNews(List<BcAnchorNews> bcAnchorNews) {
        if (bcAnchorNews == null) {
            return null;
        } else {
            List<BcAnchorNewsResDTO> list = new ArrayList(bcAnchorNews.size());
            for (BcAnchorNews bcAnchorNew : bcAnchorNews) {
                list.add(convert(bcAnchorNew));
            }
            return list;
        }
    }

    public static BcCommentImageResDTO convert(BcCommentImage bcCommentImage) {
        return BcCommentImageResDTO.builder()
                .id(StrUtil.toStringOrNull(bcCommentImage.getId()))
                .refId(StrUtil.toStringOrNull(bcCommentImage.getRefId()))
                .refType(StrUtil.toStringOrNull(bcCommentImage.getRefType()))
                .imgUrl(bcCommentImage.getImgUrl())
                .imgWidth(StrUtil.toStringOrNull(bcCommentImage.getImgWidth()))
                .imgHeight(StrUtil.toStringOrNull(bcCommentImage.getImgHeight()))
                .createTime(DateUtil.format(bcCommentImage.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcCommentImage.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .build();
    }

    public static BcAnchorNewsReplyResDTO convert(BcAnchorNewsReply bcAnchorNewsReply) {
        return BcAnchorNewsReplyResDTO.builder()
                .id(StrUtil.toStringOrNull(bcAnchorNewsReply.getId()))
                .createTime(DateUtil.format(bcAnchorNewsReply.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcAnchorNewsReply.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .isDel(StrUtil.toStringOrNull(bcAnchorNewsReply.getIsDel()))
                .parentEntityId(StrUtil.toStringOrNull(bcAnchorNewsReply.getParentEntityId()))
                .newsId(StrUtil.toStringOrNull(bcAnchorNewsReply.getNewsId()))
                .replyContent(bcAnchorNewsReply.getReplyContent())
                .replyTime(DateUtil.format(bcAnchorNewsReply.getReplyTime(), "yyyy-MM-dd HH:mm:ss"))
                .userId(StrUtil.toStringOrNull(bcAnchorNewsReply.getUserId()))
                .fromUserName(bcAnchorNewsReply.getFromUserName())
                .toUserId(StrUtil.toStringOrNull(bcAnchorNewsReply.getToUserId()))
                .toUserName(bcAnchorNewsReply.getToUserName())
                .isAnonymous(StrUtil.toStringOrNull(bcAnchorNewsReply.getIsAnonymous()))
                .isShow(StrUtil.toStringOrNull(bcAnchorNewsReply.getIsShow()))
                .isAudit(StrUtil.toStringOrNull(bcAnchorNewsReply.getIsAudit()))
                .auditTime(DateUtil.format(bcAnchorNewsReply.getAuditTime(), "yyyy-MM-dd HH:mm:ss"))
                .replyType(StrUtil.toStringOrNull(bcAnchorNewsReply.getReplyType()))
                .remark(bcAnchorNewsReply.getRemark())
                .isAnonymousByTo(StrUtil.toStringOrNull(bcAnchorNewsReply.getIsAnonymousByTo()))
                .isRead(StrUtil.toStringOrNull(bcAnchorNewsReply.getIsRead()))
                .build();
    }

    public static BcChoiceAlbumResDTO convert(BcChoiceAlbum bcChoiceAlbum) {
        return BcChoiceAlbumResDTO.builder()
                .id(StrUtil.toStringOrNull(bcChoiceAlbum.getId()))
                .createTime(DateUtil.format(bcChoiceAlbum.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcChoiceAlbum.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .categoryId(StrUtil.toStringOrNull(bcChoiceAlbum.getCategoryId()))
                .name(bcChoiceAlbum.getName())
                .logo(bcChoiceAlbum.getLogo())
                .anchor(bcChoiceAlbum.getAnchor())
                .produce(bcChoiceAlbum.getProduce())
                .description(bcChoiceAlbum.getDescription())
                .descImgUrl(bcChoiceAlbum.getDescImgUrl())
                .isRefAnchor(StrUtil.toStringOrNull(bcChoiceAlbum.getIsRefAnchor()))
                .anchorUid(StrUtil.toStringOrNull(bcChoiceAlbum.getAnchorUid()))
                .view(bcChoiceAlbum.getView())
                .virtualView(StrUtil.toStringOrNull(bcChoiceAlbum.getVirtualView()))
                .isHot(StrUtil.toStringOrNull(bcChoiceAlbum.getIsHot()))
                .isJingxuan(StrUtil.toStringOrNull(bcChoiceAlbum.getIsJingxuan()))
                .isShow(StrUtil.toStringOrNull(bcChoiceAlbum.getIsShow()))
                .sort(StrUtil.toStringOrNull(bcChoiceAlbum.getSort()))
                .shareIcon(bcChoiceAlbum.getShareIcon())
                .shareDesc(bcChoiceAlbum.getShareDesc())
                .shareTitle(bcChoiceAlbum.getShareTitle())
                .type(StrUtil.toStringOrNull(bcChoiceAlbum.getType()))
                .status(StrUtil.toStringOrNull(bcChoiceAlbum.getStatus()))
                .build();
    }

    public static BcAnchorImageResDTO convert(BcAnchorImage bcAnchorImage) {
        return BcAnchorImageResDTO.builder()
                .id(StrUtil.toStringOrNull(bcAnchorImage.getId()))
                .createTime(DateUtil.format(bcAnchorImage.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcAnchorImage.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .anchorId(StrUtil.toStringOrNull(bcAnchorImage.getAnchorId()))
                .imgName(bcAnchorImage.getImgName())
                .imgUrl(bcAnchorImage.getImgUrl())
                .isShow(StrUtil.toStringOrNull(bcAnchorImage.getIsShow()))
                .sortNo(StrUtil.toStringOrNull(bcAnchorImage.getSortNo()))
                .build();
    }

    public static BcChoiceResDTO convert(BcChoice bcChoice) {
        return BcChoiceResDTO.builder()
                .id(StrUtil.toStringOrNull(bcChoice.getId()))
                .createTime(DateUtil.format(bcChoice.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .albumId(StrUtil.toStringOrNull(bcChoice.getAlbumId()))
                .name(bcChoice.getName())
                .description(bcChoice.getDescription())
                .logo(bcChoice.getLogo())
                .url(bcChoice.getUrl())
                .view(StrUtil.toStringOrNull(bcChoice.getView()))
                .virtualView(StrUtil.toStringOrNull(bcChoice.getVirtualView()))
                .duration(StrUtil.toStringOrNull(bcChoice.getDuration()))
                .isShow(StrUtil.toStringOrNull(bcChoice.getIsShow()))
                .sort(StrUtil.toStringOrNull(bcChoice.getSort()))
                .shareIcon(bcChoice.getShareIcon())
                .shareTitle(bcChoice.getShareTitle())
                .shareDesc(bcChoice.getShareDesc())
                .type(StrUtil.toStringOrNull(bcChoice.getType()))
                .status(StrUtil.toStringOrNull(bcChoice.getStatus()))
                .totalView(StrUtil.toStringOrNull(bcChoice.getView() + bcChoice.getVirtualView()))
                .seconds(bcChoice.getSeconds())
                .build();
    }

    public static BcChoiceCommentResDTO convert(BcChoiceComment bcChoiceComment) {
        return BcChoiceCommentResDTO.builder()
                .id(StrUtil.toStringOrNull(bcChoiceComment.getId()))
                .createTime(DateUtil.format(bcChoiceComment.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .parentId(StrUtil.toStringOrNull(bcChoiceComment.getParentId()))
                .albumId(StrUtil.toStringOrNull(bcChoiceComment.getAlbumId()))
                .choiceId(StrUtil.toStringOrNull(bcChoiceComment.getChoiceId()))
                .userId(StrUtil.toStringOrNull(bcChoiceComment.getUserId()))
                .content(bcChoiceComment.getContent())
                .isAnonymous(StrUtil.toStringOrNull(bcChoiceComment.getIsAnonymous()))
                .isShow(StrUtil.toStringOrNull(bcChoiceComment.getIsShow()))
                .status(StrUtil.toStringOrNull(bcChoiceComment.getStatus()))
                .remark(bcChoiceComment.getRemark())
                .userName(bcChoiceComment.getUserName())
                .build();
    }

    public static BcChoiceCommentImgResDTO convert(BcChoiceCommentImg bcChoiceCommentImg) {
        return BcChoiceCommentImgResDTO.builder()
                .id(bcChoiceCommentImg.getId())
                .commentId(bcChoiceCommentImg.getCommentId())
                .imgUrl(bcChoiceCommentImg.getImgUrl())
                .width(bcChoiceCommentImg.getWidth())
                .height(bcChoiceCommentImg.getHeight())
                .build();
    }

//    public static BcForumResDTO convert(BcForum bcForum) {
//        return BcForumResDTO.builder()
//                .id(bcForum.getId())
//                .createTime(DateUtil.format(bcForum.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
//                .updateTime(DateUtil.format(bcForum.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
//                .fmId(bcForum.getFmId())
//                .programId(bcForum.getProgramId())
//                .code(bcForum.getCode())
//                .name(bcForum.getName())
//                .logo(bcForum.getLogo())
//                .isRec(bcForum.getIsRec())
//                .description(bcForum.getDescription())
//                .isShow(bcForum.getIsShow())
//                .status(bcForum.getStatus())
//                .isComment(bcForum.getIsComment())
//                .userName(bcForum.getUserName())
//                .chineseName(bcForum.getChineseName())
//                .build();
//    }

    public static List<BcChoiceCommentImgResDTO> convertChoiceCommentImages(List<BcChoiceCommentImg> bcChoiceCommentImgs) {
        if (bcChoiceCommentImgs == null) {
            return null;
        } else {
            List<BcChoiceCommentImgResDTO> list = new ArrayList(bcChoiceCommentImgs.size());
            for (BcChoiceCommentImg bcChoiceCommentImg : bcChoiceCommentImgs) {
                list.add(convert(bcChoiceCommentImg));
            }
            return list;
        }
    }

    public static List<BcChoiceResDTO> convertChoices(List<BcChoice> bcChoices) {
        if (bcChoices == null) {
            return null;
        } else {
            List<BcChoiceResDTO> list = new ArrayList(bcChoices.size());
            for (BcChoice bcChoice : bcChoices) {
                list.add(convert(bcChoice));
            }
            return list;
        }
    }

    public static List<BcAnchorImageResDTO> convertAnchorImages(List<BcAnchorImage> bcAnchorImages) {
        if (bcAnchorImages == null) {
            return null;
        } else {
            List<BcAnchorImageResDTO> list = new ArrayList(bcAnchorImages.size());
            for (BcAnchorImage bcAnchorImage : bcAnchorImages) {
                list.add(convert(bcAnchorImage));
            }
            return list;
        }
    }

    public static BcAnchorVideoResDTO convert(BcAnchorVideo bcAnchorVideo) {
        return BcAnchorVideoResDTO.builder()
                .id(StrUtil.toStringOrNull(bcAnchorVideo.getId()))
                .createTime(DateUtil.format(bcAnchorVideo.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcAnchorVideo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .anchorId(StrUtil.toStringOrNull(bcAnchorVideo.getAnchorId()))
                .videoName(bcAnchorVideo.getVideoName())
                .videoDesc(bcAnchorVideo.getVideoDesc())
                .videoDuration(StrUtil.toStringOrNull(bcAnchorVideo.getVideoDuration()))
                .playUrl(bcAnchorVideo.getPlayUrl())
                .coverUrl(bcAnchorVideo.getCoverUrl())
                .shareImgUrl(bcAnchorVideo.getShareImgUrl())
                .isShow(StrUtil.toStringOrNull(bcAnchorVideo.getIsShow()))
                .sortNo(StrUtil.toStringOrNull(bcAnchorVideo.getSortNo()))
                .build();
    }

    public static BcProgramLiveTypeResDTO convert(BcProgramLiveType bcProgramLiveType) {
        return BcProgramLiveTypeResDTO.builder()
                .id(StrUtil.toStringOrNull(bcProgramLiveType.getId()))
                .createTime(DateUtil.format(bcProgramLiveType.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .updateTime(DateUtil.format(bcProgramLiveType.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"))
                .name(bcProgramLiveType.getName())
                .shortName(bcProgramLiveType.getShortName())
                .channel(bcProgramLiveType.getChannel())
                .description(bcProgramLiveType.getDescription())
                .logo(bcProgramLiveType.getLogo())
                .isRec(StrUtil.toStringOrNull(bcProgramLiveType.getIsRec()))
                .isShow(StrUtil.toStringOrNull(bcProgramLiveType.getIsShow()))
                .sort(StrUtil.toStringOrNull(bcProgramLiveType.getSort()))
                .status(StrUtil.toStringOrNull(bcProgramLiveType.getStatus()))
                .build();
    }

    public static BcNavigationConfigResDTO convert(BcNavigationConfig bcNavigationConfig) {
        return BcNavigationConfigResDTO.builder()
                .id(bcNavigationConfig.getId())
                .enName(bcNavigationConfig.getEnName())
                .cnName(bcNavigationConfig.getCnName())
                .build();
    }

    public static BcTopicResDTO convert(BcTopic bcTopic) {
        return BcTopicResDTO.builder()
                .id(bcTopic.getId())
                .createTime(DateUtil.format(bcTopic.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .name(bcTopic.getName())
                .description(bcTopic.getDescription())
                .coverThumb(bcTopic.getCoverThumb())
                .view(bcTopic.getView())
                .forumId(bcTopic.getForumId())
                .fmId(bcTopic.getFmId())
                .programId(bcTopic.getProgramId())
                .topicDate(DateUtil.format(bcTopic.getTopicDate(), "yyyy-MM-dd"))
                .replyNum(bcTopic.getReplyNum())
                .forumName(bcTopic.getForumName())
                .isComment(bcTopic.getIsComment())
                .content(bcTopic.getContent())
                .build();
    }

    public static List<BcNavigationConfigResDTO> convertNavigationConfigs(List<BcNavigationConfig> bcNavigationConfigs) {
        if (bcNavigationConfigs == null) {
            return null;
        } else {
            List<BcNavigationConfigResDTO> list = new ArrayList(bcNavigationConfigs.size());
            for (BcNavigationConfig bcNavigationConfig : bcNavigationConfigs) {
                list.add(convert(bcNavigationConfig));
            }
            return list;
        }
    }

    public static List<BcProgramLiveTypeResDTO> convertProgramLiveTypes(List<BcProgramLiveType> bcProgramLiveTypes) {
        if (bcProgramLiveTypes == null) {
            return null;
        } else {
            List<BcProgramLiveTypeResDTO> list = new ArrayList(bcProgramLiveTypes.size());
            for (BcProgramLiveType bcProgramLiveType : bcProgramLiveTypes) {
                list.add(convert(bcProgramLiveType));
            }
            return list;
        }
    }

    public static List<BcAnchorVideoResDTO> convertAnchorVideos(List<BcAnchorVideo> bcAnchorVideos) {
        if (bcAnchorVideos == null) {
            return null;
        } else {
            List<BcAnchorVideoResDTO> list = new ArrayList(bcAnchorVideos.size());
            for (BcAnchorVideo bcAnchorVideo : bcAnchorVideos) {
                list.add(convert(bcAnchorVideo));
            }
            return list;
        }
    }


    public static List<BcChoiceAlbumResDTO> convertChoiceAlbums(List<BcChoiceAlbum> bcChoiceAlbums) {
        if (bcChoiceAlbums == null) {
            return null;
        } else {
            List<BcChoiceAlbumResDTO> list = new ArrayList(bcChoiceAlbums.size());
            for (BcChoiceAlbum bcChoiceAlbum : bcChoiceAlbums) {
                list.add(convert(bcChoiceAlbum));
            }
            return list;
        }
    }

    public static List<BcCommentImageResDTO> convertImages(List<BcCommentImage> bcCommentImages) {
        if (bcCommentImages == null) {
            return null;
        } else {
            List<BcCommentImageResDTO> list = new ArrayList(bcCommentImages.size());
            for (BcCommentImage bcCommentImage : bcCommentImages) {
                list.add(convert(bcCommentImage));
            }
            return list;
        }
    }

    public static List<BcGoodsResDTO> convertGoods(List<BcGoodsDTO> bcGoods) {
        if (bcGoods == null) {
            return null;
        } else {
            List<BcGoodsResDTO> list = new ArrayList(bcGoods.size());
            for (BcGoodsDTO bcGood : bcGoods) {
                list.add(convert(bcGood));
            }
            return list;
        }
    }

    public static List<BcAnchorResDTO> convertAnchors(List<BcAnchor> bcAnchors) {
        if (bcAnchors == null) {
            return null;
        } else {
            List<BcAnchorResDTO> list = new ArrayList(bcAnchors.size());
            for (BcAnchor bcAnchor : bcAnchors) {
                list.add(convert(bcAnchor));
            }
            return list;
        }
    }

    public static BcAnchorResDTO.Program convert(BcProgram bcProgram) {
        if (bcProgram == null) {
            return null;
        } else {
            return BcAnchorResDTO.Program.builder()
                    .id(bcProgram.getId())
                    .createTime(bcProgram.getCreateTime())
                    .updateTime(bcProgram.getUpdateTime())
                    .name(bcProgram.getName())
                    .isShow(bcProgram.getIsShow())
                    .status(bcProgram.getStatus())
                    .build();
        }
    }

    public static List<BcAnchorResDTO.Program> convert(List<BcProgram> bcPrograms) {
        if (bcPrograms == null) {
            return null;
        } else {
            List<BcAnchorResDTO.Program> list = new ArrayList(bcPrograms.size());
            for (BcProgram bcProgram : bcPrograms) {
                list.add(convert(bcProgram));
            }
            return list;
        }
    }

}