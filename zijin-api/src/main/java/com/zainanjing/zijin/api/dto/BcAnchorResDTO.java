package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcAnchorResDTO {
    private Long id;
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    @JsonProperty("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    @JsonProperty("channel_id")
    private Long channelId;
    @JsonProperty("user_id")
    private Long userId;
    @JsonProperty("bc_id")
    private Long bcId;
    @JsonProperty("anchor_name")
    private String anchorName;
    @JsonProperty("is_talk")
    private Integer isTalk;
    @JsonProperty("is_audit")
    private Integer isAudit;
    @JsonProperty("anchor_desc")
    private String anchorDesc;
    @JsonProperty("anchor_img_url")
    private String anchorImgUrl;
    @JsonProperty("share_img_url")
    private String shareImgUrl;
    @JsonProperty("file_duration")
    private Long fileDuration;
    @JsonProperty("is_show")
    private Integer isShow;
    @JsonProperty("list_sort_no")
    private Long listSortNo;
    @JsonProperty("is_rec_main")
    private Integer isRecMain;
    @JsonProperty("main_sort_no")
    private Long mainSortNo;
    @JsonProperty("top_sort_type")
    private Integer topSortType;
    @JsonProperty("top_sort_no")
    private Long topSortNo;
    @JsonProperty("fans_num")
    private Integer fansNum;
    @JsonProperty("fm_name")
    private String fmName;
    @JsonProperty(value = "level")
    private Integer level;
    @JsonProperty(value = "program_list")
    private List<Program> programList;
    @JsonProperty(value = "avatar")
    private String avatar;
    @JsonProperty(value = "is_focus")
    private Integer isFocus;


    public String getAvatar() {
        return avatar == null ? "" : avatar;
    }

    public Integer getIsFocus() {
        return isFocus == null ? 0 : isFocus;
    }

    public List<Program> getProgramList() {
        return programList == null ? List.of() : programList;
    }

    @Data
    @SuperBuilder
    public static class Program {
        private Long id;
        @JsonProperty("create_time")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty("update_time")
        private Date updateTime;
        private String name;
        @JsonProperty("is_show")
        private Integer isShow;
        private Integer status;
    }
}
