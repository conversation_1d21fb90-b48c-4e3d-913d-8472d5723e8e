package com.zainanjing.zijin.api.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.dto.BcGoodsDTO;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.service.IBcGoodsService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 商品相关接口
 */
@RestController
@RequestMapping("/api-bc/goods")
public class GoodsController {

    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcGoodsService bcGoodsService;

    /**
     * 获取商品列表
     */
    @Anonymous
    @GetMapping("/getGoodsList")
    public ResResult getGoodsList(
            @RequestParam(value = "goodsType", defaultValue = "0") Integer goodsType,
            @RequestParam(value = "entityId", required = true) Integer entityId,
            @RequestParam(value = "bcPlayListId", required = false) Integer bcPlayListId,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        // bcPlayListId 原接口参数无用
        IPage<BcGoodsDTO> bcGoodsList = bcGoodsService.findBcGoodsList(Page.of(page, pageSize, false),
                Map.of("goodsType", goodsType,
                        "entityId", entityId,
                        "isShow", "Y",
                        "isDel", 0));

        return ResResult.success(BcApiConverter.convertGoods(bcGoodsList.getRecords()));
    }
}