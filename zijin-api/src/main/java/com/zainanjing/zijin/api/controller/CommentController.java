package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.AsyncManager;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.mongo.MangoSqMessageService;
import com.zainanjing.convenience.support.mongo.SqMessage;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.BcComment;
import com.zainanjing.zijin.domain.BcPost;
import com.zainanjing.zijin.domain.BcZiJinImg;
import com.zainanjing.zijin.dto.GdmmUsersDTO;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.service.IBcCommentService;
import com.zainanjing.zijin.service.IBcPostService;
import com.zainanjing.zijin.service.IBcZiJinImgService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@RestController
@RequestMapping("/api-bc/bcComment")
public class CommentController {
    @Autowired
    private IBcCommentService bcCommentService;
    @Autowired
    private IBcPostService bcPostService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcZiJinImgService bcZiJinImgService;

    @Resource
    private MangoSqMessageService mangoSqMessageService;

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;

    @Anonymous
    @GetMapping("/list")
    public ResResult list(@RequestParam(value = "postId") String postId, @RequestParam(value = "type") String type,
                          @RequestParam(value = "commentId", required = false) String commentId,
                          @RequestParam(value = "currentPage", required = false, defaultValue = "1") String currentPage,
                          @RequestParam(value = "pageSize", required = false, defaultValue = "10") String pageSize) {

        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        BcPost bcPost = bcPostService.getById(postId);
        if (bcPost == null) {
            throw new ServiceException("编号为:" + postId + "的帖子不存在");
        }
        HashMap<String, Object> filterMap = new HashMap<String, Object>();
        filterMap.put("postId", postId);
        //记录当前用户来查询屏蔽用户
        filterMap.put("sessionUid", uid);
        filterMap.put("verifyUid", uid);
        if (Integer.valueOf(ZijinConstants.CONSTANTS_1).equals(bcPost.getSeeLevel())) {
            filterMap.put("uid", uid);
        }
        List<BcComment> bcCommentList = bcCommentService.searchByConditions(filterMap, type, commentId, Integer.valueOf(currentPage), Integer.valueOf(pageSize));
        return ResResult.success(bcCommentList);
    }

    @PostMapping("/save")
    public ResResult save(HttpServletRequest request, @RequestParam(value = "postId") String postId,
                          @RequestParam(value = "content", required = false) String content,
                          @RequestParam(value = "type") String type,
                          @RequestParam(value = "commentId", required = false) String commentId,
                          @RequestParam(value = "bcImgIds", required = false) String bcImgIds,
                          @RequestParam(value = "audioSecond", required = false) String audioSecond,
                          @RequestParam(value = "isAnonymous", required = false) String isAnonymous) {

        wangyiCheckUtil.checkText(content);

        Long uid = SecurityUtils.getUserId();
        BcComment bcComment = new BcComment();
        String ip = IpUtils.getIpAddr(request);
        bcComment.setIp(ip);
        bcComment.setRegion(AddressUtils.getRealAddressByIP(ip));
        if (StrUtil.isEmpty(audioSecond)) {
            audioSecond = "0";
        }
        Map<String, String> audioMap = bcCommentService.findAudioUrlByBcImgId(bcImgIds);
        if (StrUtil.isEmpty(audioMap.get("audioUrl"))) {
            if (StrUtil.isEmpty(content)) {
                throw new ServiceException("发送内容不能为空");
            }
        }
        bcComment.setUid(uid);
        bcComment.setPostId(Long.valueOf(postId));
        bcComment.setContent(content);
        bcComment.setType(Integer.valueOf(type));
        bcComment.setLevel(Integer.valueOf(type));
        bcComment.setCreateTime(CommonUtil.getTimestamp());
        bcComment.setUpdateTime(CommonUtil.getTimestamp());
        bcComment.setStatus(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        bcComment.setAudioSecond(Integer.parseInt(audioSecond));
        //评论 插入版块id
        BcPost bcPost = bcPostService.getById(postId);
        bcCommentService.saveOnAudit(bcComment, bcPost);

        bcComment.setForumId(bcPost.getForumId());
        if (StrUtil.isNotEmpty(commentId)) {
            bcComment.setParentId(Long.valueOf(commentId));
        } else {
            bcComment.setParentId(0L);
        }
        if (StrUtil.isNotEmpty(isAnonymous)) {
            bcComment.setIsAnonymous(Integer.valueOf(isAnonymous));
        } else {
            bcComment.setIsAnonymous(0);
        }
        Long id = bcCommentService.saveCommentAndImgs(bcComment, bcImgIds);
        //客户端用于不刷新 展示
        bcComment.setCommentId(id);
        bcCommentService.searchImgUrlsAndUserHead(id, uid, bcComment);
        //更新是否有最新评论为 1:是
        bcCommentService.updateHasNewComment(type, postId, commentId, Integer.valueOf(ZijinConstants.CONSTANTS_1));

        //消息列表。
        AsyncManager.me().execute(() -> {
            //发系统消息给帖子作者
            SqMessage sqMessage = null;
            String imgUrl = "";
            //消息里显示的 对方uid 对方昵称  对方头像
            Long otherUid = SecurityUtils.getUserId();
            String otherName = "";
            String otherImgUrl = "";
            List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uid", otherUid));
            if (CollUtil.isNotEmpty(gdmmUsersDTOS)) {
                bcComment.setMedalLevel(gdmmUsersDTOS.get(0).getMedalLevel());
                otherName = gdmmUsersDTOS.get(0).getUserName();
                otherImgUrl = CommonUtil.commonFindPicPath(gdmmUsersDTOS.get(0).getAvatarType(), gdmmUsersDTOS.get(0).getAvatar());
            }
            //如果有图 第一张图片
            Optional<BcZiJinImg> bcZiJinImg = bcZiJinImgService.lambdaQuery().eq(BcZiJinImg::getPostId, bcPost.getId())
                    .eq(BcZiJinImg::getType, Constants.IMG_TYPE_POST)
                    .last("limit 1").oneOpt();
            if (bcZiJinImg.isPresent()) {
                imgUrl = bcZiJinImg.get().getImgUrl();
            }

            if ("1".equals(type)) {
                sqMessage = mangoSqMessageService.initSqMessageSq("BCPOST", bcPost.getId(), bcPost.getSubject(), bcPost.getContent(),
                        BigDecimal.ZERO, imgUrl, otherUid, otherName, otherImgUrl);
                String sqContent = "评论了你的帖子" + bcPost.getSubject();
                mangoSqMessageService.saveMongodbSqMessage(sqMessage, Constants.SQMESSAGE_TYPE_SQ, Constants.SQMESSAGE_CODE_COMMENT, bcPost.getUid().longValue(), content);
            } else if ("2".equals(type)) {
                BcComment tempComment = bcCommentService.getById(commentId);
                if (tempComment != null) {
                    Long receiverUid = tempComment.getUid();
                    sqMessage = mangoSqMessageService.initSqMessageSq("BCCOMMENT", bcPost.getId(), bcPost.getSubject(), bcPost.getContent(),
                            BigDecimal.ZERO, imgUrl, otherUid, otherName, otherImgUrl);
                    String sqContent = "回复了你的评论";
                    mangoSqMessageService.saveMongodbSqMessage(sqMessage, Constants.SQMESSAGE_TYPE_SQ, Constants.SQMESSAGE_CODE_COMMENT, receiverUid, content);
                }
            }
        });
        applicationEventPublisher.publishEvent(new CommonEvent(bcComment, EventAction.builder()
                .operator(SecurityUtils.getLoginUser())
                .type(BusinessType.INSERT)
                .build()));
        return ResResult.success(bcComment);
    }

    @PostMapping("/delete")
    public ResResult delete(@RequestParam(value = "id") String id) {
        Long uid = 0L;
        if (null != SecurityUtils.getUserId()) {
            uid = SecurityUtils.getUserId();
        }
        return ResResult.success(bcCommentService.update(new UpdateWrapper<BcComment>()
                .eq("id", id).set("status", ZijinConstants.YES).set("uid", uid).set("parent_id", id)));
    }

}
