package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcFm;
import com.zainanjing.zijin.service.IBcFmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;


@RestController
@RequestMapping("/api-bc/bcFm")
public class FmController {
    @Autowired
    private IBcFmService bcFmService;

    @Anonymous
    @GetMapping("/list")
    public ResResult list(@RequestParam(value = "isRec", required = false) String isRec) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        if ("1".equals(isRec)) {
            //表示取推荐的数据
            paramMap.put("isRec", isRec);
        }
        Calendar calendar = Calendar.getInstance();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        paramMap.put("currDate", sdf.format(date));
        int currWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (currWeek == 0) {
            currWeek = 7;
        }
        paramMap.put("currWeek", currWeek + "");
        List<BcFm> bcFmList = bcFmService.findFmListByMap(paramMap);
        for (BcFm bcFm : bcFmList) {
            bcFm.setLogo(OSSUtil.getImageURL(bcFm.getLogo()));
        }
        return ResResult.success(bcFmList);
    }
}
