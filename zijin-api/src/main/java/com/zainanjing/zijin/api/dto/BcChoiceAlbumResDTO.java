package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcChoiceAlbumResDTO {

    @JsonProperty("id")
    private String id;

    @JsonProperty("create_time")
    private String createTime;

    @JsonProperty("update_time")
    private String updateTime;

    @JsonProperty("category_id")
    private String categoryId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("logo")
    private String logo;

    @JsonProperty("anchor")
    private String anchor;

    @JsonProperty("produce")
    private String produce;

    @JsonProperty("description")
    private String description;

    @JsonProperty("desc_img_url")
    private String descImgUrl;

    @JsonProperty("is_ref_anchor")
    private String isRefAnchor;

    @JsonProperty("anchor_uid")
    private String anchorUid;

    @JsonProperty("view")
    private Long view;

    @JsonProperty("nums")
    private Long nums;

    @JsonProperty("virtual_view")
    private String virtualView;

    @JsonProperty("is_hot")
    private String isHot;

    @JsonProperty("is_jingxuan")
    private String isJingxuan;

    @JsonProperty("is_show")
    private String isShow;

    @JsonProperty("sort")
    private String sort;

    @JsonProperty("share_icon")
    private String shareIcon;

    @JsonProperty("share_title")
    private String shareTitle;

    @JsonProperty("share_desc")
    private String shareDesc;

    @JsonProperty("type")
    private String type;

    @JsonProperty("status")
    private String status;

    private String totalview;

    @JsonProperty("choice_num")
    private Long choiceNum;

}
