package com.zainanjing.zijin.api.dto;

import com.ruoyi.common.utils.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zainanjing.zijin.domain.BcActivity;
import lombok.Data;

import java.util.Date;

@Data
public class BcActivityResDTO {

    private Long activityId;
    private String activityName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    private Long userNum;
    private String activityImgUrl;
    private String linkType;
    private String linkModule;
    private String resourceId;
    private String linkTo;
    private String linkUrl;

    public BcActivityResDTO() {
    }

    public BcActivityResDTO(BcActivity bcActivity) {
        this.activityId = bcActivity.getId();
        this.activityName = bcActivity.getActivityName();
        this.startTime = bcActivity.getStartDate();
        this.endTime = bcActivity.getEndDate();
        this.userNum = bcActivity.getUserNum();
        this.activityImgUrl = bcActivity.getActivityImgUrl();
        this.linkType = bcActivity.getLinkType();
        this.linkModule = bcActivity.getLinkJumpTo();
        this.resourceId = StrUtil.toStringOrNull(bcActivity.getGoodsId());
        this.linkTo = bcActivity.getLinkTo();
        this.linkUrl = bcActivity.getLinkUrl();
    }

}
