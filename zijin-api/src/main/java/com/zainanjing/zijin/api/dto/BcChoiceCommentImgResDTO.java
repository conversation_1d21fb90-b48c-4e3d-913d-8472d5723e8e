package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * 精选内容评论图片对象 bc_choice_comment_img
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcChoiceCommentImgResDTO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 评论id
     */
    @JsonProperty("comment_id")
    private Long commentId;

    /**
     * 图片地址
     */
    @JsonProperty("img_url")
    private String imgUrl;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 高度
     */
    private Integer height;

}
