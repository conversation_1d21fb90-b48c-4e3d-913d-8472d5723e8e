package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcChoiceCommentResDTO {

    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("parent_id")
    private String parentId;
    @JsonProperty("album_id")
    private String albumId;
    @JsonProperty("choice_id")
    private String choiceId;
    @JsonProperty("user_id")
    private String userId;
    private String content;
    @JsonProperty("is_anonymous")
    private String isAnonymous;
    @JsonProperty("is_show")
    private String isShow;
    private String status;
    private String remark;
    @JsonProperty("reply_num")
    private long replyNum;
    @JsonProperty("user_name")
    private String userName;
    @JsonProperty("head_img_url")
    private String headImgUrl;
    @JsonProperty("medal_level")
    private String medalLevel;
    private String avatar;
    @JsonProperty("img_list")
    private List<BcChoiceCommentImgResDTO> imgList;
    @JsonProperty("sub_comment")
    private List<BcChoiceCommentResDTO> subComment;


    public List<BcChoiceCommentImgResDTO> getImgList() {
        return imgList == null ? null : imgList;
    }

    public List<BcChoiceCommentResDTO> getSubComment() {
        return subComment == null ? null : subComment;
    }
}
