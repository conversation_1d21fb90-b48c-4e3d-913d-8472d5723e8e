//package com.zainanjing.zijin.api.dto;
//
//import com.fasterxml.jackson.annotation.JsonProperty;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import lombok.experimental.SuperBuilder;
//
//@Data
//@NoArgsConstructor
//@SuperBuilder
//public class BcForumResDTO {
//    private Long id;
//    @JsonProperty("create_time")
//    private String createTime;
//    @JsonProperty("update_time")
//    private String updateTime;
//    @JsonProperty("fm_id")
//    private Long fmId;
//    @JsonProperty("program_id")
//    private Long programId;
//    private String code;
//    private String name;
//    private String logo;
//    @JsonProperty("is_rec")
//    private Integer isRec;
//    private String description;
//    @JsonProperty("is_show")
//    private Integer isShow;
//    private Integer status;
//    @JsonProperty("is_comment")
//    private Integer isComment;
//    @JsonProperty("user_name")
//    private String userName;
//    @JsonProperty("chinese_name")
//    private String chineseName;
//
//}
