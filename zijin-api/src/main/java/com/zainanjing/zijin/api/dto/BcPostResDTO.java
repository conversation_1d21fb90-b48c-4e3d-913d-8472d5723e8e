package com.zainanjing.zijin.api.dto;

import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
public class BcPostResDTO {
    private String content;
    private String createTime;
    private String isAnonymous;
    private String ip;
    private String region;
    private String bcId;
    private String bcName;
    private String bcImgUrl;
    private String userId;
    private String userName;
    private String anchorId;
    private String anchorName;

    public String getUserId() {
        return userId == null ? "0" : userId;
    }
}
