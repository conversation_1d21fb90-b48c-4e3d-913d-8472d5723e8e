package com.zainanjing.zijin.api.support;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.ruoyi.common.exception.base.BaseException;
import com.zainanjing.convenience.support.web.ResResult;

public class ResUtil {

    private static ObjectMapper mapper = new ObjectMapper();

    static {
        // 配置空值不序列化
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //下划线
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }


    public static String toRes(Object object) {
        try {
            return mapper.writeValueAsString(ResResult.success(object));
        } catch (Exception e) {
            throw new BaseException("json序列化异常");
        }
    }
}
