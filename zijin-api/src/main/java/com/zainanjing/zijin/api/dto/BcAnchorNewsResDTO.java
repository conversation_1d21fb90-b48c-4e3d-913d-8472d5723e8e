package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
public class BcAnchorNewsResDTO {

    @JsonProperty("id")
    private String id;

    @JsonProperty("create_time")
    private String createTime;

    @JsonProperty("update_time")
    private String updateTime;

    @JsonProperty("is_del")
    private String isDel;

    @JsonProperty("anchor_id")
    private String anchorId;

    @JsonProperty("news_content")
    private String newsContent;

    @JsonProperty("cover_file_url")
    private String coverFileUrl;

    @JsonProperty("file_url")
    private String fileUrl;

    @JsonProperty("file_duration")
    private String fileDuration;

    @JsonProperty("visible_type")
    private String visibleType;

    @JsonProperty("soure_type")
    private String soureType;

    @JsonProperty("comp_num")
    private String compNum;

    @JsonProperty("is_show")
    private String isShow;

    @JsonProperty("anchor_name")
    private String anchorName;

    @JsonProperty("anchor_img_url")
    private String anchorImgUrl;

    @JsonProperty("news_count")
    private int newsCount;

    @JsonProperty("avatar")
    private String avatar;

    @JsonProperty("level")
    private int level;

    @JsonProperty("news_content_images")
    private List<BcCommentImageResDTO> newsContentImages;

    @JsonProperty("is_like")
    private String isLike;

    @JsonProperty("reply")
    private List<BcAnchorNewsReplyResDTO> reply;

    @JsonProperty("moreReply")
    private int moreReply;

    public String getIsLike() {
        return isLike == null ? "0" : isLike;
    }

    public List<BcAnchorNewsReplyResDTO> getReply() {
        return reply == null ? List.of() : reply;
    }
}
