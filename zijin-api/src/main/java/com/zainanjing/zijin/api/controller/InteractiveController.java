package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.zijin.api.dto.BcPostResDTO;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcAnchorRelation;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.domain.BcPost;
import com.zainanjing.zijin.dto.GdmmUsersDTO;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.service.IBcAnchorRelationService;
import com.zainanjing.zijin.service.IBcAnchorService;
import com.zainanjing.zijin.service.IBcForumService;
import com.zainanjing.zijin.service.IBcPostService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 互动相关接口
 */
@RestController
@RequestMapping({"/api-bc/Interactive", "/api-bc/Interactive"})
public class InteractiveController {

    @Resource
    private IBcPostService bcPostService;
    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcAnchorRelationService bcAnchorRelationService;

    @Resource
    private IBcForumService bcForumService;

    @Resource
    private IBcAnchorService bcAnchorService;

    /**
     * 获取正在互动列表
     */
    @Anonymous
    @GetMapping("/getInteractiveList")
    public ResResult getInteractiveList(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        //获取屏蔽的用户列表 TODO 暂时不做

        Page<BcPost> bcPostPage = bcPostService.lambdaQuery()
                .eq(BcPost::getStatus, 0)
                .eq(BcPost::getType, 1)
                .orderByDesc(BcPost::getId)
                .page(Page.of(page, pageSize, false));

        List<BcPostResDTO> bcPostResDTOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(bcPostPage.getRecords())) {

            List<BcForum> bcForums = bcForumService.lambdaQuery().in(BcForum::getId, bcPostPage.getRecords().stream().map(BcPost::getForumId).toList())
                    .select(BcForum::getId, BcForum::getName, BcForum::getLogo, BcForum::getFmId)
                    .list();

            List<BcAnchorRelation> bcAnchorRelations = bcAnchorRelationService.lambdaQuery().in(BcAnchorRelation::getBcId, bcForums.stream().map(BcForum::getFmId).toList())
                    .select(BcAnchorRelation::getAnchorId, BcAnchorRelation::getBcId).list();

            Map<Long, String> anchorMaps = CollUtil.isEmpty(bcAnchorRelations) ? Map.of() : bcAnchorService.lambdaQuery().in(BcAnchor::getUserId, bcAnchorRelations.stream().map(BcAnchorRelation::getAnchorId).toList()).list()
                    .stream().collect(Collectors.toMap(BcAnchor::getUserId, BcAnchor::getAnchorName));

            List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uids", bcPostPage.getRecords().stream().map(BcPost::getUid).toList()));
            Map<Long, GdmmUsersDTO> gdmmUsersMap = gdmmUsersDTOS.stream().collect(Collectors.toMap(GdmmUsersDTO::getUserId, Function.identity()));

            for (BcPost bcPost : bcPostPage.getRecords()) {

                BcPostResDTO bcPostResDTO = BcPostResDTO.builder()
                        .content(bcPost.getContent())
                        .createTime(StrUtil.toString(bcPost.getCreateTime()))
                        .isAnonymous(StrUtil.toString(bcPost.getIsAnonymous()))
                        .ip(bcPost.getIp())
                        .region(bcPost.getRegion())
                        .bcId(StrUtil.toString(bcPost.getForumId()))
                        .userId(StrUtil.toStringOrNull(bcPost.getUid()))
                        .userName(gdmmUsersMap.getOrDefault(bcPost.getUid(), new GdmmUsersDTO()).getUserName())
                        .build();
                bcForums.stream().filter(bcForum -> bcForum.getId().equals(bcPost.getForumId())).findFirst().ifPresent(bcForum -> {
                    bcPostResDTO.setBcImgUrl(bcForum.getLogo());
                    bcPostResDTO.setBcName(bcForum.getName());
                    List<BcAnchorRelation> list = bcAnchorRelations.stream().filter(bcAnchorRelation -> bcAnchorRelation.getBcId().equals(bcForum.getFmId())).toList();
                    bcPostResDTO.setAnchorId(list.stream().map(bcAnchorRelation -> StrUtil.toString(bcAnchorRelation.getAnchorId())).collect(Collectors.joining(",")));
                    bcPostResDTO.setAnchorName(list.stream().map(bcAnchorRelation -> anchorMaps.getOrDefault(bcAnchorRelation.getAnchorId(), "")).collect(Collectors.joining(",")));
                });
                bcPostResDTOList.add(bcPostResDTO);
            }
        }

        return ResResult.success(bcPostResDTOList);
    }
}