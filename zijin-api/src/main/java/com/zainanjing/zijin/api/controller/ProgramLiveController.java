package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.DateUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcProgramLive;
import com.zainanjing.zijin.domain.BcProgramLiveGoods;
import com.zainanjing.zijin.domain.BcProgramLiveType;
import com.zainanjing.zijin.mapper.BcProgramLiveMapper;
import com.zainanjing.zijin.service.IBcProgramLiveGoodsService;
import com.zainanjing.zijin.service.IBcProgramLiveTypeService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api-bc")
public class ProgramLiveController {

    @Resource
    private IBcProgramLiveTypeService bcProgramLiveTypeService;
    @Resource
    private BcProgramLiveMapper bcProgramLiveMapper;
    @Resource
    private IBcProgramLiveGoodsService godsProgramLiveGoodsService;

    /**
     * 获取直播分类列表
     *
     * @return
     */
    @Anonymous
    @RequestMapping("/bcProgramLiveType/list")
    public ResResult bcProgramLiveTypeList() {
        List<BcProgramLiveType> list = bcProgramLiveTypeService.lambdaQuery()
                .eq(BcProgramLiveType::getIsShow, 1)
                .eq(BcProgramLiveType::getStatus, 0)
                .orderByDesc(BcProgramLiveType::getSort)
                .orderByDesc(BcProgramLiveType::getId)
                .list();
        return ResResult.success(list);
    }

    /**
     * 获取直播列表
     */
    @Anonymous
    @RequestMapping("/bcProgramLive/list")
    public ResResult bcProgramLiveList(@RequestParam(value = "fmId", required = false) String fmId,
                                       @RequestParam(value = "flag", required = false) String flag,//默认不传  1表示查询startTime-endTime之间的
                                       @RequestParam(value = "id", required = false) String id,
                                       @RequestParam(value = "typeId", required = false) String typeId,
                                       @RequestParam(value = "typeChannel", required = false) String typeChannel,
                                       @RequestParam(value = "status", required = false) String status,// 1正在直播，2即将直播，3已结束，4回顾 isList为0,则是回顾，否则根据时间判断
                                       @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Map<String, Object> queryMap = new HashMap<String, Object>();
        if (StrUtil.isNotBlank(fmId)) {
            queryMap.put("fmId", fmId);
        }
        if (StrUtil.isNotBlank(flag)) {
            queryMap.put("flag", flag);
        }
        if (StrUtil.isNotBlank(id)) {
            queryMap.put("id", id);
        }
        if (StrUtil.isNotBlank(typeId)) {
            queryMap.put("typeId", typeId);
        }
        if (StrUtil.isNotBlank(status)) {
            queryMap.put("liveStatus", status);
        }
        if (StrUtil.isNotBlank(typeChannel)) {
            queryMap.put("typeChannel", typeChannel);
        }

        queryMap.put("isShow", 1);

        Integer now = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        queryMap.put("now", now);

        IPage<BcProgramLive> resultPage = bcProgramLiveMapper.search(Page.of(currentPage, pageSize, false), queryMap);
        if (resultPage.getRecords().size() > 0) {
            for (BcProgramLive bp : resultPage.getRecords()) {
                bp.setLogo(OSSUtil.getImageURL(bp.getLogo()));
                bp.setShareImg(OSSUtil.getImageURL(bp.getShareImg()));
                bp.setBanner(OSSUtil.getImageURL(bp.getBanner()));
                bp.setView(Long.valueOf(bp.getActualViewTimes() * bp.getViewTimes()).intValue());

                //修改最大点击量
                Long maxView = bp.getMaxView();
                if (bp.getView() > maxView) {
                    bp.setShowView(maxView);
                }
            }
        }
        return ResResult.success(Map.of("bcProgramLiveList", resultPage.getRecords()));
    }


    /**
     * 直播节目商品列表
     */
    @Anonymous
    @RequestMapping(value = "/bcProgramLive/goodsList", method = {RequestMethod.GET, RequestMethod.POST})
    public ResResult getGoodsList(@RequestParam(value = "id") String id,
                                  @RequestParam(value = "pageNo", required = false, defaultValue = "1") String pageNo,
                                  @RequestParam(value = "pageSize", required = false, defaultValue = "99999") String pageSize) {

        HashMap<String, Object> resultMap = new HashMap<>();
        List<BcProgramLiveGoods> bcProgramLiveGoods = godsProgramLiveGoodsService.searchGoodsList(id, pageNo, pageSize);
        resultMap.put("list", bcProgramLiveGoods);
        resultMap.put("count", bcProgramLiveGoods.size());
        return ResResult.success(resultMap);
    }
}
