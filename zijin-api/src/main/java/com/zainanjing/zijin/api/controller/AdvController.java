package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcAdv;
import com.zainanjing.zijin.service.IBcAdvService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api-bc/bcAdv")
public class AdvController {
    @Autowired
    private IBcAdvService bcAdvService;

    @Anonymous
    @GetMapping("/findAdvByType")
    public ResResult findAdvByType(@RequestParam(value = "type", required = false) String type,
                                   @RequestParam(value = "area", required = false) String area) {
        List<BcAdv> bcAdvList = bcAdvService.selectOptionalList(type, area);
        if (!CollectionUtils.isEmpty(bcAdvList)) {
            for (BcAdv ba : bcAdvList) {
                ba.setImgurl(OSSUtil.getImageURL(ba.getImgurl()));
            }
        }
        return ResResult.success(bcAdvList);
    }
}
