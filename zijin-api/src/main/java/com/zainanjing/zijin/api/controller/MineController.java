package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.zijin.api.dto.BcChoiceAlbumResDTO;
import com.zainanjing.zijin.api.dto.BcChoiceResDTO;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.api.support.ResUtil;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcChoice;
import com.zainanjing.zijin.domain.BcChoiceAlbum;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.mapper.BcMyFavoriteMapper;
import com.zainanjing.zijin.service.IBcAnchorFansService;
import com.zainanjing.zijin.service.IBcAnchorService;
import com.zainanjing.zijin.service.IBcChoiceService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 我的相关接口
 */
@RestController
@RequestMapping("/api-bc/mine")
public class MineController {

    @Resource
    private IBcAnchorFansService bcAnchorFansService;

    @Resource
    private IBcAnchorService bcAnchorService;

    @Resource
    private BcMyFavoriteMapper bcMyFavoriteMapper;

    @Resource
    private IBcChoiceService bcChoiceService;

    /**
     * 我的关注
     */
    @GetMapping("/attention")
    public ResResult attention(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<BcAnchor> bcAnchorIPage = bcAnchorFansService.anchorListByFansIdPage(Page.of(page, pageSize, false), SecurityUtils.getUserId());
        return ResResult.success(BcApiConverter.convertAnchors(bcAnchorIPage.getRecords()));
    }

    /**
     * 我的收藏
     */
    @GetMapping("/favorite")
    public Object favorite(
            @RequestParam("type") Integer type,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Long uid = SecurityUtils.getUserId();


        switch (type) {
            case 1://节目
                IPage<BcForum> bcForumIPage = bcMyFavoriteMapper.selectProgramListByUid(Page.of(page, pageSize, false), uid);
                return ResUtil.toRes(bcForumIPage.getRecords());
            case 2://专辑
                return getFavoriteAlbum(page, pageSize, uid);
            case 3://分集
                return getFavoriteChoice(page, pageSize, uid);
            default:
                return ResResult.error("参数错误");
        }
    }

    private ResResult getFavoriteChoice(Integer page, Integer pageSize, Long uid) {
        IPage<BcChoice> bcChoiceIPage = bcMyFavoriteMapper.selectAlbumEpisodeListByUid(Page.of(page, pageSize, false), uid);

        if (CollUtil.isNotEmpty(bcChoiceIPage.getRecords())) {
            List<BcChoiceResDTO> video = new ArrayList<>();
            List<BcChoiceResDTO> audio = new ArrayList<>();
            for (BcChoice bcChoice : bcChoiceIPage.getRecords()) {
                BcChoiceResDTO bcChoiceResDTO = BcApiConverter.convert(bcChoice);
                if (bcChoice.getType() == 1) {
                    audio.add(bcChoiceResDTO);
                } else if (bcChoice.getType() == 2) {
                    video.add(bcChoiceResDTO);
                }
            }
            return ResResult.success(Map.of("video", video, "audio", audio));
        }
        return ResResult.success(Map.of("video", List.of(), "audio", List.of()));
    }

    private ResResult getFavoriteAlbum(Integer page, Integer pageSize, Long uid) {
        IPage<BcChoiceAlbum> bcChoiceAlbumIPage = bcMyFavoriteMapper.selectAlbumListByUid(Page.of(page, pageSize, false), uid);
        if (CollUtil.isNotEmpty(bcChoiceAlbumIPage.getRecords())) {
            List<BcChoiceAlbumResDTO> video = new ArrayList<>();
            List<BcChoiceAlbumResDTO> audio = new ArrayList<>();
            Map<Long, Long> choiceNumsByAlbumIds = bcChoiceService.getChoiceNumsByAlbumIds(bcChoiceAlbumIPage.getRecords().stream().map(BcChoiceAlbum::getId).toList());
            for (BcChoiceAlbum bcChoiceAlbum : bcChoiceAlbumIPage.getRecords()) {
                BcChoiceAlbumResDTO bcChoiceAlbumResDTO = BcApiConverter.convert(bcChoiceAlbum);
                bcChoiceAlbumResDTO.setChoiceNum(choiceNumsByAlbumIds.getOrDefault(bcChoiceAlbum.getId(), 0L));
                if (bcChoiceAlbum.getType() == 1) {
                    audio.add(bcChoiceAlbumResDTO);
                } else if (bcChoiceAlbum.getType() == 2) {
                    video.add(bcChoiceAlbumResDTO);
                }
            }
            return ResResult.success(Map.of("video", video, "audio", audio));
        }
        return ResResult.success(Map.of("video", List.of(), "audio", List.of()));
    }

    /**
     * 我的收藏（专辑）
     */
    @GetMapping("/favoriteAlbum")
    public ResResult favoriteAlbum(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Long uid = SecurityUtils.getUserId();
        return getFavoriteAlbum(page, pageSize, uid);
    }


    /**
     * 我的收藏（分集）
     */
    @GetMapping("/favoriteChoice")
    public ResResult favoriteChoice(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Long uid = SecurityUtils.getUserId();
        return getFavoriteChoice(page, pageSize, uid);
    }

    /**
     * 我的互动 暂不需要
     */
//    @GetMapping("/interaction")
//    public ResResult interaction(
//            @RequestParam("type") Integer type,
//            @RequestParam(value = "page", defaultValue = "1") Integer page,
//            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
//        try {
//            return ResResult.success(mineService.getInteractionList(type, page, pageSize));
//        } catch (Exception e) {
//            return ResResult.error(e.getMessage());
//        }
//    }

//    /**
//     * 我的互动话题
//     */
//    @GetMapping("/interactionTopic")
//    public ResResult interactionTopic(
//            @RequestParam(value = "page", defaultValue = "1") Integer page,
//            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
//        try {
//            return ResResult.success(mineService.getInteractionTopicList(page, pageSize));
//        } catch (Exception e) {
//            return ResResult.error(e.getMessage());
//        }
//    }

    /**
     * 我的消息
     */
//    @GetMapping("/message")
//    public ResResult message(
//            @RequestParam("type") Integer type,
//            @RequestParam(value = "page", defaultValue = "1") Integer page,
//            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
//        try {
//            return ResResult.success(mineService.getMessageList(type, page, pageSize));
//        } catch (Exception e) {
//            return ResResult.error(e.getMessage());
//        }
//    }

//    /**
//     * 我的消息条数
//     */
//    @GetMapping("/messageNum")
//    public ResResult messageNum(@RequestParam("type") Integer type) {
//        try {
//            return ResResult.success(mineService.getMessageNumList(type));
//        } catch (Exception e) {
//            return ResResult.error(e.getMessage());
//        }
//    }

    /**
     * 我的消息条数
     * 空接口 兼容老版本
     */
    @GetMapping("/messageNum")
    public ResResult messageNum(@RequestParam("type") Integer type) {
        return ResResult.success();
    }
}