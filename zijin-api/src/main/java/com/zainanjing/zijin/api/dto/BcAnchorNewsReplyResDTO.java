package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
public class BcAnchorNewsReplyResDTO {

    @JsonProperty("id")
    private String id;

    @JsonProperty("create_time")
    private String createTime;

    @JsonProperty("update_time")
    private String updateTime;

    @JsonProperty("is_del")
    private String isDel;

    @JsonProperty("parent_entity_id")
    private String parentEntityId;

    @JsonProperty("news_id")
    private String newsId;

    @JsonProperty("reply_content")
    private String replyContent;

    @JsonProperty("reply_time")
    private String replyTime;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("from_user_name")
    private String fromUserName;

    @JsonProperty("to_user_id")
    private String toUserId;

    @JsonProperty("to_user_name")
    private String toUserName;

    @JsonProperty("is_anonymous")
    private String isAnonymous;

    @JsonProperty("is_show")
    private String isShow;

    @JsonProperty("is_audit")
    private String isAudit;

    @JsonProperty("audit_time")
    private String auditTime;

    @JsonProperty("reply_type")
    private String replyType;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("is_anonymous_by_to")
    private String isAnonymousByTo;

    @JsonProperty("is_read")
    private String isRead;

    @JsonProperty("images")
    private List<BcCommentImageResDTO> images;

    public List<BcCommentImageResDTO> getImages() {
        return images == null ? List.of() : images;
    }


}
