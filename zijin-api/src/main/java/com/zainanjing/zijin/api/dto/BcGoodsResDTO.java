package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
public class BcGoodsResDTO {
    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("update_time")
    private String updateTime;
    @JsonProperty("link_type")
    private String linkType;
    @JsonProperty("link_module")
    private String linkModule;
    @JsonProperty("link_to")
    private String linkTo;
    @JsonProperty("goods_res_id")
    private String goodsResId;
    @JsonProperty("img_link")
    private String imgLink;
    @JsonProperty("show_position")
    private String showPosition;
    @JsonProperty("forum_id")
    private String forumId;
    @JsonProperty("anchor_id")
    private String anchorId;
    @JsonProperty("is_recommand_anchor")
    private String isRecommandAnchor;
    @JsonProperty("is_show")
    private String isShow;
    @JsonProperty("is_explain")
    private String isExplain;
    @JsonProperty("sort_no")
    private String sortNo;
    @JsonProperty("is_del")
    private String isDel;
    @JsonProperty("goods_id")
    private String goodsId;
    @JsonProperty("goods_desc")
    private String goodsDesc;
    @JsonProperty("goods_name")
    private String goodsName;
    @JsonProperty("goods_sell_price")
    private String goodsSellPrice;
    @JsonProperty("goods_price")
    private String goodsPrice;
    @JsonProperty("order_cnt")
    private String orderCnt;
    @JsonProperty("goods_img_url")
    private String goodsImgUrl;

    public String getGoodsId() {
        return goodsId == null ? "" : goodsId;
    }

    public String getGoodsDesc() {
        return goodsDesc == null ? "" : goodsDesc;
    }

    public String getGoodsName() {
        return goodsName == null ? "" : goodsName;
    }

    public String getGoodsSellPrice() {
        return goodsSellPrice == null ? "" : goodsSellPrice;
    }

    public String getGoodsPrice() {
        return goodsPrice == null ? "" : goodsPrice;
    }

    public String getGoodsImgUrl() {
        return goodsImgUrl == null ? "" : goodsImgUrl;
    }

    public String getOrderCnt() {
        return orderCnt == null ? "" : orderCnt;
    }
}
