package com.zainanjing.zijin.api.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
public class BcAnchorImageResDTO {
    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("update_time")
    private String updateTime;
    @JsonProperty("anchor_id")
    private String anchorId;
    @JsonProperty("img_name")
    private String imgName;
    @JsonProperty("img_url")
    private String imgUrl;
    @JsonProperty("is_show")
    private String isShow;
    @JsonProperty("sort_no")
    private String sortNo;
}
