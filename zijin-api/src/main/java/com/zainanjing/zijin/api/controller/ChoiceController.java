package com.zainanjing.zijin.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.*;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.zijin.api.dto.BcChoiceAlbumResDTO;
import com.zainanjing.zijin.api.dto.BcChoiceCommentResDTO;
import com.zainanjing.zijin.api.dto.BcChoiceResDTO;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.dto.BcChoiceRankDTO;
import com.zainanjing.zijin.dto.GdmmUsersDTO;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.service.*;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Max;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 精选内容分集相关接口
 */
@RestController
@RequestMapping("/api-bc/choice")
@Validated

public class ChoiceController {

    @Resource
    private IBcChoiceService choiceService;

    @Resource
    private IBcChoiceAlbumService choiceAlbumService;

    @Resource
    private IBcChoiceCommentService choiceCommentService;

    @Resource
    private IBcVerifyConfigService verifyConfigService;

    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcChoiceCommentImgService choiceCommentImgService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private IBcChoiceStatisticsService choiceStatisticsService;

    /**
     * 热门新品
     */
    @Anonymous
    @GetMapping("/hotList")
    public ResResult hotList() {
        List<BcChoiceAlbum> choiceAlbums = choiceAlbumService.lambdaQuery().eq(BcChoiceAlbum::getStatus, 1).eq(BcChoiceAlbum::getIsHot, 1)
                .eq(BcChoiceAlbum::getIsShow, 1).select(BcChoiceAlbum::getId, BcChoiceAlbum::getCreateTime, BcChoiceAlbum::getUpdateTime, BcChoiceAlbum::getName, BcChoiceAlbum::getLogo, BcChoiceAlbum::getDescription, BcChoiceAlbum::getDescImgUrl, BcChoiceAlbum::getView, BcChoiceAlbum::getShareIcon, BcChoiceAlbum::getShareTitle, BcChoiceAlbum::getShareDesc, BcChoiceAlbum::getType).last("order by rand() limit 4").list();
        if (CollUtil.isEmpty(choiceAlbums)) {
            return ResResult.success();
        }
        Map<Long, Long> choiceViewsByAlbumIds = getChoiceViewsByAlbumIds(choiceAlbums.stream().map(BcChoiceAlbum::getId).toList());
        choiceAlbums.forEach(choiceAlbum -> choiceAlbum.setView(choiceViewsByAlbumIds.getOrDefault(choiceAlbum.getId(), 0L)));
        return ResResult.success(BcApiConverter.convertChoiceAlbums(choiceAlbums));
    }

    /**
     * 通过多个专辑id获取分集浏览数总和分集数量
     */
    private Map<Long, Long> getChoiceViewsByAlbumIds(List<Long> albumId) {
        if (CollUtil.isEmpty(albumId)) {
            return Collections.emptyMap();
        }
        QueryWrapper<BcChoice> query = Wrappers.query();
        query.select("album_id", "sum(`view` + `virtual_view`) as total").in("album_id", albumId).groupBy("album_id");
        List<Map<String, Object>> maps = choiceService.listMaps(query);
        return maps.stream().collect(Collectors.toMap(map -> (Long) map.get("album_id"), map -> ((BigDecimal) map.getOrDefault("total", BigDecimal.ZERO)).longValue()));
    }


    /**
     * 专辑列表（音频、视频）
     */
    @Anonymous
    @GetMapping("/albumList")
    public ResResult albumList(@RequestParam(value = "type", defaultValue = "1") Integer type, @RequestParam(value = "is_choice", defaultValue = "0") Integer choice, @RequestParam(value = "cate_id", defaultValue = "0") Integer cateId, @RequestParam(value = "is_hot", defaultValue = "0") Integer hot, @RequestParam(value = "currentPage", defaultValue = "1") Integer page, @RequestParam(value = "pageSize", defaultValue = "20") @Max(100) Integer pageSize) {
        Page<BcChoiceAlbum> resPages = choiceAlbumService.lambdaQuery().eq(BcChoiceAlbum::getType, type).eq(choice > 0, BcChoiceAlbum::getIsJingxuan, 1).eq(cateId > 0, BcChoiceAlbum::getCategoryId, cateId).eq(hot > 0, BcChoiceAlbum::getIsHot, 1).eq(BcChoiceAlbum::getStatus, 1).eq(BcChoiceAlbum::getIsShow, 1).select(BcChoiceAlbum::getId, BcChoiceAlbum::getCreateTime, BcChoiceAlbum::getUpdateTime, BcChoiceAlbum::getName, BcChoiceAlbum::getLogo, BcChoiceAlbum::getDescription, BcChoiceAlbum::getDescImgUrl, BcChoiceAlbum::getView, BcChoiceAlbum::getShareIcon, BcChoiceAlbum::getShareTitle, BcChoiceAlbum::getShareDesc, BcChoiceAlbum::getType).orderByDesc(BcChoiceAlbum::getSort).orderByDesc(BcChoiceAlbum::getId).page(Page.of(page, pageSize, false));
        if (CollUtil.isNotEmpty(resPages.getRecords())) {
            List<BcChoiceAlbumResDTO> bcChoiceAlbumResDTOS = BcApiConverter.convertChoiceAlbums(resPages.getRecords());
            Map<Long, Long> choiceViewsByAlbumIds = getChoiceViewsByAlbumIds(resPages.getRecords().stream().map(BcChoiceAlbum::getId).toList());
            Map<Long, Long> choiceNumsByAlbumIds = choiceService.getChoiceNumsByAlbumIds(resPages.getRecords().stream().map(BcChoiceAlbum::getId).toList());

            bcChoiceAlbumResDTOS.forEach(choiceAlbum -> {
                choiceAlbum.setView(choiceViewsByAlbumIds.getOrDefault(Long.valueOf(choiceAlbum.getId()), 0L));
                choiceAlbum.setNums(choiceNumsByAlbumIds.getOrDefault(Long.valueOf(choiceAlbum.getId()), 0L));
            });
            return ResResult.success(bcChoiceAlbumResDTOS);
        }

        return ResResult.success(List.of());
    }

    /**
     * 专辑详情（音频、视频）
     */
    @Anonymous
    @GetMapping("/albumDetail")
    public ResResult albumDetail(@RequestParam("id") Integer id) {
        BcChoiceAlbum bcChoiceAlbum = choiceAlbumService.getById(id);
        if (bcChoiceAlbum != null) {
            choiceAlbumService.lambdaUpdate().eq(BcChoiceAlbum::getId, id).setIncrBy(BcChoiceAlbum::getView, 1).update();
            BcChoiceAlbumResDTO bcChoiceAlbumResDTO = BcApiConverter.convert(bcChoiceAlbum);
            Map<Long, Long> choiceNumsByAlbumIds = choiceService.getChoiceNumsByAlbumIds(List.of(bcChoiceAlbum.getId()));
            bcChoiceAlbumResDTO.setNums(choiceNumsByAlbumIds.getOrDefault(Long.valueOf(bcChoiceAlbum.getId()), 0L));
            bcChoiceAlbumResDTO.setTotalview(StrUtil.toString(bcChoiceAlbum.getView() + bcChoiceAlbum.getVirtualView()));
            return ResResult.success(bcChoiceAlbumResDTO);
        }
        return ResResult.error("专辑不存在");
    }

    /**
     * 分集列表（音频、视频）
     */
    @Anonymous
    @GetMapping("/programList")
    public ResResult programList(
            @RequestParam("albumId") Integer albumId,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {

        Page<BcChoice> resPages = choiceService.lambdaQuery()
                .eq(BcChoice::getAlbumId, albumId)
                .eq(BcChoice::getIsShow, 1)
                .eq(BcChoice::getStatus, 1)
                .select(BcChoice::getId, BcChoice::getCreateTime, BcChoice::getAlbumId, BcChoice::getName, BcChoice::getDescription, BcChoice::getLogo, BcChoice::getUrl, BcChoice::getView, BcChoice::getVirtualView, BcChoice::getDuration, BcChoice::getSeconds, BcChoice::getIsShow, BcChoice::getSort, BcChoice::getShareIcon, BcChoice::getShareTitle, BcChoice::getShareDesc, BcChoice::getType, BcChoice::getStatus)
                .orderByDesc(BcChoice::getSort).orderByDesc(BcChoice::getId)
                .page(Page.of(page, pageSize, false));

        if (CollUtil.isNotEmpty(resPages.getRecords())) {
            List<BcChoiceResDTO> bcChoiceResDTOS = BcApiConverter.convertChoices(resPages.getRecords());
            String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
            for (BcChoiceResDTO bcChoiceResDTO : bcChoiceResDTOS) {
                Long commentNum = choiceCommentService.lambdaQuery().eq(BcChoiceComment::getChoiceId, bcChoiceResDTO.getId())
                        .eq(BcChoiceComment::getIsShow, 1)
                        .eq(BcChoiceComment::getParentId, 0)
                        .and(i -> i.eq(StrUtil.isNotEmpty(uid), BcChoiceComment::getUserId, uid)
                                .or().eq(BcChoiceComment::getStatus, 2))
                        .count();
                bcChoiceResDTO.setCommentNum(commentNum.toString());
            }
            return ResResult.success(bcChoiceResDTOS);
        }
        return ResResult.success(List.of());

    }

    /**
     * 分集详情
     */
    @Anonymous
    @GetMapping("/programDetail")
    public ResResult programDetail(@RequestParam("id") Integer id) {
        if (id <= 0) {
            return ResResult.error("分集id不能为空！");
        }
        BcChoice info = choiceService.getById(id);
        if (info == null) {
            return ResResult.error("分集不存在！");
        }
        // 更新分集的浏览数
        info.setView(info.getView() + 1);
        choiceService.lambdaUpdate().eq(BcChoice::getId, id).setIncrBy(BcChoice::getView, 1).update();
        choiceAlbumService.lambdaUpdate().eq(BcChoiceAlbum::getId, info.getAlbumId()).setIncrBy(BcChoiceAlbum::getView, 1).update();


        // 更新对应专辑浏览次数
        BcChoiceAlbum albumInfo = choiceAlbumService.getById(info.getAlbumId());
        if (albumInfo != null) {
            albumInfo.setView(albumInfo.getView() + 1);
            choiceAlbumService.updateById(albumInfo);
        }

        // 插入统计表数据
        // Service_ChoiceStatisticsModel::addRow($id);
        LocalDate today = LocalDate.now();
        //获取当天的00:00的时间戳
        long unixTime = today.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
        //获取这个月第一天的00:00的时间戳
        long unixMonthTime = today.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
        LocalDateTime now = LocalDateTime.now();
        BcChoiceStatistics bcChoiceStatistics = choiceStatisticsService.getOne(new QueryWrapper<BcChoiceStatistics>()
                .eq("choice_id", id).eq("unix_time", unixTime));
        if (null == bcChoiceStatistics) {
            bcChoiceStatistics = new BcChoiceStatistics();
            bcChoiceStatistics.setChoiceId(id);
            bcChoiceStatistics.setType(info.getType());
            bcChoiceStatistics.setView(1);
            bcChoiceStatistics.setTotalView(info.getView() + info.getVirtualView());
            bcChoiceStatistics.setCreateTime(now);
            bcChoiceStatistics.setUpdateTime(now);
            bcChoiceStatistics.setUnixTime(Math.toIntExact(unixTime));
            bcChoiceStatistics.setUnixMonthTime(Math.toIntExact(unixMonthTime));
        } else {
            bcChoiceStatistics.setUpdateTime(now);
            bcChoiceStatistics.setView(bcChoiceStatistics.getView() + 1);
            bcChoiceStatistics.setTotalView(info.getView() + info.getVirtualView());
        }
        choiceStatisticsService.saveOrUpdate(bcChoiceStatistics);
        // 当前登录用户uid
        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());

        Long commentNum = choiceCommentService.lambdaQuery().eq(BcChoiceComment::getChoiceId, info.getId())
                .eq(BcChoiceComment::getIsShow, 1)
                .eq(BcChoiceComment::getParentId, 0)
                .and(i -> i.eq(StrUtil.isNotEmpty(uid), BcChoiceComment::getUserId, uid)
                        .or().eq(BcChoiceComment::getStatus, 2))
                .count();

        BcChoiceResDTO bcChoiceResDTO = BcApiConverter.convert(info);

        // 获取分集评论数
        bcChoiceResDTO.setCommentNum(commentNum.toString());

        // 是否开启分集评论，默认为1开启
        bcChoiceResDTO.setIsComment("1");

        // 获取分集评论配置
        verifyConfigService.lambdaQuery().eq(BcVerifyConfig::getVerifyType, 4).oneOpt().ifPresent(verifyConfig -> {
            bcChoiceResDTO.setIsComment(verifyConfig.getIsComment().toString());
        });
        return ResResult.success(bcChoiceResDTO);
    }

    /**
     * 评论列表
     */
    @Anonymous
    @GetMapping("/commentList")
    public ResResult commentList(
            @RequestParam("id") Integer id,
            @RequestParam(value = "onlyNum", defaultValue = "0") Integer onlyNum,
            @RequestParam(value = "sortType", defaultValue = "2") Integer sortType,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        if (onlyNum == 1) {
            Long commentNum = choiceCommentService.lambdaQuery().eq(BcChoiceComment::getChoiceId, id)
                    .eq(BcChoiceComment::getIsShow, 1)
                    .eq(BcChoiceComment::getParentId, 0)
                    .and(i -> i.eq(StrUtil.isNotEmpty(uid), BcChoiceComment::getUserId, uid)
                            .or().eq(BcChoiceComment::getStatus, 2))
                    .count();
            return ResResult.success(commentNum);
        }
        Page<BcChoiceComment> resPage = choiceCommentService.lambdaQuery().eq(BcChoiceComment::getChoiceId, id)
                .eq(BcChoiceComment::getIsShow, 1)
                .eq(BcChoiceComment::getParentId, 0)
                .and(i -> i.eq(StrUtil.isNotEmpty(uid), BcChoiceComment::getUserId, uid)
                        .or().eq(BcChoiceComment::getStatus, 2))
                .orderBy(true, sortType == 1, BcChoiceComment::getId)
                .page(Page.of(page, pageSize, false));

        if (CollUtil.isNotEmpty(resPage.getRecords())) {
            List<BcChoiceComment> subComments = choiceCommentService.lambdaQuery().eq(BcChoiceComment::getChoiceId, id)
                    .in(BcChoiceComment::getParentId, resPage.getRecords().stream().map(BcChoiceComment::getId).toList())
                    .eq(BcChoiceComment::getStatus, 2)
                    .orderByDesc(BcChoiceComment::getId)
                    .list();
            List<BcChoiceCommentResDTO> comments = new ArrayList<>();

            Set<Long> uids = resPage.getRecords().stream().map(BcChoiceComment::getUserId).collect(Collectors.toSet());
//            uids.addAll(subComments.stream().map(BcChoiceComment::getUserId).collect(Collectors.toSet()));

            Set<Long> commentIds = resPage.getRecords().stream().map(BcChoiceComment::getId).collect(Collectors.toSet());
            commentIds.addAll(subComments.stream().map(BcChoiceComment::getId).collect(Collectors.toSet()));

            List<BcChoiceCommentImg> bcChoiceCommentImgs = choiceCommentImgService.lambdaQuery().in(BcChoiceCommentImg::getCommentId, commentIds).orderByDesc(BcChoiceCommentImg::getId).list();

            Map<Long, GdmmUsersDTO> gdmmUsersMap = new HashMap<>();
            if (CollUtil.isNotEmpty(uids)) {
                List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uids", uids));
                gdmmUsersMap = gdmmUsersDTOS.stream().collect(Collectors.toMap(GdmmUsersDTO::getUserId, Function.identity()));
            }
            Map<Long, GdmmUsersDTO> finalGdmmUsersMap = gdmmUsersMap;

            for (BcChoiceComment comment : resPage.getRecords()) {
                BcChoiceCommentResDTO commentResDTO = BcApiConverter.convert(comment);
                GdmmUsersDTO gdmmUsersDTO = finalGdmmUsersMap.get(comment.getUserId());

                if (ObjUtil.isNotNull(gdmmUsersDTO)) {
                    commentResDTO.setUserName(gdmmUsersDTO.getUserName());
                    commentResDTO.setHeadImgUrl(gdmmUsersDTO.getHeadImgUrl());
                    commentResDTO.setMedalLevel(gdmmUsersDTO.getMedalLevel().toString());
                    commentResDTO.setAvatar(OSSUtil.getImageURL(gdmmUsersDTO.getAvatar()));
                }
                List<BcChoiceCommentImg> commentImgs = bcChoiceCommentImgs.stream().filter(img -> img.getCommentId().equals(comment.getId())).toList();
                commentResDTO.setImgList(BcApiConverter.convertChoiceCommentImages(commentImgs));

                List<BcChoiceComment> subCommentList = subComments.stream().filter(subComment -> subComment.getParentId().equals(comment.getId())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(subCommentList)) {
                    List<BcChoiceCommentResDTO> subCommentResDTOList = new ArrayList<>();
                    for (BcChoiceComment subComment : subCommentList) {
                        BcChoiceCommentResDTO subDTO = BcApiConverter.convert(subComment);
                        gdmmUsersDTO = finalGdmmUsersMap.get(subComment.getUserId());
                        if (ObjUtil.isNotNull(gdmmUsersDTO)) {
                            subDTO.setUserName(gdmmUsersDTO.getUserName());
                            subDTO.setHeadImgUrl(gdmmUsersDTO.getHeadImgUrl());
                            subDTO.setMedalLevel(gdmmUsersDTO.getMedalLevel().toString());
                            subDTO.setAvatar(gdmmUsersDTO.getAvatar());
                        }
                        commentImgs = bcChoiceCommentImgs.stream().filter(img -> img.getCommentId().equals(subComment.getId())).toList();
                        subDTO.setImgList(BcApiConverter.convertChoiceCommentImages(commentImgs));
                        subCommentResDTOList.add(subDTO);
                    }
                    commentResDTO.setSubComment(subCommentResDTOList);
                    commentResDTO.setReplyNum(subCommentList.size());
                }
                comments.add(commentResDTO);
            }
            return ResResult.success(comments);
        }
        return ResResult.success(List.of());
    }

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;

    /**
     * 保存评论
     */
    @Log(title = "保存评论", businessType = BusinessType.INSERT)
    @PostMapping("/comment")
    public ResResult comment(
            @RequestParam("content") String content,
            @RequestParam(value = "parent_id", defaultValue = "0") Long parentId,
            @RequestParam("album_id") Long albumId,
            @RequestParam("choice_id") Long choiceId,
            @RequestParam(value = "is_anonymous", defaultValue = "0") Integer isAnonymous,
            @RequestParam(value = "images", required = false) String images) {
        // 敏感词过滤
        wangyiCheckUtil.checkText(content);
        wangyiCheckUtil.checkImages(images);

        String userId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        BcChoiceComment bcChoiceComment = new BcChoiceComment();
        bcChoiceComment.setAlbumId(albumId);
        bcChoiceComment.setChoiceId(choiceId);
        bcChoiceComment.setContent(content);
        bcChoiceComment.setIsAnonymous(isAnonymous);
        bcChoiceComment.setParentId(parentId);
        bcChoiceComment.setUserId(Long.valueOf(userId));
        bcChoiceComment.setIsShow(1);
        verifyConfigService.lambdaQuery().eq(BcVerifyConfig::getVerifyType, 4).oneOpt().ifPresent(bcVerifyConfig -> {
            if (ObjUtil.equals(bcVerifyConfig.getIsComment(), 0)) {
                throw new ServiceException("评论已关闭");
            }
            if (ObjUtil.equals(bcVerifyConfig.getIsAudit(), 1)) {
                bcChoiceComment.setStatus(1);
            } else {
                bcChoiceComment.setStatus(2);
            }
        });
        bcChoiceComment.setImgUrls(images);
        if (choiceCommentService.addChoiceComment(bcChoiceComment)) {
            if (parentId > 0) {
                choiceCommentService.lambdaUpdate().eq(BcChoiceComment::getId, parentId).setIncrBy(BcChoiceComment::getReplyNums, 1).update();
            }
            applicationEventPublisher.publishEvent(new CommonEvent(bcChoiceComment, EventAction.builder()
                    .operator(SecurityUtils.getLoginUser())
                    .type(BusinessType.INSERT)
                    .build()));
            return ResResult.successMsg("评论成功");
        } else {
            return ResResult.error("评论失败");
        }
    }

    /**
     * 删除评论
     */
    @Log(title = "删除评论", businessType = BusinessType.DELETE)
    @PostMapping("/delComment")
    public ResResult delComment(@RequestParam("id") Integer id) {
        String userId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        BcChoiceComment commentInfo = choiceCommentService.getById(id);

        if (commentInfo == null) {
            return ResResult.error("此评论不存在！");
        }

        if (commentInfo.getIsShow() == 0) {
            return ResResult.error("此评论已删除，请勿重复操作！");
        }

        // 不是自己的帖子不能删除
        if (!commentInfo.getUserId().equals(Long.valueOf(userId))) {
            return ResResult.error("无权限删除此评论！");
        }

        commentInfo.setIsShow(0);
        choiceCommentService.lambdaUpdate().eq(BcChoiceComment::getId, id).set(BcChoiceComment::getIsShow, 0).update();

        return ResResult.successMsg("删除成功！");
    }

    /**
     * 音视频排行榜
     */
    @Anonymous
    @GetMapping("/rankList")
    public ResResult rankList(
            @RequestParam("type") Integer type,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        int startInt = (page - 1) * pageSize;
        List<BcChoiceRankDTO> rankList = choiceService.getRankList(type, startInt, pageSize);
        return ResResult.success(rankList);
    }

    /**
     * 最新音视频列表
     */
    @Anonymous
    @GetMapping("/newestList")
    public ResResult newestList(
            @RequestParam("type") Integer type,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "5") @Max(value = 100) Integer pageSize) {

        Page<BcChoice> res = choiceService.lambdaQuery()
                .eq(BcChoice::getType, type).eq(BcChoice::getStatus, 1).eq(BcChoice::getIsShow, 1)
                .orderByDesc(BcChoice::getId)
                .page(Page.of(page, pageSize, false));

        if (CollUtil.isEmpty(res.getRecords())) {
            return ResResult.success(List.of());
        }

        Map<Long, String> choiceAlbumMaps = choiceAlbumService.lambdaQuery().in(BcChoiceAlbum::getId, res.getRecords().stream().map(BcChoice::getAlbumId).toList()).list().stream()
                .collect(Collectors.toMap(BcChoiceAlbum::getId, BcChoiceAlbum::getName));

        List<BcChoiceResDTO> bcChoiceResDTOS = new ArrayList<>();
        for (BcChoice bcChoice : res.getRecords()) {
            BcChoiceResDTO resObj = BcApiConverter.convert(bcChoice);
            resObj.setAlbumName(choiceAlbumMaps.get(bcChoice.getAlbumId()));
            resObj.setTotalView(StrUtil.toString(bcChoice.getView() + bcChoice.getVirtualView()));
            bcChoiceResDTOS.add(resObj);
        }

        return ResResult.success(bcChoiceResDTOS);
    }
}