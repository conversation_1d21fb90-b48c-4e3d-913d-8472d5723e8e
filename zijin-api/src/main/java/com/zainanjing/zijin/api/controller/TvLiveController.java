package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.BcProgramTvLive;
import com.zainanjing.zijin.service.IBcProgramTvLiveService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 电视直播接口
 *
 * <AUTHOR>
 * @date 2020/12/14
 */
@RestController
@RequestMapping("/api-bc/tvLive")
public class TvLiveController {

    @Resource
    private IBcProgramTvLiveService bcProgramTvLiveService;

    /**
     * 更新观看人数
     *
     * @param id 节目ID
     * @return 更新结果
     */
    @Anonymous
    @RequestMapping(value = "/updateViews", method = {RequestMethod.GET,RequestMethod.POST})
    public ResResult updateViews(@RequestParam("id") Long id) {
        bcProgramTvLiveService.lambdaUpdate().eq(BcProgramTvLive::getId, id).setIncrBy(BcProgramTvLive::getView, 1).update();
        return ResResult.success();
    }
}