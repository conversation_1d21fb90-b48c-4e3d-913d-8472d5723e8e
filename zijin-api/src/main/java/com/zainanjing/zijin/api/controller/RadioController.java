package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.zijin.api.dto.BcTopicResDTO;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.service.*;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Max;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 互动区相关接口
 */
@RestController
@RequestMapping("/api-bc/radio")
public class RadioController {

    @Resource
    private IBcTopicService bcTopicService;

    @Resource
    private IBcForumService bcForumService;

    @Resource
    private IBcPostService bcPostService;

    @Resource
    private IBcFmService bcFmService;

    @Resource
    private IBcAnchorRelationService bcAnchorRelationService;

    @Resource
    private IBcVerifyConfigService bcVerifyConfigService;

    /**
     * 互动区话题列表
     */
    @Anonymous
    @GetMapping("/getTopicList")
    public ResResult getTopicList(@RequestParam("forumId") Integer forumId,
                                  @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
                                  @RequestParam(value = "pageSize", defaultValue = "20") @Max(100) Integer pageSize) throws JsonProcessingException {
        Page<BcTopic> resPage = bcTopicService.lambdaQuery().eq(BcTopic::getForumId, forumId)
                .eq(BcTopic::getStatus, 1)
                .select(BcTopic::getId, BcTopic::getCreateTime, BcTopic::getName, BcTopic::getDescription, BcTopic::getCoverThumb, BcTopic::getView, BcTopic::getForumId, BcTopic::getFmId, BcTopic::getProgramId, BcTopic::getTopicDate, BcTopic::getReplyNum)
                .page(Page.of(page, pageSize, false));

        if (CollUtil.isNotEmpty(resPage.getRecords())) {
            // 获取话题所属互动区
            List<Long> forumIds = resPage.getRecords().stream().map(BcTopic::getForumId).collect(Collectors.toList());
            List<BcForum> forums = bcForumService.lambdaQuery().in(BcForum::getId, forumIds)
                    .select(BcForum::getId, BcForum::getName)
                    .list();
            Map<Long, BcForum> forumMap = forums.stream().collect(Collectors.toMap(BcForum::getId, forum -> forum));

            // 话题ID
            List<Long> topicIds = resPage.getRecords().stream().map(BcTopic::getId).collect(Collectors.toList());
            // 获取话题回复数
            QueryWrapper<BcPost> query = Wrappers.query();
            query.select("topic_id, count(id) as nums").in("topic_id", topicIds).groupBy("topic_id");
            Map<Long, Integer> replyNumMap = bcPostService.listMaps(query).stream().collect(Collectors.toMap(map -> (Long) map.get("topic_id"), map -> ((Long) map.get("nums")).intValue()));

            // 获取话题所属频率
            List<Long> fmIds = resPage.getRecords().stream().map(BcTopic::getFmId).collect(Collectors.toList());
            List<BcFm> fms = bcFmService.lambdaQuery().in(BcFm::getId, fmIds)
                    .select(BcFm::getId, BcFm::getName)
                    .list();
            Map<Long, String> fmMap = fms.stream().collect(Collectors.toMap(BcFm::getId, BcFm::getName));

            List<BcTopicResDTO> resList = new ArrayList<>();
            for (BcTopic topic : resPage.getRecords()) {
                BcTopicResDTO convert = BcApiConverter.convert(topic);
                convert.setForumName(forumMap.get(topic.getForumId()).getName());
                convert.setFmName(fmMap.get(topic.getFmId()));
                convert.setReplyNum(replyNumMap.getOrDefault(topic.getId(), 0));
                resList.add(convert);
            }
            return ResResult.success(resList);
        }
        return ResResult.success(List.of());
    }

    /**
     * 话题详情
     */
    @Anonymous
    @GetMapping("/topicdetail")
    public ResResult topicDetail(@RequestParam("id") Integer topicId) {
        BcTopic topic = bcTopicService.lambdaQuery().eq(BcTopic::getId, topicId)
                .eq(BcTopic::getStatus, 1)
                .select(BcTopic::getId, BcTopic::getCreateTime, BcTopic::getContent, BcTopic::getName, BcTopic::getDescription, BcTopic::getCoverThumb, BcTopic::getView, BcTopic::getForumId, BcTopic::getFmId, BcTopic::getProgramId, BcTopic::getTopicDate, BcTopic::getReplyNum, BcTopic::getIsComment)
                .one();
        BcTopicResDTO convert = BcApiConverter.convert(topic);
        String userId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        //客户端要置顶某个帖子，判断一下当前用户是否有对应节目的权限
        //根据节目对应的主播id
        if (StrUtil.isNotBlank(userId)) {
            if (bcAnchorRelationService.lambdaQuery()
                    .eq(BcAnchorRelation::getAnchorId, userId).eq(BcAnchorRelation::getBcId, topic.getProgramId())
                    .exists()) {
                convert.setCanTopPost(1);
            }
        }
        bcVerifyConfigService.lambdaQuery().eq(BcVerifyConfig::getVerifyType, 1).oneOpt().ifPresent(bcVerifyConfig -> {
            if (ObjUtil.equals(bcVerifyConfig.getIsComment(), 0)) {
                convert.setIsComment(0);
            }
        });

        bcTopicService.lambdaUpdate().eq(BcTopic::getId, topic.getId())
                .setIncrBy(BcTopic::getView, 1)
                .set(BcTopic::getIsComment, convert.getIsComment())
                .update();
        return ResResult.success(convert);
    }

    /**
     * 置顶帖子
     */
    @PostMapping("/topPost")
    public ResResult topPost(@RequestParam("id") Integer postId,
                             @RequestParam("top") Integer top) {

        if (!List.of(0, 1).contains(top)) {
            return ResResult.error("置顶参数错误！");
        }

        BcPost bcPost = bcPostService.getById(postId);

        if (bcPost == null) {
            return ResResult.error("帖子不存在！");
        }

        String userId = StrUtil.toStringOrNull(SecurityUtils.getUserId());

        if (!bcAnchorRelationService.lambdaQuery()
                .eq(BcAnchorRelation::getAnchorId, userId)
                .eq(BcAnchorRelation::getBcId, bcPost.getProgramId())
                .exists()) {
            return ResResult.error(20, "您没有此操作的权限！");
        }


        boolean success = bcPostService.lambdaUpdate().eq(BcPost::getId, postId)
                .set(BcPost::getIsTop, top)
                .update();
        String msg = top == 1 ? "置顶成功！" : "取消置顶成功！";
        if (!success) {
            msg = top == 1 ? "置顶失败！" : "取消置顶失败！";
            return ResResult.error(msg);
        }
        return ResResult.successMsg(msg);

    }
}