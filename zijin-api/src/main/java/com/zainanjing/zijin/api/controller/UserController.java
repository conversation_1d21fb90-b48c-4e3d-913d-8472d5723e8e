package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.service.*;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户相关接口
 */
@RestController
@RequestMapping("/api-bc/user")
public class UserController {

    @Resource
    private IBcAnchorFansService bcAnchorFansService;

    @Resource
    private IBcAnchorService bcAnchorService;

    @Resource
    private IBcMyFavoriteService bcMyFavoriteService;

    @Resource
    private IBcMyPraiseService bcMyPraiseService;

    @Resource
    private IBcAnchorNewsService bcAnchorNewsService;

    @Resource
    private IBcAnchorNewsReplyService bcAnchorNewsReplyService;

    @Resource
    private IBcReportService bcReportService;

    /**
     * 关注主播
     */

    @PostMapping("/follow")
    public ResResult follow(@RequestParam("anchorId") Long anchorId,
                            @RequestParam("flag") @Min(0) @Max(1) Integer flag) {
        if (anchorId <= 0) {
            return ResResult.error("主播ID格式有误");
        }

        Long uid = SecurityUtils.getUserId();
        BcAnchor bcAnchor = bcAnchorService.lambdaQuery().eq(BcAnchor::getUserId, anchorId).one();
        if (bcAnchor == null) {
            return ResResult.error("主播不存在");
        }
        if (flag == 0) {//取消
            if (bcAnchorFansService.lambdaUpdate().eq(BcAnchorFans::getAnchorId, anchorId).eq(BcAnchorFans::getFansId, uid).remove()) {
                bcAnchorService.lambdaUpdate().setIncrBy(BcAnchor::getFansNum, -1).eq(BcAnchor::getUserId, anchorId).update();
            }
        } else {//关注
            Date now = new Date();
            bcAnchorFansService.lambdaQuery().eq(BcAnchorFans::getAnchorId, anchorId).eq(BcAnchorFans::getFansId, uid).oneOpt().ifPresentOrElse(
                    bcAnchorFans -> {
                    },
                    () -> {
                        if (bcAnchorFansService.save(BcAnchorFans.builder()
                                .createTime(now)
                                .updateTime(now)
                                .anchorId(anchorId)
                                .fansId(uid).build())) {
                            bcAnchorService.lambdaUpdate().setIncrBy(BcAnchor::getFansNum, 1).eq(BcAnchor::getUserId, anchorId).update();
                        }
                    }
            );
        }

        return ResResult.success();
    }

    /**
     * 收藏节目、专辑、分集
     */
    @Log(title = "收藏节目、专辑、分集", businessType = BusinessType.INSERT)
    @PostMapping("/collect")
    public ResResult collect(@RequestParam("favoriteId") String favoriteId,
                             @RequestParam("type") Integer type,
                             @RequestParam("flag") Integer flag) {

        if (!List.of(1, 2, 3).contains(type)) {
            return ResResult.error("收藏类型格式有误");
        }

        Long uid = SecurityUtils.getUserId();
        if (flag == 0) {//取消
            bcMyFavoriteService.lambdaUpdate().eq(BcMyFavorite::getFavoriteId, favoriteId).eq(BcMyFavorite::getType, type).eq(BcMyFavorite::getUserId, uid).remove();
        } else {
            Date now = new Date();
            bcMyFavoriteService.lambdaQuery().eq(BcMyFavorite::getFavoriteId, favoriteId).eq(BcMyFavorite::getType, type).eq(BcMyFavorite::getUserId, uid).oneOpt().ifPresentOrElse(
                    bcMyFavorite -> {
                    },
                    () -> {
                        bcMyFavoriteService.save(BcMyFavorite.builder()
                                .createTime(now)
                                .updateTime(now)
                                .userId(uid)
                                .favoriteId(Long.valueOf(favoriteId))
                                .type(type).build());
                    }
            );
        }
        return ResResult.success();
    }

    /**
     * 检测是否收藏
     */
    @GetMapping("/checkCollect")
    public ResResult checkCollect(@RequestParam("favoriteId") Integer favoriteId,
                                  @RequestParam("type") Integer type) {
        if (!List.of(1, 2, 3).contains(type)) {
            return ResResult.error("收藏类型格式有误");
        }
//        Long uid = Optional.ofNullable(StrUtil.toStringOrNull(SecurityUtils.getUserId()))
//                .map(Long::valueOf)
//                .orElse(null);
//        if (uid == null) {
//            return ResResult.error("用户ID为空");
//        }
        Long uid = SecurityUtils.getUserId();
        boolean flag = bcMyFavoriteService.lambdaQuery().eq(BcMyFavorite::getFavoriteId, favoriteId).eq(BcMyFavorite::getType, type).eq(BcMyFavorite::getUserId, uid).exists();
        return ResResult.success(Map.of("flag", flag ? 1 : 0));
    }

    /**
     * 点赞
     */
    @PostMapping("/praise")
    public ResResult praise(@RequestParam("praiseId") Long praiseId,
                            @RequestParam("type") Integer type,
                            @RequestParam("flag") Integer flag) {
        if (praiseId <= 0) {
            return ResResult.error("点赞项目ID格式有误");
        }

        Long uid = SecurityUtils.getUserId();
        if (flag == 0) {//取消
            if (bcMyPraiseService.lambdaUpdate()
                    .eq(BcMyPraise::getPraiseId, praiseId).eq(BcMyPraise::getType, type)
                    .eq(BcMyPraise::getUserId, uid).remove()) {
                if (ObjUtil.equals(1, type)) {
                    bcAnchorNewsService.lambdaUpdate().setIncrBy(BcAnchorNews::getCompNum, -1).eq(BcAnchorNews::getId, praiseId).update();
                }
            }
        } else {
            bcMyPraiseService.lambdaQuery().eq(BcMyPraise::getPraiseId, praiseId).eq(BcMyPraise::getType, type).eq(BcMyPraise::getUserId, uid).oneOpt().ifPresentOrElse(
                    bcMyPraise -> {
                    },
                    () -> {
                        Date now = new Date();
                        Long anchorId = bcAnchorNewsService.lambdaQuery().eq(BcAnchorNews::getId, praiseId).select(BcAnchorNews::getAnchorId).one().getAnchorId();
                        if (bcMyPraiseService.save(BcMyPraise.builder()
                                .createTime(now)
                                .updateTime(now)
                                .userId(uid)
                                .praiseId(praiseId)
                                .toUserId(anchorId)
                                .type(type).build())) {
                            if (ObjUtil.equals(1, type)) {
                                bcAnchorNewsService.lambdaUpdate().setIncrBy(BcAnchorNews::getCompNum, 1).eq(BcAnchorNews::getId, praiseId).update();
                            }
                        }
                    }
            );


        }
        return ResResult.success();
    }

    /**
     * 阅读消息
     */
    @Log(title = "阅读消息", businessType = BusinessType.OTHER)
    @PostMapping("/readMessage")
    public ResResult readMessage(@RequestParam("type") Integer type,
                                 @RequestParam("messageId") Integer messageId) {
        Long uid = SecurityUtils.getUserId();
        switch (type) {
            case 1:
                bcAnchorNewsReplyService.lambdaUpdate().set(BcAnchorNewsReply::getIsRead, 1)
                        .eq(BcAnchorNewsReply::getUserId, uid)
                        .eq(BcAnchorNewsReply::getIsRead, 0)
                        .eq(BcAnchorNewsReply::getId, messageId).update();
                break;
            case 2:
                bcMyPraiseService.lambdaUpdate().set(BcMyPraise::getIsRead, 1)
                        .eq(BcMyPraise::getUserId, uid)
                        .eq(BcMyPraise::getIsRead, 0)
                        .eq(BcMyPraise::getId, messageId).update();
                break;
            default:
                return ResResult.error("消息类型格式有误");
        }

        return ResResult.success();
    }

    /**
     * 清空消息（评论、点赞）
     */
    @Log(title = "清空消息（评论、点赞）", businessType = BusinessType.CLEAN)
    @PostMapping("/clear")
    public ResResult clear(@RequestParam("type") Integer type) {
        Long uid = SecurityUtils.getUserId();
        switch (type) {
            case 1:
                bcAnchorNewsReplyService.lambdaUpdate().set(BcAnchorNewsReply::getIsRead, 1)
                        .eq(BcAnchorNewsReply::getUserId, uid)
                        .eq(BcAnchorNewsReply::getIsRead, 0).update();
                break;
            case 2:
                bcMyPraiseService.lambdaUpdate().set(BcMyPraise::getIsRead, 1)
                        .eq(BcMyPraise::getUserId, uid)
                        .eq(BcMyPraise::getIsRead, 0).update();
                break;
            default:
                return ResResult.error("消息类型格式有误");
        }

        return ResResult.success();
    }

    /**
     * 举报
     */
    @PostMapping("/report")
    public ResResult report() {
        Date now = new Date();
        bcReportService.save(BcReport.builder()
                .createTime(now)
                .updateTime(now)
                .userId(SecurityUtils.getUserId())
                .isDel(0)
                .reportContent("").build());
        return ResResult.success();
    }
}