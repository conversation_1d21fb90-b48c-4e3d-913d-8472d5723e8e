package com.zainanjing.zijin.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
public class BcCommentImageResDTO {
    @JsonProperty("id")
    private String id;

    @JsonProperty("ref_id")
    private String refId;

    @JsonProperty("ref_type")
    private String refType;

    @JsonProperty("img_url")
    private String imgUrl;

    @JsonProperty("img_width")
    private String imgWidth;

    @JsonProperty("img_height")
    private String imgHeight;

    @JsonProperty("create_time")
    private String createTime;

    @JsonProperty("update_time")
    private String updateTime;
}
