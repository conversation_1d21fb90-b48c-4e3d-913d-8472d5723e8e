package com.zainanjing.zijin.api.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.DateUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.zijin.api.converter.BcApiConverter;
import com.zainanjing.zijin.api.dto.*;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.dto.BcGoodsDTO;
import com.zainanjing.zijin.dto.GdmmUsersDTO;
import com.zainanjing.zijin.mapper.BcAnchorNewsMapper;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.mapper.BcGoodsMapper;
import com.zainanjing.zijin.mapper.BcProgramListMapper;
import com.zainanjing.zijin.service.*;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 主播相关接口
 */
@RestController
@RequestMapping("/api-bc/anchor")
public class AnchorController {

    @Resource
    private IBcAnchorService anchorService;

    @Resource
    private IBcFmService bcFmService;

    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcAnchorRelationService bcAnchorRelationService;

    @Resource
    private IBcAnchorFansService bcAnchorFansService;

    @Resource
    private BcGoodsMapper bcGoodsMapper;

    @Resource
    private IBcAnchorNewsService bcAnchorNewsService;

    @Resource
    private IBcAnchorNewsReplyService bcAnchorNewsReplyService;

    @Resource
    private IBcForumService forumService;

    @Resource
    private IBcReplayService replayService;

    /**
     * 主播圈排行榜
     */
    @Anonymous
    @GetMapping("/top")
    public ResResult top(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize) {
        IPage<BcAnchorResDTO> pageRes = anchorService.lambdaQuery().eq(BcAnchor::getIsDel, 0).eq(BcAnchor::getIsShow, 1)
                .orderByDesc(BcAnchor::getTopSortNo).orderByDesc(BcAnchor::getFansNum).orderByAsc(BcAnchor::getId).page(Page.of(page, pageSize, false))
                .convert(x -> BcApiConverter.convert(x));

        if (pageRes.getRecords().size() > 0) {
            Map<Long, List<BcProgram>> allBcNames = bcAnchorRelationService.getAllBcName(null);

            Set<Long> uids = new HashSet<>();
            Set<Long> channelIds = new HashSet<>();
            pageRes.getRecords().forEach(anchor -> {
                uids.add(anchor.getUserId());
                channelIds.add(anchor.getChannelId());
            });
            Map<Long, GdmmUsersDTO> gdmmUsersMap = new HashMap<>();
            if (CollUtil.isNotEmpty(uids)) {
                List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uids", uids));
                gdmmUsersMap = gdmmUsersDTOS.stream().collect(Collectors.toMap(GdmmUsersDTO::getUserId, Function.identity()));
            }
            Map<Long, BcFm> bcFmMap = new HashMap<>();
            if (CollUtil.isNotEmpty(channelIds)) {
                List<BcFm> bcFms = bcFmService.lambdaQuery().in(BcFm::getId, channelIds).list();
                bcFmMap = bcFms.stream().collect(Collectors.toMap(BcFm::getId, Function.identity()));
            }
            for (BcAnchorResDTO anchor : pageRes.getRecords()) {
                if (gdmmUsersMap.containsKey(anchor.getUserId())) {
                    GdmmUsersDTO gdmmUsersDTO = gdmmUsersMap.get(anchor.getUserId());
                    anchor.setAvatar(OSSUtil.getImageURL(gdmmUsersDTO.getAvatar()));
                    anchor.setLevel(gdmmUsersDTO.getMedalLevel());
                }
                if (bcFmMap.containsKey(anchor.getChannelId())) {
                    anchor.setFmName(bcFmMap.get(anchor.getChannelId()).getName());
                }
                if (allBcNames.containsKey(anchor.getUserId())) {
                    anchor.setProgramList(BcApiConverter.convert(allBcNames.get(anchor.getUserId())));
                }
            }

        }

        return ResResult.success(pageRes.getRecords());
    }

    /**
     * 主播圈推荐主播
     */
    @Anonymous
    @GetMapping("/recommend")
    public ResResult recommend(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize) {
        return getRecommend(page, pageSize);
    }

    private ResResult getRecommend(Integer page, Integer pageSize) {
        IPage<BcAnchorResDTO> pageRes = anchorService.lambdaQuery().eq(BcAnchor::getIsDel, 0).eq(BcAnchor::getIsShow, 1)
                .orderByDesc(BcAnchor::getListSortNo).orderByAsc(BcAnchor::getId).page(Page.of(page, pageSize, false))
                .convert(x -> BcApiConverter.convert(x));

        if (pageRes.getRecords().size() > 0) {
            /*
             * 特殊逻辑处理
             * 推荐主播的前4个是固定的，后4个是随机的
             */
            if (pageRes.getRecords().size() > 4 && pageSize == 8) {
                List<BcAnchorResDTO> fixedList = pageRes.getRecords().subList(0, 4);
                Set<Long> anchorUid = fixedList.stream().map(BcAnchorResDTO::getUserId).collect(Collectors.toSet());
                List<BcAnchor> lastFourData = anchorService.lambdaQuery()
                        .eq(BcAnchor::getIsDel, 0).eq(BcAnchor::getIsShow, 1)
                        .notIn(BcAnchor::getUserId, anchorUid)
                        .last("order by rand() limit 4").list();
                fixedList.addAll(BcApiConverter.convertAnchors(lastFourData));
                pageRes.setRecords(fixedList);
            }

            Map<Long, List<BcProgram>> allBcNames = bcAnchorRelationService.getAllBcName(null);

            Set<Long> uids = new HashSet<>();
            Set<Long> channelIds = new HashSet<>();
            pageRes.getRecords().forEach(anchor -> {
                uids.add(anchor.getUserId());
                channelIds.add(anchor.getChannelId());
            });
            Map<Long, GdmmUsersDTO> gdmmUsersMap = new HashMap<>();
            if (CollUtil.isNotEmpty(uids)) {
                List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uids", uids));
                gdmmUsersMap = gdmmUsersDTOS.stream().collect(Collectors.toMap(GdmmUsersDTO::getUserId, Function.identity()));
            }
            Map<Long, BcFm> bcFmMap = new HashMap<>();
            if (CollUtil.isNotEmpty(channelIds)) {
                List<BcFm> bcFms = bcFmService.lambdaQuery().in(BcFm::getId, channelIds).list();
                bcFmMap = bcFms.stream().collect(Collectors.toMap(BcFm::getId, Function.identity()));
            }
            String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
            Set<Long> isFocus = new HashSet<>();
            if (StrUtil.isNotBlank(uid)) {
                bcAnchorFansService.lambdaQuery().eq(BcAnchorFans::getFansId, uid).in(BcAnchorFans::getAnchorId, uids).list().forEach(bcAnchorFans -> {
                    isFocus.add(bcAnchorFans.getAnchorId());
                });
            }

            for (BcAnchorResDTO anchor : pageRes.getRecords()) {
                if (gdmmUsersMap.containsKey(anchor.getUserId())) {
                    GdmmUsersDTO gdmmUsersDTO = gdmmUsersMap.get(anchor.getUserId());
                    anchor.setAvatar(OSSUtil.getImageURL(gdmmUsersDTO.getAvatar()));
                    anchor.setLevel(gdmmUsersDTO.getMedalLevel());
                }
                if (bcFmMap.containsKey(anchor.getChannelId())) {
                    anchor.setFmName(bcFmMap.get(anchor.getChannelId()).getName());
                }
                if (allBcNames.containsKey(anchor.getUserId())) {
                    anchor.setProgramList(BcApiConverter.convert(allBcNames.get(anchor.getUserId())));
                }
                if (isFocus.contains(anchor.getUserId())) {
                    anchor.setIsFocus(1);
                }
            }
        }

        return ResResult.success(pageRes.getRecords());
    }

    /**
     * 电台推荐主播
     */
    @Anonymous
    @GetMapping("/radioRecommendAnchor")
    public ResResult radioRecommendAnchor(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "4") Integer pageSize,
            @RequestParam(value = "type", defaultValue = "1") Integer type) {
        if (ObjUtil.equals(1, type)) {
            IPage<BcAnchorRadioRecommendResDTO> pageRes = anchorService.lambdaQuery()
                    .eq(BcAnchor::getIsRecMain, 1)
                    .eq(BcAnchor::getIsDel, 0).eq(BcAnchor::getIsShow, 1)
                    .orderByDesc(BcAnchor::getMainSortNo).orderByAsc(BcAnchor::getId).page(Page.of(page, pageSize, false))
                    .convert(x -> BcApiConverter.convertAnchorRadioRecommend(x));
            return ResResult.success(pageRes.getRecords());
        } else if (ObjUtil.equals(2, type)) {
            return getRecommend(page, pageSize);
        }
        return ResResult.error("参数错误");
    }

    /**
     * 主播推荐爆品
     */
    @Anonymous
    @GetMapping("/hotSale")
    public ResResult hotSale(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<BcGoodsDTO> bcGoodsDTOIPage = bcGoodsMapper.selectBcGoodsList(PageDTO.of(page, pageSize, false),
                Map.of("isRecommandAnchor", "Y", "isDel", 0, "isShow", "Y"));
        List<BcGoodsResDTO> bcGoodsResDTOS = BcApiConverter.convertGoods(bcGoodsDTOIPage.getRecords());
        return ResResult.success(bcGoodsResDTOS);
    }

    /**
     * 获取主播信息
     */
    @Anonymous
    @GetMapping("/info")
    public ResResult info(@RequestParam("anchorUid") Integer anchorUid) {
        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());

        BcAnchor bcAnchor = anchorService.lambdaQuery().eq(BcAnchor::getIsDel, 0).eq(BcAnchor::getUserId, anchorUid).one();
        if (bcAnchor == null) {
            return ResResult.error("主播不存在");
        }

        BcAnchorResDTO bcAnchorResDTO = BcApiConverter.convert(bcAnchor);

        bcFmService.lambdaQuery().eq(BcFm::getId, bcAnchorResDTO.getChannelId()).oneOpt().ifPresent(bcFm -> bcAnchorResDTO.setFmName(bcFm.getName()));

        List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uid", bcAnchorResDTO.getUserId()));
        if (CollUtil.isNotEmpty(gdmmUsersDTOS)) {
            gdmmUsersDTOS.stream().findFirst().ifPresent(gdmmUsersDTO -> {
                bcAnchorResDTO.setAvatar(OSSUtil.getImageURL(gdmmUsersDTO.getAvatar()));
                bcAnchorResDTO.setLevel(gdmmUsersDTO.getMedalLevel());
            });
        }
        Map<Long, List<BcProgram>> allBcNames = bcAnchorRelationService.getAllBcName(bcAnchorResDTO.getUserId());
        if (allBcNames.containsKey(bcAnchorResDTO.getUserId())) {
            bcAnchorResDTO.setProgramList(BcApiConverter.convert(allBcNames.get(bcAnchorResDTO.getUserId())));
        }

        if (StrUtil.isNotBlank(uid)) {
            if (bcAnchorFansService.lambdaQuery().eq(BcAnchorFans::getFansId, uid)
                    .eq(BcAnchorFans::getAnchorId, bcAnchorResDTO.getUserId()).exists()) {
                bcAnchorResDTO.setIsFocus(1);
            }
        }

        return ResResult.success(bcAnchorResDTO);
    }

    /**
     * 通过bc_anchor的主键id获取主播信息
     */
    @Anonymous
    @GetMapping("/infoById")
    public ResResult infoById(@RequestParam("id") Integer id) {
        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());

        BcAnchor bcAnchor = anchorService.lambdaQuery().eq(BcAnchor::getIsDel, 0).eq(BcAnchor::getId, id).one();
        if (bcAnchor == null) {
            return ResResult.error("主播不存在");
        }

        BcAnchorResDTO bcAnchorResDTO = BcApiConverter.convert(bcAnchor);

        bcFmService.lambdaQuery().eq(BcFm::getId, bcAnchorResDTO.getChannelId()).oneOpt().ifPresent(bcFm -> bcAnchorResDTO.setFmName(bcFm.getName()));

        List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uid", bcAnchorResDTO.getUserId()));
        if (CollUtil.isNotEmpty(gdmmUsersDTOS)) {
            gdmmUsersDTOS.stream().findFirst().ifPresent(gdmmUsersDTO -> {
                bcAnchorResDTO.setAvatar(OSSUtil.getImageURL(gdmmUsersDTO.getAvatar()));
                bcAnchorResDTO.setLevel(gdmmUsersDTO.getMedalLevel());
            });
        }
        Map<Long, List<BcProgram>> allBcNames = bcAnchorRelationService.getAllBcName(bcAnchorResDTO.getUserId());
        if (allBcNames.containsKey(bcAnchorResDTO.getUserId())) {
            bcAnchorResDTO.setProgramList(BcApiConverter.convert(allBcNames.get(bcAnchorResDTO.getUserId())));
        }

        if (StrUtil.isNotBlank(uid)) {
            if (bcAnchorFansService.lambdaQuery().eq(BcAnchorFans::getFansId, uid)
                    .eq(BcAnchorFans::getAnchorId, bcAnchorResDTO.getUserId()).exists()) {
                bcAnchorResDTO.setIsFocus(1);
            }
        }

        return ResResult.success(bcAnchorResDTO);
    }


    @Resource
    private BcAnchorNewsMapper bcAnchorNewsMapper;

    @Resource
    private IBcCommentImageService bcCommentImageService;

    /**
     * 主播动态
     */
    @Anonymous
    @GetMapping("/dynamic")
    public ResResult dynamic(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam("anchorUid") Integer anchorUid) {

        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("anchorUid", anchorUid);
        if (StrUtil.isBlank(uid) || ObjUtil.notEqual(uid, anchorUid.toString())) {
            queryMap.put("visibleType", 1);
        }

        IPage<BcAnchorNews> bcAnchorNewsIPage = bcAnchorNewsMapper
                .selectAnchorNewsPage(Page.of(page, pageSize, false)
                        , queryMap);

        if (bcAnchorNewsIPage.getRecords().size() == 0) {
            return ResResult.success(bcAnchorNewsIPage.getRecords());
        }

        List<BcAnchorNewsResDTO> bcAnchorNewsResDTOS = BcApiConverter.convertNews(bcAnchorNewsIPage.getRecords());
        List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uid", anchorUid));
        Map<Long, List<BcCommentImage>> bcCommentImagesMap = bcCommentImageService.lambdaQuery().eq(BcCommentImage::getRefType, ZijinConstants.ANCHOR_NEWS_IMAGE_TYPE)
                .in(BcCommentImage::getRefId, bcAnchorNewsResDTOS.stream().map(BcAnchorNewsResDTO::getId).collect(Collectors.toList()))
                .list().stream().collect(Collectors.groupingBy(BcCommentImage::getRefId));

        for (BcAnchorNewsResDTO bcAnchorNewsResDTO : bcAnchorNewsResDTOS) {
            if (gdmmUsersDTOS.size() > 0) {
                gdmmUsersDTOS.stream().findFirst().ifPresent(gdmmUsersDTO -> {
                    bcAnchorNewsResDTO.setLevel(gdmmUsersDTO.getMedalLevel());
                });
            }
            if (bcCommentImagesMap.containsKey(Long.valueOf(bcAnchorNewsResDTO.getId()))) {
                bcAnchorNewsResDTO.setNewsContentImages(BcApiConverter.convertImages(bcCommentImagesMap.get(Long.valueOf(bcAnchorNewsResDTO.getId()))));
            }
            dynamicReply(bcAnchorNewsResDTO, 1, 10, uid);
        }


        // 取是否点赞 TODO
//        $record = MyPraiseModel::getRecordByUidIdAndPraiseIdAndType ($params['userId'], $value['id'],
//                MyPraiseModel::TYPE_ANCHOR_NEW);
//        if ($record) {
//            $value['is_like'] = "1";
//        } else {
//            $value['is_like'] = "0";

        return ResResult.success(bcAnchorNewsResDTOS);
    }

    public void dynamicReply(BcAnchorNewsResDTO bcAnchorNewsResDTO, Integer page, Integer pageSize, String uid) {
        // 1.主播可以到动态下的待审核和审核通过的评论
        // 2.普通用户可以看到自己发表的待审核评论和其他审核通过的评论
        // 判断是否是主播
        boolean isAnchor = false;
        if (ObjUtil.equals(bcAnchorNewsResDTO.getAnchorId(), uid)) {
            isAnchor = true;
        }

        IPage<BcAnchorNewsReply> bcAnchorNewsReplyPage = bcAnchorNewsReplyService.lambdaQuery()
                .eq(BcAnchorNewsReply::getNewsId, bcAnchorNewsResDTO.getId())
                .eq(BcAnchorNewsReply::getIsDel, 0)
                .eq(BcAnchorNewsReply::getIsShow, 1)
                .and(isAnchor, wrapper -> {
                    wrapper.in(BcAnchorNewsReply::getIsAudit, Arrays.asList(0, 1));
                }).and(!isAnchor, wrapper -> {
                    wrapper.eq(StrUtil.isNotBlank(uid), BcAnchorNewsReply::getUserId, uid)
                            .or()
                            .eq(BcAnchorNewsReply::getIsAudit, 1);
                }).orderByDesc(BcAnchorNewsReply::getCreateTime).orderByAsc(BcAnchorNewsReply::getId)
                .page(new Page<>(page, pageSize));

        if (bcAnchorNewsReplyPage.getRecords().size() == 0) {
            bcAnchorNewsResDTO.setReply(List.of());
            return;
        }

        Map<Long, List<BcCommentImage>> bcCommentImagesMap = bcCommentImageService.lambdaQuery().eq(BcCommentImage::getRefType, ZijinConstants.ANCHOR_NEWS_REPLY_IMAGE_TYPE)
                .in(BcCommentImage::getRefId, bcAnchorNewsReplyPage.getRecords().stream().map(BcAnchorNewsReply::getId).collect(Collectors.toList()))
                .list().stream().collect(Collectors.groupingBy(BcCommentImage::getRefId));

        List<BcAnchorNewsReplyResDTO> bcAnchorNewsReplyResDTOS = new ArrayList<>();

        for (BcAnchorNewsReply bcAnchorNewsReply : bcAnchorNewsReplyPage.getRecords()) {
            BcAnchorNewsReplyResDTO bcAnchorNewsReplyResDTO = BcApiConverter.convert(bcAnchorNewsReply);
            if (bcCommentImagesMap.containsKey(bcAnchorNewsReply.getId())) {
                List<BcCommentImageResDTO> bcCommentImageResDTOS = BcApiConverter.convertImages(bcCommentImagesMap.get(bcAnchorNewsReply.getId()));
                bcAnchorNewsReplyResDTO.setImages(bcCommentImageResDTOS);
            }
            bcAnchorNewsReplyResDTOS.add(bcAnchorNewsReplyResDTO);
        }

        bcAnchorNewsResDTO.setReply(bcAnchorNewsReplyResDTOS);

        if (bcAnchorNewsReplyPage.getTotal() > pageSize && pageSize != -1) {
            bcAnchorNewsResDTO.setMoreReply(1);
        }

    }

    /**
     * 动态详情（加载更多评论）
     */
    @Anonymous
    @GetMapping("/dynamicDetail")
    public ResResult dynamicDetail(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam("dynamicId") Integer dynamicId,
            @RequestParam(value = "isPage", defaultValue = "0") Integer isPage) {

        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        IPage<BcAnchorNews> bcAnchorNewsIPage = bcAnchorNewsMapper
                .selectAnchorNewsPage(Page.of(1, -1)
                        , Map.of("id", dynamicId));

        if (bcAnchorNewsIPage.getRecords().size() == 0) {
            return ResResult.success();
        }

        BcAnchorNewsResDTO bcAnchorNewsResDTO = BcApiConverter.convert(bcAnchorNewsIPage.getRecords().get(0));
        List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uid", bcAnchorNewsResDTO.getAnchorId()));
        List<BcCommentImage> bcCommentImages = bcCommentImageService.lambdaQuery().eq(BcCommentImage::getRefType, ZijinConstants.ANCHOR_NEWS_IMAGE_TYPE)
                .eq(BcCommentImage::getRefId, bcAnchorNewsResDTO.getId())
                .list();

        if (gdmmUsersDTOS.size() > 0) {
            gdmmUsersDTOS.stream().findFirst().ifPresent(gdmmUsersDTO -> {
                bcAnchorNewsResDTO.setLevel(gdmmUsersDTO.getMedalLevel());
            });
        }
        //TODO 缺少是否点赞
        bcAnchorNewsResDTO.setNewsContentImages(BcApiConverter.convertImages(bcCommentImages));
        dynamicReply(bcAnchorNewsResDTO, page, isPage == 0 ? -1 : pageSize, uid);
        return ResResult.success(bcAnchorNewsResDTO);
    }

    /**
     * 获取动态回复列表
     */
    @Anonymous
    @GetMapping("/moreDynamicComment")
    public ResResult moreDynamicComment(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam("dynamicId") Integer dynamicId,
            @RequestParam(value = "isPage", defaultValue = "0") Integer isPage) {
        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        BcAnchorNewsResDTO bcAnchorNewsResDTO = BcApiConverter.convert(bcAnchorNewsMapper.selectById(dynamicId));
        dynamicReply(bcAnchorNewsResDTO, page, isPage == 0 ? -1 : pageSize, uid);
        return ResResult.success(bcAnchorNewsResDTO.getReply());
    }

    /**
     * 动态点赞 TODO
     */
//    @PostMapping("/dynamicLike")
//    public ResResult dynamicLike(
//            @RequestParam("dynamicId") Integer dynamicId,
//            @RequestParam(value = "operate", defaultValue = "1") Integer operate) {
//        if (dynamicId <= 0) {
//            return ResResult.error("动态ID不能为空");
//        }
//        try {
//            boolean result = userService.praise(MyPraiseModel.TYPE_ANCHOR_NEW, dynamicId, operate);
//            return result ? ResResult.success() : ResResult.error();
//        } catch (Exception e) {
//            return ResResult.error(e.getMessage());
//        }
//    }

    /**
     * 动态评论
     */
    @Log(title = "动态评论", businessType = BusinessType.INSERT)
    @PostMapping("/dynamicComment")
    public ResResult dynamicComment(
            @RequestParam("dynamicId") Long dynamicId,
            @RequestParam("content") String content,
            @RequestParam(value = "isAnonymous", defaultValue = "0") Integer isAnonymous,
            @RequestParam(value = "isAnonymousByTo", defaultValue = "0") Integer isAnonymousByTo,
            @RequestParam(value = "toUserId", required = false) Long toUserId,
            @RequestParam(value = "replyId", defaultValue = "0") Long replyId,
            @RequestParam(value = "picture", required = false) String picture) {

        Long uid = SecurityUtils.getUserId();
        Date now = new Date();
        BcAnchorNewsReply reply = new BcAnchorNewsReply();
        // 构建评论数据
        reply.setCreateTime(now);
        reply.setUpdateTime(now);
        reply.setNewsId(dynamicId);
        reply.setReplyContent(content);
        reply.setReplyTime(now);
        reply.setUserId(uid);
        reply.setToUserId(toUserId);
        reply.setIsAnonymous(isAnonymous);
        reply.setIsAnonymousByTo(isAnonymousByTo);
        reply.setParentEntityId(replyId);
        reply.setImgUrls(picture);

        bcAnchorNewsReplyService.commentAnchorNews(reply);

        // 构建返回数据
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("newsId", reply.getNewsId());
        resultData.put("userId", reply.getUserId());
        resultData.put("userName", reply.getFromUserName());
        resultData.put("isAnonymous", reply.getIsAnonymous());
        resultData.put("isAnonymousByTo", reply.getIsAnonymousByTo());
        resultData.put("replyType", reply.getReplyType());
        resultData.put("toUserId", reply.getToUserId());
        resultData.put("toUserName", reply.getToUserName());
        resultData.put("commentContent", reply.getReplyContent());
        resultData.put("commentId", reply.getId());
        resultData.put("auditStatus", reply.getIsAudit());

        // 获取评论图片
        List<BcCommentImage> images = bcCommentImageService.lambdaQuery().eq(BcCommentImage::getRefType, ZijinConstants.ANCHOR_NEWS_REPLY_IMAGE_TYPE)
                .eq(BcCommentImage::getRefId, reply.getId())
                .list();
        resultData.put("imgList", CollUtil.isEmpty(images) ? List.of() : BcApiConverter.convertImages(images));

        return ResResult.success(resultData);
    }


    /**
     * 删除回复
     */
    @Log(title = "删除回复", businessType = BusinessType.DELETE)
    @PostMapping("/deleteReply")
    public ResResult deleteReply(@RequestParam("replyId") Long replyId) {
        /*
         * 1.主播可以删除自己动态下任何人的回复
         * 2.普通用户只能删除属于自己的回复
         * 3.当前用户是主播，但是这个动态是别人的，回复是自己的，他想删除自己的回复
         */
        Long uid = SecurityUtils.getUserId();
        // 查询回复信息
        BcAnchorNewsReply reply = bcAnchorNewsReplyService.getById(replyId);
        if (reply == null || reply.getIsDel() == 1) {
            return ResResult.error("回复不存在");
        }

        // 查询用户是否是主播
        BcAnchor anchor = anchorService.lambdaQuery()
                .eq(BcAnchor::getUserId, uid)
                .eq(BcAnchor::getIsDel, 0)
                .one();
        boolean isAnchor = anchor != null;

        // 权限判断
        if (isAnchor) {
            // 主播 - 检查是否是自己的动态或自己的回复
            BcAnchorNews news = bcAnchorNewsService.getById(reply.getNewsId());
            if (!uid.equals(news.getAnchorId()) && !uid.equals(reply.getUserId())) {
                return ResResult.error("无权删除该回复");
            }
        } else {
            // 普通用户 - 只能删除自己的回复
            if (!uid.equals(reply.getUserId())) {
                return ResResult.error("无权删除该回复");
            }
        }

        // 开始事务处理
        boolean success = bcAnchorNewsReplyService.deleteNewsReply(replyId);
        return success ? ResResult.success() : ResResult.error();
    }

    /**
     * 删除动态(仅主播自己可操作)
     */
    @Log(title = "删除动态", businessType = BusinessType.DELETE)
    @PostMapping("/deleteDynamic")
    public ResResult deleteDynamic(
            @RequestParam(value = "type", defaultValue = "1") Integer type,
            @RequestParam("dynamicId") Long dynamicId,
            @RequestParam(value = "visibleValue", required = false) Integer visibleValue) {
        Long uid = SecurityUtils.getUserId();
        BcAnchorNews news = bcAnchorNewsService.getById(dynamicId);
        if (news == null || !uid.equals(news.getAnchorId())) {
            return ResResult.error("动态不存在！");
        }

        boolean result;
        if (type == 1) {
            // 删除动态
            result = bcAnchorNewsService.removeById(dynamicId);
        } else {

            if (ObjUtil.isNull(visibleValue)) {
                return ResResult.error("参数异常");
            }
            result = bcAnchorNewsService.lambdaUpdate().eq(BcAnchorNews::getId, dynamicId)
                    .eq(BcAnchorNews::getAnchorId, uid)
                    .set(BcAnchorNews::getVisibleType, visibleValue)
                    .set(BcAnchorNews::getUpdateTime, new Date())
                    .update();
        }
        return result ? ResResult.success() : ResResult.error();
    }

    /**
     * 动态一键审核
     */
    @Log(title = "删除动态", businessType = BusinessType.AUDIT)
    @PostMapping("/keyAudit")
    public ResResult keyAudit(@RequestParam("dynamicId") Long dynamicId) {

        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());

        BcAnchorNews news = bcAnchorNewsService.getById(dynamicId);
        if (news == null || !uid.equals(news.getAnchorId())) {
            return ResResult.error("动态不存在或无权操作");
        }
        // 批量审核该动态下的所有评论
        boolean result = bcAnchorNewsReplyService.lambdaUpdate().eq(BcAnchorNewsReply::getNewsId, dynamicId)
                .eq(BcAnchorNewsReply::getIsDel, 0)
                .eq(BcAnchorNewsReply::getIsAudit, 0)
                .set(BcAnchorNewsReply::getIsAudit, 1)
                .set(BcAnchorNewsReply::getAuditTime, new Date())
                .update();

        return ResResult.success();
    }


    /**
     * 主播有货
     */
    @Anonymous
    @GetMapping("/goods")
    public ResResult goods(
            @RequestParam("anchorUid") Integer anchorUid,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        if (anchorUid == null || anchorUid <= 0) {
            return ResResult.success(List.of());
        }

        IPage<BcGoodsDTO> bcGoodsDTOIPage = bcGoodsMapper.selectBcGoodsList(PageDTO.of(page, pageSize, false),
                Map.of("anchorUid", anchorUid, "isDel", 0, "isShow", "Y"));
        List<BcGoodsResDTO> bcGoodsResDTOS = BcApiConverter.convertGoods(bcGoodsDTOIPage.getRecords());
        return ResResult.success(bcGoodsResDTOS);
    }

    @Resource
    private IBcChoiceAlbumService bcChoiceAlbumService;

    /**
     * 主播声优（专辑）
     */
    @Anonymous
    @GetMapping("/collection")
    public ResResult collection(
            @RequestParam("anchorUid") Integer anchorUid,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        if (anchorUid == null || anchorUid <= 0) {
            return ResResult.success(List.of());
        }
        Page<BcChoiceAlbum> pageRes = bcChoiceAlbumService.lambdaQuery().eq(BcChoiceAlbum::getAnchorUid, anchorUid)
                .eq(BcChoiceAlbum::getIsShow, 1)
                .eq(BcChoiceAlbum::getStatus, 1)
                .orderByDesc(BcChoiceAlbum::getSort).orderByDesc(BcChoiceAlbum::getId)
                .page(Page.of(page, pageSize, false));
        return ResResult.success(BcApiConverter.convertChoiceAlbums(pageRes.getRecords()));
    }

    @Resource
    private BcProgramListMapper bcProgramListMapper;

    /**
     * 主播声优（节目）
     */
    @Anonymous
    @GetMapping("/program")
    public ResResult program(
            @RequestParam("anchorUid") Integer anchorUid,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        if (anchorUid == null || anchorUid <= 0) {
            return ResResult.success(List.of());
        }

        List<Long> bcIds = bcAnchorRelationService.lambdaQuery().eq(BcAnchorRelation::getAnchorId, anchorUid).select(BcAnchorRelation::getBcId)
                .list().stream().map(BcAnchorRelation::getBcId).collect(Collectors.toList());

        if (CollUtil.isEmpty(bcIds)) {
            return ResResult.success(List.of());
        }
        List<BcProgramList> bcProgramLists = bcProgramListMapper.selectProgramLiveList(bcIds);
        Integer programListSize = bcProgramLists.size();
        if (page > 1) {
            bcProgramLists.clear();
        }

        if (page > 1 || programListSize < pageSize) {

            //获取录播
            Set<String> codes = forumService.lambdaQuery()
                    .in(BcForum::getProgramId, bcIds).eq(BcForum::getStatus, 0).eq(BcForum::getIsShow, 1)
                    .select(BcForum::getCode).list().stream().map(BcForum::getCode).collect(Collectors.toSet());

            if (CollUtil.isNotEmpty(codes)) {

                // 获取回放节目
                Integer start = pageSize * (page - 1) - programListSize;
                Integer size = pageSize;
                if (page == 1) {
                    start = 0;
                    size = pageSize - programListSize;
                }
                List<BcProgramList> bcProgramListsReply = bcProgramListMapper.selectProgramReplayList(start, size, codes);
                if (CollUtil.isNotEmpty(bcProgramListsReply)) {
                    bcProgramLists.addAll(bcProgramListsReply);
                }
            }
        }


        // 获取主播列表
        List<BcAnchorRelation> bcAnchorRelations = bcAnchorRelationService.lambdaQuery().in(BcAnchorRelation::getBcId, bcIds).list();
        List<Long> uids = bcAnchorRelations.stream().map(BcAnchorRelation::getAnchorId).collect(Collectors.toList());
        List<BcAnchor> bcAnchors = CollUtil.isEmpty(uids) ? List.of() : anchorService.lambdaQuery().in(BcAnchor::getUserId, uids).list();

        Map<Long, GdmmUsersDTO> gdmmUsersMap = new HashMap<>();
        if (CollUtil.isNotEmpty(uids)) {
            List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uids", uids));
            gdmmUsersMap = gdmmUsersDTOS.stream().collect(Collectors.toMap(GdmmUsersDTO::getUserId, Function.identity()));
        }

        JSONArray bcInfo = new JSONArray();
        for (BcProgramList bcProgramList : bcProgramLists) {
            JSONObject res = new JSONObject();
            res.put("bc_id", bcProgramList.getId());
            res.put("bc_name", bcProgramList.getProgramName());
            res.put("bc_img_url", bcProgramList.getLogo());
            res.put("bc_description", bcProgramList.getDescription());
            res.put("forum_id", bcProgramList.getForumId());
            res.put("bc_play_start_time", bcProgramList.getStartTime());
            res.put("bc_play_end_time", bcProgramList.getEndTime());
            res.put("ch_name", bcProgramList.getFmName());
            res.put("ch_id", bcProgramList.getFmId());
            if (ObjUtil.equals(bcProgramList.getStatus(), 0)) {
                res.put("bc_status", 0);
                res.put("bc_play_date", DateUtil.format(new Date(), "yyyy-MM-dd"));
            } else if (ObjUtil.equals(bcProgramList.getStatus(), 1)) {
                res.put("bc_status", 1);
                res.put("bc_play_date", bcProgramList.getStartDate());
            }

            res.put("bc_url", bcProgramList.getFmUrl());
            Set<Long> anchorIds = bcAnchorRelations
                    .stream().filter(bcAnchorRelation -> bcAnchorRelation.getBcId().equals(bcProgramList.getId()))
                    .map(BcAnchorRelation::getAnchorId).collect(Collectors.toSet());
            List<BcAnchor> anchors = bcAnchors.stream().filter(bcAnchor -> anchorIds.contains(bcAnchor.getUserId())).collect(Collectors.toList());
            List<BcAnchorResDTO> bcAnchorResDTOS = new ArrayList<>();
            if (CollUtil.isNotEmpty(anchors)) {
                for (BcAnchor anchor : anchors) {
                    BcAnchorResDTO bcAnchorResDTO = BcApiConverter.convert(anchor);
                    if (gdmmUsersMap.containsKey(anchor.getUserId())) {
                        GdmmUsersDTO gdmmUsersDTO = gdmmUsersMap.get(anchor.getUserId());
                        bcAnchorResDTO.setAvatar(OSSUtil.getImageURL(gdmmUsersDTO.getAvatar()));
                        bcAnchorResDTO.setLevel(gdmmUsersDTO.getMedalLevel());
                    }
                    bcAnchorResDTOS.add(bcAnchorResDTO);
                }
            }
            res.put("anchor", bcAnchorResDTOS);
            bcInfo.add(res);
        }
        return ResResult.success(bcInfo);
    }

    @Resource
    private IBcAnchorImageService bcAnchorImageService;

    /**
     * 主播密界
     */
    @Anonymous
    @GetMapping("/album")
    public ResResult album(
            @RequestParam("anchorUid") Integer anchorUid,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "isPage", defaultValue = "1") Integer isPage) {
        if (anchorUid == null || anchorUid <= 0) {
            return ResResult.success(List.of());
        }
        IPage<BcAnchorImage> bcAnchorImageIPage = bcAnchorImageService.lambdaQuery().eq(BcAnchorImage::getAnchorId, anchorUid)
                .eq(BcAnchorImage::getIsShow, 1)
                .eq(BcAnchorImage::getIsDel, 0)
                .orderByDesc(BcAnchorImage::getSortNo).orderByDesc(BcAnchorImage::getCreateTime).orderByDesc(BcAnchorImage::getId)
                .page(Page.of(page, isPage == 1 ? pageSize : -1, false));
        return ResResult.success(BcApiConverter.convertAnchorImages(bcAnchorImageIPage.getRecords()));
    }

    @Resource
    private IBcAnchorVideoService anchorVideoService;

    /**
     * 主播秀
     */
    @Anonymous
    @GetMapping("/video")
    public ResResult video(
            @RequestParam("anchorUid") Integer anchorUid,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "isPage", defaultValue = "1") Integer isPage) {
        if (anchorUid == null || anchorUid <= 0) {
            return ResResult.success(List.of());
        }
        IPage<BcAnchorVideo> bcAnchorVideoIPage = anchorVideoService.lambdaQuery().eq(BcAnchorVideo::getAnchorId, anchorUid)
                .eq(BcAnchorVideo::getIsShow, 1)
                .eq(BcAnchorVideo::getIsDel, 0)
                .orderByDesc(BcAnchorVideo::getSortNo).orderByDesc(BcAnchorVideo::getCreateTime).orderByDesc(BcAnchorVideo::getId)
                .page(Page.of(page, pageSize, false));
        return ResResult.success(BcApiConverter.convertAnchorVideos(bcAnchorVideoIPage.getRecords()));
    }

    /**
     * 判断当前用是否是主播
     */
    @GetMapping("/isAnchor")
    public ResResult isAnchor() {
        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        BcAnchor one = anchorService.lambdaQuery().eq(BcAnchor::getUserId, uid).one();
        if (one != null) {
            return ResResult.success(Map.of("anchor_id", one.getId(),
                    "is_anchor", 1,
                    "anchor_uid", one.getUserId(),
                    "anchor_name", one.getAnchorName(),
                    "anchor_logo_url", OSSUtil.getImageURL(one.getAnchorImgUrl()),
                    "file_duration", 30));
        } else {
            return ResResult.success(Map.of("anchor_id", 0,
                    "is_anchor", 0,
                    "anchor_uid", 0,
                    "anchor_name", "",
                    "anchor_logo_url", "",
                    "file_duration", 30));
        }
    }

    /**
     * 主播发布动态
     */
    @Log(title = "主播发布动态", businessType = BusinessType.INSERT)
    @PostMapping("/issue")
    public ResResult issue(
            @RequestParam("type") Integer type,
            @RequestParam("content") String content,
            @RequestParam(value = "video", required = false) String video,
            @RequestParam(value = "images", required = false) String images,
            @RequestParam(value = "visibleType", defaultValue = "1") Integer visibleType,
            @RequestParam(value = "fileDuration", defaultValue = "0") Long fileDuration) {
        String uid = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        // 判断用户是否是主播
        BcAnchor anchor = anchorService.lambdaQuery().eq(BcAnchor::getUserId, uid).one();
        if (anchor == null) {
            return ResResult.error("当前用户不是主播，无法发布动态！");
        }

        Date now = new Date();
        BcAnchorNews news = new BcAnchorNews();
        news.setCreateTime(now);
        news.setUpdateTime(now);
        news.setAnchorId(Long.valueOf(uid));
        news.setNewsContent(content);
        news.setVisibleType(visibleType);
        news.setSoureType(1);

        if (type == 1 || type == 3) {
            if (type == 3 && StrUtil.isNotEmpty(video)) {
                news.setFileUrl(video);
                news.setCoverFileUrl(video + "?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast");
                news.setFileDuration(fileDuration);
            }
            bcAnchorNewsService.save(news);
        } else if (type == 2 && StrUtil.isNotEmpty(images)) {
            String[] imageArray = images.split(",");
            List<BcCommentImage> imageList = new ArrayList<>();
            for (String imageUrl : imageArray) {
                BcCommentImage image = new BcCommentImage();
                image.setCreateTime(now);
                image.setUpdateTime(now);
                image.setRefId(news.getId());
                image.setRefType(ZijinConstants.ANCHOR_NEWS_IMAGE_TYPE);
                image.setImgUrl(imageUrl);
                long[] imageWidthAndHeight = OSSUtil.getImageWidthAndHeight(imageUrl);
                image.setImgWidth(imageWidthAndHeight[0]);
                image.setImgHeight(imageWidthAndHeight[1]);
                imageList.add(image);
            }
            bcAnchorNewsService.save(news);
            bcCommentImageService.saveBatch(imageList);
        }

        Map<String, Object> resultInfo = new HashMap<>();
        resultInfo.put("is_anchor", 1);
        resultInfo.put("anchor_uid", anchor.getUserId());
        resultInfo.put("anchor_name", anchor.getAnchorName());
        resultInfo.put("anchor_logo_url", OSSUtil.getImageURL(anchor.getAnchorImgUrl()));
        resultInfo.put("file_duration", 30); // Assuming 30 is the default video duration

        return ResResult.success(resultInfo);
    }
}