<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcMyFavoriteMapper">

    <select id="selectProgramListByUid" resultType="com.zainanjing.zijin.domain.BcForum">
        SELECT f.*
        FROM bc_my_favorite mf
                 INNER JOIN bc_forum f ON mf.favorite_id = f.id
        WHERE mf.user_id = #{uid}
          AND mf.type = 1
          AND f.status = 0
        ORDER BY mf.id DESC
    </select>


    <select id="selectAlbumListByUid" resultType="com.zainanjing.zijin.domain.BcChoiceAlbum">
        SELECT ca.*
        FROM bc_my_favorite mf
                 INNER JOIN bc_choice_album ca ON mf.favorite_id = ca.id
        WHERE mf.user_id = #{uid}
          AND mf.type = 2
          AND ca.status = 1
        ORDER BY mf.id DESC
    </select>

    <select id="selectAlbumEpisodeListByUid" resultType="com.zainanjing.zijin.domain.BcChoice">
        SELECT c.*
        FROM bc_my_favorite mf
                 LEFT JOIN bc_choice c ON mf.favorite_id = c.id
        WHERE mf.user_id = #{uid}
          AND mf.type = 3
          AND c.status = 1
        ORDER BY mf.id DESC
    </select>

</mapper>