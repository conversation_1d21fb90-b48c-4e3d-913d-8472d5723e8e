package com.zainanjing.official.mapper;

import com.zainanjing.official.dto.OfficialAccountDTO;
import com.zainanjing.official.domain.OfficialAccount;
import com.ruoyi.common.core.mapper.GenericMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订阅号Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-08
 */
public interface OfficialAccountMapper extends GenericMapper<OfficialAccount>
{
    List<OfficialAccountDTO> selectAccountById(@Param("ids") List<Long> ids, @Param("cUid")String cUid);

}
