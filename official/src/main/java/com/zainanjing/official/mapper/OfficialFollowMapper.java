package com.zainanjing.official.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.official.domain.OfficialFollow;
import com.zainanjing.official.dto.OfficialAccountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_official_follow(C 端用户 关注订阅号表)】的数据库操作Mapper
 * @createDate 2022-12-12 17:12:14
 * @Entity generator.domain.OfficialFollow
 */
public interface OfficialFollowMapper extends GenericMapper<OfficialFollow> {

    IPage<OfficialAccountDTO> queryOfficialAccounts(IPage page,@Param("p") Map<String, Object> params);

    void updateFollowNum(@Param("id") Long id, @Param("num") int num);

}
