package com.zainanjing.official.mapper;

import com.zainanjing.official.domain.Article;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 新闻Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
public interface ArticleMapper extends GenericMapper<Article> {
    Long updateView(@Param("id") Long id);

    Long updateShare(@Param("id") Long id);

    void updateLike(@Param("id") Long id,@Param("num")  int num);

    void updateOfficialLike(@Param("id") Long id,@Param("num")  int num);

    void updateCollect(@Param("ids") String[] ids, @Param("num") int num);

    void updateCommentNum(@Param("id") Long id,@Param("num")  int num);

    void reCountCommentNum(@Param("id") Long id);

    List<Article> selectSubArticles(IPage page,
                                    @Param("articleId") Long articleId);

    List<Article> selectNotSetPage(IPage page,
                                   @Param("categoryId") Integer categoryId,
                                   @Param("userId") String userId,
                                   @Param("collectionId") Long collectionId);
}
