package com.zainanjing.official.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.OfficialArticleShare;
import com.zainanjing.official.event.ArticleEvent;
import com.zainanjing.official.mapper.ArticleMapper;
import com.zainanjing.official.mapper.OfficialArticleShareMapper;
import com.zainanjing.official.service.IOfficialArticleShareService;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description 针对表【t_official_article_share】的数据库操作Service实现
 * @createDate 2023-03-21 15:45:34
 */
@Service
public class OfficialArticleShareServiceImpl extends GenericCurdServiceImpl<OfficialArticleShareMapper, OfficialArticleShare>
        implements IOfficialArticleShareService {

    @Resource
    private ArticleMapper articleMapper;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAndEvent(OfficialArticleShare officialArticleShare) {
        if (articleMapper.updateShare(officialArticleShare.getArticleId()) > 0) {
            OfficialArticleShare officialArticleShareNew = new OfficialArticleShare();
            officialArticleShareNew.setArticleId(officialArticleShare.getArticleId());
            officialArticleShareNew.setcUserId(officialArticleShare.getcUserId());
            if (this.save(officialArticleShare)) {
                //发送事件
                Article article = new Article();
                article.setId(officialArticleShare.getArticleId());
                applicationEventPublisher.publishEvent(new ArticleEvent(this, article,
                        EventAction.builder().operator(SecurityUtils.getLoginUser()).type(BusinessType.SHARE).build()));
                return true;
            }
        }
        return false;
    }
}
