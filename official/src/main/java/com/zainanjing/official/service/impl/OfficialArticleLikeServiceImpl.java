package com.zainanjing.official.service.impl;

import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.OfficialArticleLike;
import com.zainanjing.official.event.ArticleEvent;
import com.zainanjing.official.mapper.ArticleMapper;
import com.zainanjing.official.mapper.OfficialArticleLikeMapper;
import com.zainanjing.official.service.IOfficialArticleLikeService;
import com.zainanjing.official.service.IOfficialOperLogService;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【t_official_article_like】的数据库操作Service实现
 * @createDate 2023-03-01 09:26:48
 */
@Service
public class OfficialArticleLikeServiceImpl extends GenericCurdServiceImpl<OfficialArticleLikeMapper, OfficialArticleLike>
        implements IOfficialArticleLikeService {

    @Resource
    private ArticleMapper articleMapper;

    @Resource
    private IOfficialOperLogService officialOperLogService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public boolean like(OfficialArticleLike officialArticleLike) {
        officialArticleLike.setcUserId(StrUtil.toStringOrNull(SecurityUtils.getUserId()));
        Article article = articleMapper.selectById(officialArticleLike.getArticleId());
        if(Objects.isNull(article)){
            return false;
        }
        if (this.save(officialArticleLike)) {
            articleMapper.updateLike(officialArticleLike.getArticleId(), 1);
            articleMapper.updateOfficialLike(article.getOfficialAccountId(), 1);
            officialOperLogService.saveLog(officialArticleLike.getcUserId(), officialArticleLike.getArticleId(), null, 5);
            applicationEventPublisher.publishEvent(new ArticleEvent(this, article,
                    EventAction.builder().operator(SecurityUtils.getLoginUser()).type(BusinessType.LIKE).build()));
            return true;
        }
        return false;
    }
}
