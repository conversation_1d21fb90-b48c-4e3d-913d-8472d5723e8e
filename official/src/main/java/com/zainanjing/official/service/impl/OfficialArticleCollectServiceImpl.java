package com.zainanjing.official.service.impl;

import com.zainanjing.official.domain.OfficialArticleCollect;
import com.zainanjing.official.mapper.OfficialArticleCollectMapper;
import com.zainanjing.official.service.IOfficialArticleCollectService;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_official_article_collect】的数据库操作Service实现
* @createDate 2023-02-24 11:46:21
*/
@Service
public class OfficialArticleCollectServiceImpl extends GenericCurdServiceImpl<OfficialArticleCollectMapper, OfficialArticleCollect>
implements IOfficialArticleCollectService {

}
