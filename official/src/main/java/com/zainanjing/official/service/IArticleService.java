package com.zainanjing.official.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.official.domain.Article;

import java.util.List;

/**
 * 新闻Service接口
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
public interface IArticleService extends GenericCurdService<Article> {
    void updateView(Long id);

    Long updateShare(Long id);

    void updateLikeNum(long id, int num);

    void updateOfficialLike(long id, int num);

    void updateCollect(String ids, int num);

    void reCountCommentNum(long id);

    void updateCommentNum(long id, int num);

    Page<Article> selectNotSetPage(int pageNum, int pageSize, Integer categoryId, String userId, Long collectionId);

    Page<Article> getSubArticles(int pageNum,
                                 int pageSize,
                                 Long articleId);

    Long sumView(Long view, Long initView);

    /**
     * 更新同时发送事件
     */
    boolean updateByIdAndEvent(Article entity);

    /**
     * 删除同时发送事件
     */
    boolean removeByIdsAndEvent(List<Long> list);

    /**
     * 保存同时发送事件
     */
    boolean saveAndEvent(Article entity);

    /**
     * 审批同时触发事件
     */
    boolean auditAndEvent(Article entity);


}
