package com.zainanjing.official.service.impl;

import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.official.domain.ArticleComment;
import com.zainanjing.official.domain.OfficialCommentLike;
import com.zainanjing.official.event.OfCommentEvent;
import com.zainanjing.official.mapper.ArticleCommentMapper;
import com.zainanjing.official.mapper.OfficialCommentLikeMapper;
import com.zainanjing.official.service.IOfficialCommentLikeService;
import com.zainanjing.official.service.IOfficialOperLogService;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 评论点赞Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-27
 */
@Service
public class OfficialCommentLikeServiceImpl extends GenericCurdServiceImpl<OfficialCommentLikeMapper, OfficialCommentLike>
        implements IOfficialCommentLikeService {

    @Resource
    private IOfficialOperLogService officialOperLogService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private ArticleCommentMapper articleCommentMapper;

    @Override
    public boolean like(OfficialCommentLike officialCommentLike) {
        officialCommentLike.setUid(StrUtil.toStringOrNull(SecurityUtils.getUserId()));
        if (this.save(officialCommentLike)) {
            articleCommentMapper.update(new LambdaUpdateWrapper<ArticleComment>().eq(ArticleComment::getId, officialCommentLike.getCommentId())
                    .setIncrBy(ArticleComment::getLikeNum, 1));

            officialOperLogService.saveLog(officialCommentLike.getUid(), officialCommentLike.getCommentId(), null, 7);
            ArticleComment articleComment = new ArticleComment();
            articleComment.setId(officialCommentLike.getCommentId());
            applicationEventPublisher.publishEvent(new OfCommentEvent(this, articleComment,
                    EventAction.builder().operator(SecurityUtils.getLoginUser()).type(BusinessType.LIKE).build()));
            return true;
        }
        return false;
    }
}
