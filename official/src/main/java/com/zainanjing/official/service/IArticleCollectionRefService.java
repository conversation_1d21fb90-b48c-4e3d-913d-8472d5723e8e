package com.zainanjing.official.service;

import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.ArticleCollectionRef;
import com.ruoyi.common.core.service.GenericCurdService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_article_collection_ref(新闻表)】的数据库操作Service
 * @createDate 2023-04-03 16:59:42
 */
public interface IArticleCollectionRefService extends GenericCurdService<ArticleCollectionRef> {

    List<ArticleCollectionRef> getCollectInfoByArticles(List<Article> articles);

    Long getCollectViewedByArticleId(Long articleId);

    void deleteByArticles(List<Article> articles);

}
