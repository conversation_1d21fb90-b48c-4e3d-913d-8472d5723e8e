package com.zainanjing.official.service;

import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.OfficialArticleShare;
import com.ruoyi.common.core.service.GenericCurdService;

/**
* <AUTHOR>
* @description 针对表【t_official_article_share】的数据库操作Service
* @createDate 2023-03-21 15:45:34
*/
public interface IOfficialArticleShareService extends GenericCurdService<OfficialArticleShare> {
    /**
     * 保存同时发送事件
     */
    boolean saveAndEvent(OfficialArticleShare officialArticleShare);
}
