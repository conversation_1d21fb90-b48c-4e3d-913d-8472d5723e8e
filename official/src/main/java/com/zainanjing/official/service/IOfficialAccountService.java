package com.zainanjing.official.service;

import com.zainanjing.official.dto.OfficialAccountDTO;
import com.zainanjing.official.domain.OfficialAccount;
import com.ruoyi.common.core.service.GenericCurdService;

import java.util.List;

/**
 * 订阅号Service接口
 * 
 * <AUTHOR>
 * @date 2022-12-08
 */
public interface IOfficialAccountService extends GenericCurdService<OfficialAccount>
{
    List<OfficialAccountDTO> selectAccountById(List<Long> id, String cUid);

    boolean remove(List<Long> ids);

    void logOff(String userId);
}
