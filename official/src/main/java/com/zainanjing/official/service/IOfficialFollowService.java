package com.zainanjing.official.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.official.domain.OfficialFollow;
import com.zainanjing.official.dto.OfficialAccountDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_official_follow(C 端用户 关注订阅号表)】的数据库操作Service
 * @createDate 2022-12-12 17:06:51
 */
public interface IOfficialFollowService extends GenericCurdService<OfficialFollow> {

    /**
     *
     */
    IPage<OfficialAccountDTO> queryOfficialAccounts(IPage page, Map<String, Object> params);

    void updateFollowNum(long id, int num);

}
