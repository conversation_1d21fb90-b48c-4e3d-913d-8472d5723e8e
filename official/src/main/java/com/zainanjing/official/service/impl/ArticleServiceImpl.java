package com.zainanjing.official.service.impl;

import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.OfficialArticleView;
import com.zainanjing.official.event.ArticleEvent;
import com.zainanjing.official.mapper.ArticleMapper;
import com.zainanjing.official.service.IArticleService;
import com.zainanjing.official.service.IOfficialArticleViewService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 新闻Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
@Service
public class ArticleServiceImpl extends GenericCurdServiceImpl<ArticleMapper, Article>
        implements IArticleService {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private IOfficialArticleViewService officialArticleViewService;

    @Override
    @Async
    public void updateView(Long id) {
        OfficialArticleView officialArticleView = new OfficialArticleView();
        officialArticleView.setArticleId(id);
        officialArticleViewService.save(officialArticleView);
        this.baseMapper.updateView(id);
    }

    @Override
    public Long updateShare(Long id) {
        return this.baseMapper.updateShare(id);
    }

    @Override
    public void updateLikeNum(long id, int num) {
        this.baseMapper.updateLike(id, num);
    }

    @Override
    public void updateOfficialLike(long id, int num) {
        this.baseMapper.updateOfficialLike(id, num);
    }

    @Override
    public void updateCollect(String ids, int num) {
        if (StrUtil.isNotEmpty(ids)) {
            this.baseMapper.updateCollect(ids.split(","), num);
        }
    }

    @Override
    @Async
    public void updateCommentNum(long id, int num) {
        this.baseMapper.updateCommentNum(id, num);
    }

    @Override
    public Page<Article> selectNotSetPage(int pageNum, int pageSize, Integer categoryId, String userId, Long collectionId) {
        Page pagination = new Page(pageNum, pageSize);
        List<Article> articles = this.baseMapper.selectNotSetPage(pagination, categoryId, userId, collectionId);
        pagination.setRecords(articles);
        return pagination;
    }

    @Override
    public Page<Article> getSubArticles(int pageNum,
                                        int pageSize,
                                        Long articleId) {

        Page pagination = new Page(pageNum, pageSize,false);
        List<Article> articles = this.baseMapper.selectSubArticles(pagination, articleId);
        pagination.setRecords(articles);
        return pagination;

    }

    @Override
    @Async
    public void reCountCommentNum(long id) {
        this.baseMapper.reCountCommentNum(id);
    }

    public Long sumView(Long view, Long initView) {
        if (view == null) {
            view = 0L;
        }
        if (initView == null) {
            initView = 0L;
        }
        return view + initView;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByIdAndEvent(Article entity) {
        if (super.updateById(entity)) {
            applicationEventPublisher.publishEvent(new ArticleEvent(this, this.getById(entity.getId()),
                    EventAction.builder().type(BusinessType.UPDATE).build()));
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAndEvent(Article entity) {
        if (super.save(entity)) {
            applicationEventPublisher.publishEvent(new ArticleEvent(this, entity, EventAction.builder().type(BusinessType.INSERT).build()));
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditAndEvent(Article entity) {
        if (this.lambdaUpdate().set(Article::getStatus, entity.getStatus())
                .set(Article::getRejectReason, entity.getRejectReason())
                .eq(Article::getId, entity.getId()).update()) {
            applicationEventPublisher.publishEvent(new ArticleEvent(this, entity, EventAction.builder().type(BusinessType.AUDIT).build()));
            return true;
        }
        return false;
    }



    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsAndEvent(List<Long> ids) {
        if (this.lambdaUpdate().set(Article::getStatus, 1)
                .in(Article::getId, ids).update()) {
            applicationEventPublisher.publishEvent(new ArticleEvent(this, ids, EventAction.builder().type(BusinessType.DELETE).build()));
            return true;
        }
        return false;
    }

}
