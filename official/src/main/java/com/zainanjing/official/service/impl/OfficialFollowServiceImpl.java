package com.zainanjing.official.service.impl;

import com.zainanjing.official.dto.OfficialAccountDTO;
import com.zainanjing.official.domain.OfficialFollow;
import com.zainanjing.official.mapper.OfficialFollowMapper;
import com.zainanjing.official.service.IOfficialFollowService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_official_follow(C 端用户 关注订阅号表)】的数据库操作Service实现
 * @createDate 2022-12-12 17:12:14
 */
@Service
public class OfficialFollowServiceImpl extends GenericCurdServiceImpl<OfficialFollowMapper, OfficialFollow> implements IOfficialFollowService {

    @Override
    public IPage<OfficialAccountDTO> queryOfficialAccounts(IPage page, Map<String, Object> params) {
        return this.baseMapper.queryOfficialAccounts(page,params);
    }

    @Override
    public void updateFollowNum(long id,int num) {
        this.baseMapper.updateFollowNum(id, num);
    }
}
