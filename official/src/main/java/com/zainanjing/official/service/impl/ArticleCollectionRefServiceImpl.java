package com.zainanjing.official.service.impl;

import com.zainanjing.official.constant.ArticleConstant;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.ArticleCollectionRef;
import com.zainanjing.official.mapper.ArticleCollectionRefMapper;
import com.zainanjing.official.service.IArticleCollectionRefService;
import com.zainanjing.official.service.IArticleService;
import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_article_collection_ref(新闻表)】的数据库操作Service实现
 * @createDate 2023-04-03 16:59:42
 */
@Service
public class ArticleCollectionRefServiceImpl extends GenericCurdServiceImpl<ArticleCollectionRefMapper, ArticleCollectionRef>
        implements IArticleCollectionRefService {
    @Resource
    private IArticleService articleService;

    @Override
    public List<ArticleCollectionRef> getCollectInfoByArticles(List<Article> articles) {
        //视频类型的文章寻找是否是属于集合
        List<Long> subArticles = articles.stream().filter(x -> ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())).map(Article::getId).collect(Collectors.toList());
        List<ArticleCollectionRef> articleCollectionRefs = this.list(new QueryWrapper<ArticleCollectionRef>().in(CollUtil.isNotEmpty(subArticles),"sub_article_id", subArticles));
        List<ArticleCollectionRef> articleCollectionRefsResult = new ArrayList<>();
        if (CollectionUtils.isEmpty(articleCollectionRefs)) {
            return articleCollectionRefs;
        }

        Set<Long> collect = articleCollectionRefs.stream().map(ArticleCollectionRef::getArticleId).collect(Collectors.toSet());
        //有视频属于集合
        Map<Long, String> idTitleMap = articleService.list(new QueryWrapper<Article>().select("id,title").in("id", collect).eq("status", ArticleConstant.ARTICLE_STATUS_APPROVE)).stream().collect(Collectors.toMap(Article::getId, Article::getTitle));
        for (ArticleCollectionRef x : articleCollectionRefs) {
            if (idTitleMap.get(x.getArticleId()) == null) {
                continue;
            }
            x.setCollectionTitle(idTitleMap.get(x.getArticleId()));
            articleCollectionRefsResult.add(x);
        }
        return articleCollectionRefsResult;
    }

    @Override
    public Long getCollectViewedByArticleId(Long articleId) {
        List<ArticleCollectionRef> articleCollectionRefs = this.list(new QueryWrapper<ArticleCollectionRef>().eq("article_id", articleId));
        if (CollUtil.isNotEmpty(articleCollectionRefs)) {
            List<Long> subArticleIds = articleCollectionRefs.stream().map(ArticleCollectionRef::getSubArticleId).collect(Collectors.toList());
            List<Object> obect = articleService.listObjs(new QueryWrapper<Article>().select("sum(viewed+init_view)").in("id", subArticleIds).eq("status", ArticleConstant.ARTICLE_STATUS_APPROVE));
            if(CollUtil.isNotEmpty(obect)) {
                return Long.valueOf(obect.get(0).toString());
            }
        }
        return 0L;
    }

    @Async
    @Override
    public void deleteByArticles(List<Article> articles) {
        List<Long> collectIds = articles.stream().filter(x -> x.getType().equals(ArticleConstant.ARTICLE_TYPE_COLLECT)).map(Article::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collectIds)) {
            //如果是合集，那么删除合集的文章关联关系
            QueryWrapper<ArticleCollectionRef> ew1 = new QueryWrapper<>();
            ew1.in("article_id", collectIds);
            this.remove(ew1);
        }
        List<Long> subArticleIds = articles.stream().filter(x -> x.getType().equals(ArticleConstant.ARTICLE_TYPE_VIDEO)).map(Article::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(subArticleIds)) {
            //如果是视频，那么删除视频的集合关联关系
            QueryWrapper<ArticleCollectionRef> ew2 = new QueryWrapper<>();
            ew2.in("sub_article_id", subArticleIds);
            this.remove(ew2);
        }
    }

}
