package com.zainanjing.official.service.impl;

import com.zainanjing.official.domain.OfficialOperLog;
import com.zainanjing.official.mapper.OfficialOperLogMapper;
import com.zainanjing.official.service.IOfficialOperLogService;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【t_official_oper_log(C端用户操作日志，关注、收藏、点赞)】的数据库操作Service实现
 * @createDate 2023-03-24 14:46:02
 */
@Service
public class OfficialOperLogServiceImpl extends GenericCurdServiceImpl<OfficialOperLogMapper, OfficialOperLog>
        implements IOfficialOperLogService {

    @Async
    @Override
    public void saveLog(String cUserId, Long articleId, Long officialId, Integer operType) {
        OfficialOperLog officialOperLog = new OfficialOperLog();
        officialOperLog.setOfficialId(officialId);
        officialOperLog.setOperType(operType);
        officialOperLog.setcUserId(cUserId);
        officialOperLog.setArticleId(articleId);
        save(officialOperLog);
    }
}
