package com.zainanjing.official.service.impl;

import com.ruoyi.common.utils.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.base.BaseException;
import com.ruoyi.ext.push.PushOperate;
import com.ruoyi.ext.push.PushTagCategory;
import com.ruoyi.ext.push.PushType;
import com.zainanjing.official.domain.OfficialAccount;
import com.zainanjing.official.domain.OfficialAccountApply;
import com.zainanjing.official.mapper.OfficialAccountApplyMapper;
import com.zainanjing.official.service.IOfficialAccountApplyService;
import com.zainanjing.official.service.IOfficialAccountService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_official_account_apply(订阅号申请表)】的数据库操作Service实现
 * @createDate 2023-02-21 16:20:39
 */
@Service
public class OfficialAccountApplyServiceImpl extends GenericCurdServiceImpl<OfficialAccountApplyMapper, OfficialAccountApply>
        implements IOfficialAccountApplyService {

    @Autowired
    private IOfficialAccountService iOfficialAccountService;

    @Resource
    private PushOperate pushOperate;

    @Override
    public Boolean approveApply(OfficialAccountApply officialAccountApply) {
        //数据判断
        OfficialAccountApply officialAccountApplyUpdate = new OfficialAccountApply();

        OfficialAccountApply officialAccountApplyOld = this.getById(officialAccountApply.getId());
        if (officialAccountApplyOld == null) {
            throw new ServiceException("该申请不存在");
        }
        if (officialAccountApplyOld.getStatus() == 1 || officialAccountApplyOld.getStatus() == 2) {
            throw new ServiceException("该申请已经审核过了");
        }
        //准备数据
        LambdaQueryWrapper<OfficialAccount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OfficialAccount::getApplyId, officialAccountApplyOld.getId());
        List<OfficialAccount> list = iOfficialAccountService.list(lambdaQueryWrapper);
        //逻辑
        if (officialAccountApply.getStatus() == 1) {
            //新增
            if (CollectionUtils.isEmpty(list)) {
                OfficialAccount officialAccount = BeanUtil.copyProperties(officialAccountApplyOld, OfficialAccount.class, "id");
                //officialAccount
                officialAccount.setApplyId(officialAccountApplyOld.getId());
                officialAccount.setCategoryType("1");//通过申请的均为OGC
                iOfficialAccountService.save(officialAccount);
                officialAccountApplyUpdate.setOfficialAccountId(officialAccount.getId());
            } else {
                //修改:只能更新，内容领域；名称；描述；头像
                OfficialAccount officialAccount = list.get(0);
                officialAccount.setCategoryId(officialAccountApplyOld.getCategoryId());
                officialAccount.setLogoImage(officialAccountApplyOld.getLogoImage());
                officialAccount.setName(officialAccountApplyOld.getName());
                officialAccount.setDescription(officialAccountApplyOld.getDescription());
                iOfficialAccountService.updateById(officialAccount);
                officialAccountApplyUpdate.setOfficialAccountId(officialAccount.getId());
            }
            officialAccountApplyUpdate.setReason("");
        } else if (officialAccountApply.getStatus() == 2) {
            officialAccountApplyUpdate.setReason(officialAccountApply.getReason());
            //修改时审核失败的时候，修改数据还原。
            if (!CollectionUtils.isEmpty(list)) {
                OfficialAccount officialAccount = list.get(0);
                officialAccountApplyUpdate.setCategoryId(officialAccount.getCategoryId());
                officialAccountApplyUpdate.setLogoImage(officialAccount.getLogoImage());
                officialAccountApplyUpdate.setName(officialAccount.getName());
                officialAccountApplyUpdate.setDescription(officialAccount.getDescription());
                officialAccountApplyUpdate.setOfficialAccountId(officialAccount.getId());
            }
        }

        //存储信息
        officialAccountApplyUpdate.setId(officialAccountApply.getId());
        officialAccountApplyUpdate.setStatus(officialAccountApply.getStatus());
        boolean isSuccess = this.updateById(officialAccountApplyUpdate);
        if (isSuccess) {
            try {
                if (officialAccountApplyUpdate.getStatus() == 1) {
                    pushOperate.sendOfficialApplyByAliasAndTags(PushType.C, officialAccountApply.getCreateBy(), PushTagCategory.C.name(), "订阅号审核通过，可点击我的-创作者中心进行查看", "");
                } else if (officialAccountApplyUpdate.getStatus() == 2) {
                    pushOperate.sendOfficialApplyByAliasAndTags(PushType.C, officialAccountApply.getCreateBy(), PushTagCategory.C.name(), "订阅号审核不通过，失败原因：" + officialAccountApply.getReason(), officialAccountApply.getReason());
                }
            } catch (Exception e) {
                log.error("推送失败", e);
            }
        }
        return isSuccess;
    }

}
