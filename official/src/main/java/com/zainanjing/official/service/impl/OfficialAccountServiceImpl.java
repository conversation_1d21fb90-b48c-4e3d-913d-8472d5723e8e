package com.zainanjing.official.service.impl;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.OfficialAccount;
import com.zainanjing.official.domain.OfficialAccountApply;
import com.zainanjing.official.dto.OfficialAccountDTO;
import com.zainanjing.official.mapper.OfficialAccountMapper;
import com.zainanjing.official.search.EArticleService;
import com.zainanjing.official.service.IArticleService;
import com.zainanjing.official.service.IOfficialAccountApplyService;
import com.zainanjing.official.service.IOfficialAccountService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订阅号Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Service
@Slf4j
public class OfficialAccountServiceImpl extends GenericCurdServiceImpl<OfficialAccountMapper, OfficialAccount>
        implements IOfficialAccountService {

    @Resource
    private IArticleService iArticleService;

    @Resource
    private EArticleService eArticleService;

    @Resource
    private IArticleService articleService;

    @Lazy
    @Resource
    private IOfficialAccountApplyService officialAccountApplyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(List<Long> ids) {
        LambdaQueryWrapper<Article> wrapperQuery = new LambdaQueryWrapper<>();
        wrapperQuery.select(Article::getId).in(Article::getOfficialAccountId, ids);
        List<Article> list = iArticleService.list(wrapperQuery);
        LambdaUpdateWrapper<OfficialAccount> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OfficialAccount::getDeleted, 1).in(OfficialAccount::getId, ids);
        if (this.update(wrapper)) {
            if (CollUtil.isNotEmpty(list)) {
                //清空关联数据
                LambdaUpdateWrapper<Article> wrapperUpdate = new LambdaUpdateWrapper();
                wrapperUpdate.set(Article::getStatus, 1).in(Article::getOfficialAccountId, ids);
                iArticleService.update(wrapperUpdate);
                //清除ES相关数据
                eArticleService.deletes(list.stream().map(Article::getId).collect(Collectors.toList()));
            }
        }
        return false;
    }


    @Override
    public List<OfficialAccountDTO> selectAccountById(List<Long> ids, String cUid) {
        return this.baseMapper.selectAccountById(ids, cUid);
    }

    /**
     * 注销用户
     *
     * @param userId
     */
    public void logOff(String userId) {

        //注销逻辑
        //删除订阅号，删除订阅号下的文章，清除ES相关信息 -- 这个暂时不动，删除订阅号下的用户订阅关系
        log.info("注销用户开始:{}", userId);
        //获取OGC订阅号
        LambdaQueryWrapper<OfficialAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OfficialAccount::getCreateBy, userId);
        queryWrapper.eq(OfficialAccount::getCategoryType, "1");//只注销OGC相关信息
        OfficialAccount officialAccount = this.getOne(queryWrapper);
        if (officialAccount == null) {
            //查看申请中是否存在
            LambdaQueryWrapper<OfficialAccountApply> queryApplyWrapper = new LambdaQueryWrapper<>();
            queryApplyWrapper.eq(OfficialAccountApply::getCreateBy, userId);
            List<OfficialAccountApply> list = officialAccountApplyService.list(queryApplyWrapper);
            if (CollUtil.isNotEmpty(list)) {
                for (OfficialAccountApply apply : list) {
                    apply.setStatus(2);
                    apply.setReason("用户注销");
                    officialAccountApplyService.updateById(apply);
                }
            }
            return;
        }
        int current = 1;
        while (true) {
            //查询对应资讯
            LambdaQueryWrapper<Article> queryArticle = new LambdaQueryWrapper<>();
            queryArticle.eq(Article::getOfficialAccountId, officialAccount.getId());
            queryArticle.ne(Article::getStatus, 1);//已经删除的信息不在处理
            IPage<Article> page = articleService.page(new Page<>(current, 100), queryArticle);
            if (CollUtil.isEmpty(page.getRecords())) {
                break;
            }
            //删除资讯
            List<Long> removeIds = page.getRecords().stream().map(Article::getId).collect(Collectors.toList());
            eArticleService.deletes(removeIds);
            LambdaUpdateWrapper<Article> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(Article::getStatus, 1);
            updateWrapper.in(Article::getId, removeIds);
            articleService.update(updateWrapper);
            if (page.getRecords().size() < 100) {
                break;
            }
            current++;
        }
        LambdaUpdateWrapper<OfficialAccount> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OfficialAccount::getDeleted, 1).eq(OfficialAccount::getId, officialAccount.getId());
        this.update(updateWrapper);
        log.info("注销用户结束:{}", userId);
    }
}
