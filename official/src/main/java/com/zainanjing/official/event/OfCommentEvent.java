package com.zainanjing.official.event;

import com.ruoyi.common.dto.EventAction;
import com.zainanjing.official.domain.ArticleComment;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class OfCommentEvent extends ApplicationEvent {

    private ArticleComment articleComment;
    private List<Long> ids;
    private EventAction action;

    public OfCommentEvent(Object source, ArticleComment articleComment, EventAction action) {
        super(source);
        this.articleComment = articleComment;
        this.action = action;
    }

    public OfCommentEvent(Object source, List<Long> ids, EventAction action) {
        super(source);
        this.ids = ids;
        this.action = action;
    }

    public ArticleComment getArticleComment() {
        return articleComment;
    }

    public List<Long> getIds() {
        return ids;
    }

    public EventAction getAction() {
        return action;
    }
}