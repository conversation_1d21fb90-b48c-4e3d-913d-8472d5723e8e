package com.zainanjing.official.event;

import com.ruoyi.common.dto.EventAction;
import com.zainanjing.official.domain.Article;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class ArticleEvent extends ApplicationEvent {

    private Article article;
    private List<Long> ids;
    private EventAction action;

    public ArticleEvent(Object source, Article article, EventAction action) {
        super(source);
        this.article = article;
        this.action = action;
    }

    public ArticleEvent(Object source, List<Long> ids, EventAction action) {
        super(source);
        this.ids = ids;
        this.action = action;
    }

    public Article getArticle() {
        return article;
    }

    public List<Long> getIds() {
        return ids;
    }

    public EventAction getAction() {
        return action;
    }
}