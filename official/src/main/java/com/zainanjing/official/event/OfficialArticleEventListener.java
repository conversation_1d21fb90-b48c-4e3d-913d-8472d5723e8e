package com.zainanjing.official.event;

import com.ruoyi.common.utils.BeanUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.dto.EventAction;
import com.zainanjing.official.constant.ArticleConstant;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.ArticleCollectionRef;
import com.zainanjing.official.search.EArticle;
import com.zainanjing.official.search.EArticleService;
import com.zainanjing.official.service.IArticleCollectionRefService;
import com.zainanjing.official.service.IArticleService;
import jakarta.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OfficialArticleEventListener {

    @Resource
    private EArticleService eArticleService;

    @Resource
    private IArticleService articleService;

    @Resource
    private IArticleCollectionRefService iArticleCollectionRefService;

    /**
     * 订阅号监听
     */
    @EventListener
    public void handleArticleEvent(ArticleEvent event) {
        Article article = event.getArticle();
        List<Long> ids = event.getIds();
        EventAction actionType = event.getAction();

        switch (actionType.getType()) {
            case UPDATE:
                if (ObjUtil.equals(article.getEnabled(), 0) && article.getStatus().equals(ArticleConstant.ARTICLE_STATUS_APPROVE) && !ArticleConstant.ARTICLE_TYPE_COLLECT.equals(article.getType())) {
                    EArticle eArticle = BeanUtil.copyProperties(article, EArticle.class);
                    eArticleService.save(eArticle);
                } else {
                    eArticleService.delete(article.getId());
                }
                break;
            case AUDIT:
                if (article.getStatus().equals(0)) {
                    Article articleOld = articleService.getById(article.getId());
                    if (!ArticleConstant.ARTICLE_TYPE_COLLECT.equals(articleOld.getType())) {
                        eArticleService.save(BeanUtil.copyProperties(articleOld, EArticle.class));
                    }
                } else {
                    eArticleService.delete(article.getId());
                }
                break;
            case DELETE:
                iArticleCollectionRefService.remove(new LambdaQueryWrapper<ArticleCollectionRef>().in(ArticleCollectionRef::getSubArticleId, ids));
                iArticleCollectionRefService.remove(new LambdaQueryWrapper<ArticleCollectionRef>().in(ArticleCollectionRef::getArticleId, ids));
                eArticleService.deletes(ids);
                break;
            case INSERT:
                if (article.getStatus().equals(ArticleConstant.ARTICLE_STATUS_APPROVE) && !ArticleConstant.ARTICLE_TYPE_COLLECT.equals(article.getType())) {//集合不进入ES
                    EArticle eArticle = BeanUtil.copyProperties(article, EArticle.class);
                    eArticleService.save(eArticle);
                }
                break;
            default:
                break;
        }
    }
}
