package com.zainanjing.official.event;

import com.ruoyi.common.dto.EventAction;
import com.zainanjing.official.domain.OfficialAccount;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class OfAccountEvent extends ApplicationEvent {

    private OfficialAccount officialAccount;
    private List<Long> ids;
    private EventAction action;

    public OfAccountEvent(Object source, OfficialAccount officialAccount, EventAction action) {
        super(source);
        this.officialAccount = officialAccount;
        this.action = action;
    }

    public OfAccountEvent(Object source, List<Long> ids, EventAction action) {
        super(source);
        this.ids = ids;
        this.action = action;
    }

    public OfficialAccount getOfficialAccount() {
        return officialAccount;
    }

    public List<Long> getIds() {
        return ids;
    }

    public EventAction getAction() {
        return action;
    }
}