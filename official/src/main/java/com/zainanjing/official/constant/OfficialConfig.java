package com.zainanjing.official.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Lazy(false)
@Component
public class OfficialConfig {

    public static String SHARE_IMG_URL;

    @Value("${system.official.share-img-url:}")
    public void setShareImgUrl(String shareImgUrl) {
        SHARE_IMG_URL = shareImgUrl;
    }

    public static String city;

    @Value("${system.city:''}")
    public void setCity(String city) {
        OfficialConfig.city = city;
    }

    public static String apiUrl;

    public String getApiUrl() {
        return apiUrl;
    }

    @Value("${system.api-url:''}")
    public void setApiUrl(String apiUrl) {
        OfficialConfig.apiUrl = apiUrl;
    }
}
