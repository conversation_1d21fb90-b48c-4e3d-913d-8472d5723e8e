package com.zainanjing.official.search;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zainanjing.official.domain.Article;

import java.util.List;

public interface EArticleService {
    void save(EArticle eArticle);

    void delete(Long id);

    void deletes(List<Long> ids);

    void deleteAll();

    IPage<Article> list(int pageNum,
                        int pageSize,
                        String keyWords,
                        Long customerId);
}
