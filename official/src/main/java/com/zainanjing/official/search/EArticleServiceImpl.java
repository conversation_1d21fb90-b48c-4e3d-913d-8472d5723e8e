package com.zainanjing.official.search;

import com.ruoyi.common.utils.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zainanjing.official.constant.OfficialConfig;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.OfficialAccount;
import com.zainanjing.official.service.IArticleService;
import com.zainanjing.official.service.IOfficialAccountService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EArticleServiceImpl implements EArticleService {

    private static final Logger log = LoggerFactory.getLogger(EArticleServiceImpl.class);

    @Resource
    private RestClient client;

    @Resource
    private OfficialConfig officialConfig;

    private String indexName = "bmfw_official_article";

    @PostConstruct
    public void init() {
        indexName = officialConfig.city + "_" + indexName;
        try {
            createIndex();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean indexExists() {
        try {
            Request request = new Request("HEAD", "/" + indexName);
            Response response = client.performRequest(request);
            return response.getStatusLine().getStatusCode() == 200;
        } catch (Exception e) {
            log.error("Failed to check index exists", e);
        }
        return false;
    }

    public void createIndex() throws IOException {
        if (indexExists()) {
            log.info("Index already exists");
            return;
        }

        Request request = new Request("PUT", "/" + indexName);
        String settingsAndMappings = """
                {
                    "settings": {
                        "index": {
                            "number_of_shards": 1,
                            "number_of_replicas": 0
                        }
                    },
                    "mappings": {
                        "properties": {
                            "id": { "type": "long" },
                            "title": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            },
                            "content": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            },
                            "createTime": { "type": "date" }
                        }
                    }
                }
                """;
        request.setJsonEntity(settingsAndMappings);
        Response response = client.performRequest(request);
        if (response.getStatusLine().getStatusCode() != 200) {
            throw new RuntimeException("Failed to create index");
        }
    }

    @Autowired
    private IArticleService iArticleService;

    @Autowired
    @Lazy
    private IOfficialAccountService iOfficialAccountService;

    @Override
    public void save(EArticle eArticle) {
        try {
            Request request = new Request("PUT", "/" + indexName + "/_doc/" + eArticle.getId());
            request.setJsonEntity(JSON.toJSONString(eArticle));
            client.performRequest(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void delete(Long id) {
        try {
            Request request = new Request("DELETE", "/" + indexName + "/_doc/" + id);
            client.performRequest(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void deletes(List<Long> ids) {
        ids.forEach(this::delete);
    }

    @Override
    public void deleteAll() {
        try {
            Request request = new Request("DELETE", "/" + indexName);
            Response response = client.performRequest(request);
            if (response.getStatusLine().getStatusCode() == 200) {
                createIndex();
            }
        } catch (IOException e) {
            log.error("Failed to delete index", e);
        }
    }

    @Override
    public IPage<Article> list(int pageNum, int pageSize, String keyWords, Long customerId) {
        IPage<Article> page = new Page<>(pageNum, pageSize);
        try {
            String query = """
                    {
                        "query": {
                            "bool": {
                                "should": [
                                    { "match_phrase": { "title": "%s" } },
                                    { "match_phrase": { "content": "%s" } }
                                ]
                            }
                        },
                        "sort": [
                            { "createTime": { "order": "desc" } }
                        ],
                        "from": %d,
                        "size": %d
                    }
                    """.formatted(keyWords, keyWords, (pageNum - 1) * pageSize, pageSize);

            Request request = new Request("POST", "/" + indexName + "/_search");
            request.setJsonEntity(query);
            Response response = client.performRequest(request);
            JSONObject resultJson = JSONObject.parseObject(EntityUtils.toString(response.getEntity()));
            JSONObject hitsJson = resultJson.getJSONObject("hits");
            Long total = hitsJson.getJSONObject("total").getLong("value");
            page.setTotal(total);
            page.setPages(pageSize == 0 ? 1 : (int) Math.ceil(total / pageSize));
            List<JSONObject> hits = hitsJson.getList("hits", JSONObject.class);

            if (CollUtil.isNotEmpty(hits)) {
                Set<Long> ids = new HashSet<>();
                hits.forEach(hit -> {
                    ids.add(Long.parseLong(hit.getString("_id")));
                });
                QueryWrapper<Article> ewArticleQuery = new QueryWrapper<>();
                ewArticleQuery.in("id", ids);
                ewArticleQuery.orderByDesc("create_time");
                List<Article> articles = iArticleService.list(ewArticleQuery);
                QueryWrapper<OfficialAccount> oaQuery = new QueryWrapper<>();
                oaQuery.in("id", articles.stream().map(Article::getOfficialAccountId).collect(Collectors.toList()));
                List<OfficialAccount> officialAccountList = iOfficialAccountService.list(oaQuery);
                for (Article x : articles) {
                    x.setShareUrl(officialConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                    Optional<OfficialAccount> temp = officialAccountList.stream().filter(m -> Objects.equals(m.getId(), x.getOfficialAccountId())).findAny();
                    if (temp.isPresent()) {
                        x.setSource(temp.get().getName());
                        x.setSourceImgUrl(temp.get().getLogoImage());
                    }
                    x.setContent(null);
                    x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                }
                page.setRecords(articles);
                return page;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return page;
    }
}
