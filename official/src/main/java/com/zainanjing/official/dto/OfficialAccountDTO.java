package com.zainanjing.official.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class OfficialAccountDTO {

    private static final long serialVersionUID = 1L;

    /** 编号 */
    private Long id;

    /** 分类编号 */
    private Long categoryId;

    /** 名称 */
    private String name;

    /** 订阅号头像 */
    private String logoImage;

    /** 订阅号介绍 */
    private String description;

    /** 热门推荐标志(1为热门推荐) */
    private Integer recommended;

    /** 是否默认 */
    private Integer defaulted;

    /** 关注数量 */
    private Long followNum;

    /** 点赞数量 */
    private Long likeNum;

    /** 1 为关注 **/
    private Integer followed;

    private String categoryName;

    private Integer categoryType;

    /** 申请主体（1:个人/达人;2:机构） */
    private Integer applyType;

    /** 主体描述 */
    private String applyTypeDesc;

    /** 机构名称 */
    private String organName;

    /** 机构性质 */
    private String organType;

    private String organTypeDesc;

    private String createBy;

    /**
     * 是否开启  是否启用1:启用;0:停用
     */
    @JsonIgnore
    private Integer enabled;
}
