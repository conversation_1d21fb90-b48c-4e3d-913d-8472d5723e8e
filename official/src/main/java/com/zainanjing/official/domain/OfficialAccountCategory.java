package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订阅号分类对象 t_official_account_category
 *
 * <AUTHOR>
 * @date 2022-12-08
 */

@Data
@TableName(value = "t_official_account_category")
public class OfficialAccountCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 订阅号分类图
     */
    private String logoImage;

    /**
     * 类型分类
     * （0: PGC；1: OGC）
     */
    private String categoryType;

    /** 热门推荐标志(1为热门推荐) */
    private Integer recommended;

    /**
     * 订阅号描述
     */
    private String description;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 删除标志(1为删除)
     */
    @JsonIgnore
    @TableLogic
    private Integer deleted;

    /**
     * 创建者
     */
    @JsonIgnore
    private String createBy;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 更新者
     */
    @JsonIgnore
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;

}
