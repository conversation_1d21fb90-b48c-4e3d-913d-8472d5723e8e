package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 新闻集合表
 * @TableName t_article_collection_ref
 */
@TableName(value ="t_article_collection_ref")
@Data
public class ArticleCollectionRef implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 资讯ID
     */
    private Long articleId;

    /**
     * 子资讯ID
     */
    private Long subArticleId;

    /**
     * 排序
     */
    private Integer sort;

    @TableField(exist = false)
    private String collectionTitle;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}