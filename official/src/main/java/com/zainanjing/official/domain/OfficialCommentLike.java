package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 评论点赞对象 t_official_comment_like
 *
 * <AUTHOR>
 * @date 2024-08-27
 */
@Data
@TableName(value = "t_official_comment_like")
public class OfficialCommentLike implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * C端用户ID
     */
    private String uid;

    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空")
    private Long commentId;

    /**
     * 点赞时间
     */
    private Date createTime;

}
