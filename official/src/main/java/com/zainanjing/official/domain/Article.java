package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;

import com.ruoyi.common.config.Views;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 新闻对象 t_article
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
@Data
@TableName(value = "t_article")
public class Article implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;


    /**
     * 显示模式 0无图 1普通 2多图 3单图
     */
    private Integer displayMode;

    @TableField(exist = false)
    private String articleCatName;

    /**
     * 类型分类
     * （0: PGC；1: OGC）
     */
    private String categoryType;

    @TableField(exist = false)
    private String officialAccountName;

    /**
     * 作者
     */
    private String author;

    /**
     * 资讯编辑
     */
    private String editor;

    /**
     * 资讯记者
     */
    private String reporter;

    /**
     * 查看次数
     */
    private Long viewed;

    /**
     * 0:显示（审核通过）1:删除 2:草稿 3:待审核 4:审核拒绝
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否开启评论 0否 1是
     */
    private Long isComment;

    /**
     * 是否开启 0开启1关闭
     */
    private Integer enabled;

    /**
     * 分类id
     */
    private Long articleCatId;

    private Long officialAccountId;

    /**
     * 1普通新闻，2视频新闻，3新闻合集，4图文分离模式
     */
    private Integer type;

    /**
     * 内容
     */
    private String content;

    /**
     * 作者邮箱
     */
    @JsonIgnore
    private String authorEmail;

    /**
     * 关键字
     */
    @JsonIgnore
    private String keywords;

    /**
     * 0后台插入,1中搜新闻通过定时任务入库
     */
    @JsonIgnore
    private Long articleType;


    private Integer recommended;

    /**
     * 描述
     */
    private String remark;

    /**
     * 新闻列表图片
     */
    private String imgUrl;

    /**
     * 新闻列表图片-宽
     */
    private Integer imgWidth;

    /**
     * 新闻列表图片-高
     */
    private Integer imgHeight;

    /*
     * 分享的图片地址
     */
    @TableField(exist = false)
    private String shareImgUrl;

    /**
     * 音频链接
     */
    @JsonIgnore
    private String audioUrl;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 视频/音频时间
     */
    private Long duration;

    /**
     * 视频默认图片
     */
    @JsonIgnore
    private String videoImgUrl;

    /**
     * 中搜新闻对应的id
     */
    @JsonIgnore
    private String idForChinaSearch;

    /**
     * 资讯URL(中搜详情等)
     */
    @JsonIgnore
    private String articleDetailUrl;

    /**
     * 虚拟点赞量
     */
    private Long initLikeNum;

    /**
     * 点赞数量
     */
    private Long likeNum;

    /**
     * 收藏数量
     */
    private Long collectNum;

    /**
     * 评论数量
     */
    private Long commentNum;

    /**
     * 分享数量
     */
    private Integer shareNum;

    @TableField(exist = false)
    private String shareUrl;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否高亮显示 1是 0否
     */
    @JsonIgnore
    private String isHighLight;


    /**
     * 资讯来源id
     */
    @JsonIgnore
    private Long sourceId;

    /**
     * 第三方新闻对应的来源名称
     */
    @JsonIgnore
    private String sourceNameForThird;

    /**
     * 资讯标签id 逗号分隔
     */
    private String labelId;

    /**
     * 是否设为通告 0否 1是
     */
    @JsonIgnore
    private Long isNotice;

    /**
     * 审核拒绝原因
     */
    private String rejectReason;

    /**
     * 评分
     */
    @JsonIgnore
    private Long score;

    /**
     * 所属专题新闻的id 为0时不属于任何专题
     */
    @JsonView(Views.Admin.class)
    private Long subjectArticleId;


    /**
     * 初始点击量，加上真实点击量后在前台展示
     */
    @JsonView(Views.Admin.class)
    private Long initView;

    /**
     * 点击量乘以倍数
     */
    @JsonView(Views.Admin.class)
    private Long viewTimes;

    /**
     * 管理员id 关联管理员表id
     */
    @JsonView(Views.Admin.class)
    private Long managerId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    @JsonIgnore
    private String updateBy;

    /**
     * 是否置顶
     */
    private Integer isTop;

    /**
     * 发帖IP
     */
    @JsonIgnore
    private String ip;

    /**
     * 发帖地区
     */
    private String region;

    /**
     * 资源ID(关联产品ID)
     */
    private String resId;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;

    @TableField(exist = false)
    private String source;

    @TableField(exist = false)
    private String sourceImgUrl;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Article> relatedNewsList;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ArticleComment> commentList;

    @TableField(exist = false)
    private Integer isLike = 0;

    @TableField(exist = false)
    private Integer isCollect = 0;

    /**
     * 关联订阅号是否被关注
     */
    @TableField(exist = false)
    private Integer isOfficialFollow = 0;

    @TableField(exist = false)
    private String officialCateName;

    @TableField(exist = false)
    private String officialCreateBy;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(exist = false)
    private String collectionIds;

    @TableField(exist = false)
    private Long collectionId;

    @TableField(exist = false)
    private String collectionTitle;


    public void likeNumAddInit() {
        this.likeNum = (likeNum == null ? 0 : likeNum) + (initLikeNum == null ? 0 : initLikeNum);
    }
}
