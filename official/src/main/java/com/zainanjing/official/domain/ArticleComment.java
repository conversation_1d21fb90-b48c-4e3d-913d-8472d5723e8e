package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.ruoyi.common.config.Views;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 新闻评论对象 t_article_comment
 * 
 * <AUTHOR>
 * @date 2022-12-10
 */
@Data
@TableName(value ="t_article_comment")
public class ArticleComment  implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 资讯评论自增ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 资讯ID */
    @JsonView(Views.Admin.class)
    private Long articleId;

    /** 资讯标题 */
    @JsonView(Views.Admin.class)
    private String articleTitle;

    @JsonView(Views.Admin.class)
    private Long officialAccountId;

    /** 用户ID */
    private String uid;

    /** 用户名 */
    private String userName;

    /** 手机号码 */
    @JsonView(Views.Admin.class)
    private String userMobile;

    /** 评论人头像 */
    private String avatarUrl;

    /** 评论内容 */
    private String content;
    /** 评论图片地址,多个逗号分隔 */
    private String imgUrl;

    /** 1评论资讯 2评论评论 */
    private Integer type;

    /** type为2时 上级评论ID */
    private Long parentId;

    /** 排序字段 */
    private Integer sort;

    /** 状态 0正常（审核通过）1待审核 2管理员删除 */
    @JsonView(Views.Admin.class)
    private Integer status;

    /**
     * 发帖IP
     */
    @JsonIgnore
    private String ip;

    /**
     * 发帖地区
     */
    private String region;

    /**
     * 点赞数量
     */
    private Long likeNum;

    /** 创建者 */
    @JsonIgnore
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    @JsonIgnore
    private String updateBy;

    /** 更新时间 */
    @JsonView(Views.Admin.class)
    private Date updateTime;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ArticleComment> commentList;

    /** 是否匿名 1是 0否 **/
    private int isAnonymous;

    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String ids;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private Long totalNum;

    @TableField(exist = false)
    private Integer isLike = 0;

}
