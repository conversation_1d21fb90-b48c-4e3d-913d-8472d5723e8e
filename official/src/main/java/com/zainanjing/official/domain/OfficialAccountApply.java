package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 订阅号申请表
 * @TableName t_official_account_apply
 */
@Data
@TableName(value ="t_official_account_apply")
public class OfficialAccountApply implements Serializable {
    /**
     * 编号
     */
    @TableId(type = IdType.AUTO)
    private String id;

    private Long officialAccountId;

    /**
     * 申请主体（1:个人/达人;2:机构）
     */
    private Integer applyType;

    /**
     * 机构名称
     */
    private String organName;

    /**
     * 机构性质
     */
    private String organType;

    /**
     * 组织机构代码证
     */
    private String organCode;

    /**
     * 机构代码证照片
     */
    private String organCodeImg;

    /**
     * 授权委托书
     */
    private String organGrantImg;

    /**
     * 分类编号
     */
    private Long categoryId;

    /**
     * 名称
     */
    private String name;

    /**
     * 订阅号头像
     */
    private String logoImage;

    /**
     * 订阅号介绍
     */
    private String description;

    /**
     * 申请人身份证照片
     */
    private String applicantIdImg;

    /**
     * 联系电话
     */
    private String applicantPhone;

    /**
     * 状态:0 审核中；1: 审核通过；2：审核拒绝；3:已停用
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 申请人ID
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String smsCode;

}