package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * C 端用户 关注订阅号表
 * @TableName t_official_follow
 */
@TableName(value ="t_official_follow")
public class OfficialFollow implements Serializable {
    /**
     * C端用户id
     */
    private Long cUserId;

    /**
     * 订阅号id
     */
    private Long officialAccountId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * C端用户id
     */
    public Long getcUserId() {
        return cUserId;
    }

    /**
     * C端用户id
     */
    public void setcUserId(Long cUserId) {
        this.cUserId = cUserId;
    }

    /**
     * 订阅号id
     */
    public Long getOfficialAccountId() {
        return officialAccountId;
    }

    /**
     * 订阅号id
     */
    public void setOfficialAccountId(Long officialAccountId) {
        this.officialAccountId = officialAccountId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OfficialFollow other = (OfficialFollow) that;
        return (this.getcUserId() == null ? other.getcUserId() == null : this.getcUserId().equals(other.getcUserId()))
            && (this.getOfficialAccountId() == null ? other.getOfficialAccountId() == null : this.getOfficialAccountId().equals(other.getOfficialAccountId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getcUserId() == null) ? 0 : getcUserId().hashCode());
        result = prime * result + ((getOfficialAccountId() == null) ? 0 : getOfficialAccountId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", cUserId=").append(cUserId);
        sb.append(", officialAccountId=").append(officialAccountId);
        sb.append("]");
        return sb.toString();
    }
}