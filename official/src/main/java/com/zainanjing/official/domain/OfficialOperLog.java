package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * C端用户操作日志，关注、收藏、点赞
 * @TableName t_official_oper_log
 */
@TableName(value ="t_official_oper_log")
public class OfficialOperLog implements Serializable {
    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * C端用户ID
     */
    private String cUserId;

    /**
     * 新闻资讯ID
     */
    private Long articleId;

    /**
     * 订阅号ID
     */
    private Long officialId;

    /**
     * 操作类型
1: 关注
2: 取消关注
3: 收藏
4: 取消收藏
5: 点赞
6: 取消点赞
     7: 评论点赞
     8: 取消评论点赞
     */
    private Integer operType;

    /**
     * 收藏时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    public Long getId() {
        return id;
    }

    /**
     * ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * C端用户ID
     */
    public String getcUserId() {
        return cUserId;
    }

    /**
     * C端用户ID
     */
    public void setcUserId(String cUserId) {
        this.cUserId = cUserId;
    }

    /**
     * 新闻资讯ID
     */
    public Long getArticleId() {
        return articleId;
    }

    /**
     * 新闻资讯ID
     */
    public void setArticleId(Long articleId) {
        this.articleId = articleId;
    }

    /**
     * 订阅号ID
     */
    public Long getOfficialId() {
        return officialId;
    }

    /**
     * 订阅号ID
     */
    public void setOfficialId(Long officialId) {
        this.officialId = officialId;
    }

    /**
     * 操作类型
1: 关注
2: 取消关注
3: 收藏
4: 取消收藏
5: 点赞
6: 取消点赞
     */
    public Integer getOperType() {
        return operType;
    }

    /**
     * 操作类型
1: 关注
2: 取消关注
3: 收藏
4: 取消收藏
5: 点赞
6: 取消点赞
     */
    public void setOperType(Integer operType) {
        this.operType = operType;
    }

    /**
     * 收藏时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 收藏时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OfficialOperLog other = (OfficialOperLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getcUserId() == null ? other.getcUserId() == null : this.getcUserId().equals(other.getcUserId()))
            && (this.getArticleId() == null ? other.getArticleId() == null : this.getArticleId().equals(other.getArticleId()))
            && (this.getOfficialId() == null ? other.getOfficialId() == null : this.getOfficialId().equals(other.getOfficialId()))
            && (this.getOperType() == null ? other.getOperType() == null : this.getOperType().equals(other.getOperType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getcUserId() == null) ? 0 : getcUserId().hashCode());
        result = prime * result + ((getArticleId() == null) ? 0 : getArticleId().hashCode());
        result = prime * result + ((getOfficialId() == null) ? 0 : getOfficialId().hashCode());
        result = prime * result + ((getOperType() == null) ? 0 : getOperType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", cUserId=").append(cUserId);
        sb.append(", articleId=").append(articleId);
        sb.append(", officialId=").append(officialId);
        sb.append(", operType=").append(operType);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}