package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订阅号对象 t_official_account
 * 
 * <AUTHOR>
 * @date 2022-12-08
 */
@Data
@TableName(value ="t_official_account")
public class OfficialAccount  implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 编号 */
    @TableId
    private Long id;

    /** 分类编号 */
    private Long categoryId;

    private String categoryType;

    /** 名称 */
    private String name;

    /** 订阅号头像 */
    private String logoImage;

    /** 订阅号介绍 */
    private String description;

    /** 热门推荐标志(1为热门推荐) */
    private Integer recommended;

    /** 是否默认 */
    private Integer defaulted;

    private Long sort;

    /** 关注数量 */
    private Long followNum;

    /** 点赞数量 */
    private Long likeNum;

    /** 申请ID */
    private String applyId;

    /** 申请主体（1:个人/达人;2:机构） */
    private Integer applyType;

    /** 主体描述 */
    private String applyTypeDesc;

    /** 机构名称 */
    private String organName;

    /** 机构性质 */
    private String organType;

    private String organTypeDesc;

    /** 删除标志(1为删除) */
    @JsonIgnore
    @TableLogic
    private Integer deleted;

    /**
     * 是否开启  是否启用1:启用;0:停用
     */
    private Integer enabled;

    /** 创建者 */
    private String createBy;

    @JsonIgnore
    /** 创建时间 */
    private Date createTime;

    @JsonIgnore
    /** 更新者 */
    private String updateBy;

    @JsonIgnore
    /** 更新时间 */
    private Date updateTime;

}
