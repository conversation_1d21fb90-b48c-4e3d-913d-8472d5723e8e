package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订阅号用户权限对象 t_user_official
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Data
@TableName(value ="t_user_official")
public class UserOfficial implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 订阅号ID */
    private Long officialAccountId;

    /** 系统用户ID */
    private Long sysUserId;

    /** 用户名 **/
    @TableField(exist = false)
    private String userName;

    /** 用户昵称 **/
    @TableField(exist = false)
    private String nickName;

    /** 手机号码 **/
    @TableField(exist = false)
    private String phonenumber;

    @TableField(exist = false)
    private List<Long> roleIds;

    @TableField(exist = false)
    private List<String> roleNames;

    @TableField(exist = false)
    private String officialName;


    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;
}
