package com.zainanjing.official.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 新闻分类对象 t_article_cat
 * 
 * <AUTHOR>
 * @date 2022-12-08
 */
@Data
@TableName(value ="t_article_cat")
public class ArticleCat  implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 编码 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 分类名称 */
    private String catName;

    /** 0:新闻 1:短视频 */
    private Long type;

    /** 类型分类
     （0: PGC；1: OGC）
     */
    private String  categoryType;

    /** 备注 */
    private String remark;

    /** 排序 */
    private String sort;

    /**  是否开启 0开启1关闭 */
    private String enabled;

    /** 分类图片 */
    private String imgUrl;

    /** 中国搜索接口 */
    private String chinasoApi;

    /** 链接类型 （1 内链 2 外链） */
    private Long linkType;

    /** 内链模块 */
    private Long linkModule;

    /** 内链资源id */
    private String resourceId;

    /** 链接到,1分类列表页 2商品内容页 */
    private Long linkTo;

    /** 外链地址 */
    private String linkUrl;

    /** 是否默认分类 */
    private Long isdefault;

    /** 删除标志(1为删除) */
    private Integer deleted;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;
}
