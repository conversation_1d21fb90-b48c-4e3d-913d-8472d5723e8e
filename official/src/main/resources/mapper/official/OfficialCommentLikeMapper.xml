<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.official.mapper.OfficialCommentLikeMapper">
    
    <resultMap type="com.zainanjing.official.domain.OfficialCommentLike" id="OfficialCommentLikeResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="commentId"    column="comment_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 id,  c_user_id,  comment_id,  create_time     </sql>

</mapper>