<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.official.mapper.OfficialFollowMapper">

    <resultMap id="BaseResultMap" type="com.zainanjing.official.domain.OfficialFollow">
        <id property="cUserId" column="c_user_id" jdbcType="BIGINT"/>
        <id property="officialAccountId" column="official_account_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        c_user_id,official_account_id
    </sql>

    <select id="queryOfficialAccounts" resultType="com.zainanjing.official.dto.OfficialAccountDTO"
                parameterType="map">
            select a.id,
                   a.category_id as categoryId,
                   a.name,
                   a.logo_image as logoImage,
                   a.description,
                   a.recommended,
                   a.defaulted,
                   a.category_type,
                   c.name as categoryName,
                   a.create_by
            <if test="p.customerId != null and p.customerId != ''">
                ,(b.c_user_id IS NOT NULL) followed
            </if>
            from t_official_account a
                left join t_official_account_category c on a.category_id=c.id
            <if test="p.customerId != null and p.customerId != ''">
                     left join t_official_follow b on a.id = b.official_account_id
                and b.c_user_id = #{p.customerId}
            </if>
            <where>
                a.deleted = 0
                and a.enabled = 1
                <if test="p.isMyFollow != null and p.isMyFollow">
                    and b.c_user_id = #{p.customerId}
                </if>
                <if test="p.recommended != null and p.recommended != ''">
                    and a.recommended = #{p.recommended}
                </if>
                <if test="p.categoryId != null and p.categoryId != ''">
                    and a.category_id = #{p.categoryId}
                </if>
                <if test="p.officialId != null and p.officialId != ''">
                    and a.id = #{p.officialId}
                </if>
                <if test="p.keyword != null and p.keyword != ''">
                    and a.name LIKE CONCAT('%', #{p.keyword}, '%')
                </if>
                <if test="p.notCreateIds!=null and p.notCreateIds.size()>0">
                    AND a.create_by not in
                    <foreach collection="p.notCreateIds" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            </where>
            <if test="p.isMyFollow != null and p.isMyFollow">
                order by b.create_time desc
            </if>
            <if test="p.isMyFollow == null || !p.isMyFollow">
                order by a.sort asc
            </if>
        </select>

    <update id="updateFollowNum">
        update t_official_account set follow_num = follow_num + #{num} where id =  #{id}
    </update>

</mapper>
