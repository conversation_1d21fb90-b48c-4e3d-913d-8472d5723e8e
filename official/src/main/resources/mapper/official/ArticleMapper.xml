<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zainanjing.official.mapper.ArticleMapper">

    <update id="updateView" parameterType="long">
        update t_article set viewed=viewed+1 where id =  #{id}
    </update>

    <update id="updateShare" parameterType="long">
        update t_article set share_num=share_num+1 where id =  #{id}
    </update>

    <update id="updateLike">
        update t_article set like_num=like_num + #{num} where id =  #{id}
    </update>

    <update id="updateOfficialLike">
        update t_official_account set like_num=like_num + #{num} where id =  #{id}
    </update>

    <update id="updateCollect">
        update t_article set collect_num=collect_num + #{num}
         where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateCommentNum">
        update t_article set comment_num=comment_num + #{num} where id =  #{id}
    </update>

    <update id="reCountCommentNum">
        update t_article a set comment_num =
           (
               select count(id) from t_article_comment where article_id=a.id and status = 0
           )
        where a.id = #{id}
    </update>

    <select id="selectSubArticles" resultType="com.zainanjing.official.domain.Article">
        select *
        from t_article a
                 inner join t_article_collection_ref b on a.id = b.sub_article_id
            and b.article_id = #{articleId}
        where a.enabled = 0
          and a.status = 0
        order by b.sort
    </select>
    
    <select id="selectNotSetPage" resultType="com.zainanjing.official.domain.Article">
        select a.*
        from t_article a
        where a.enabled = 0
        and a.status = 0
        and a.create_by =#{userId}
        and a.article_cat_id =#{categoryId}
        and ( NOT EXISTS (select 1 from t_article_collection_ref b where a.id = b.sub_article_id )
        <if test="collectionId != null">
            or EXISTS (select 1 from t_article_collection_ref b where a.id = b.sub_article_id and b.article_id = #{collectionId})
        </if>
        )
        order by a.is_top desc, a.create_time desc
    </select>
</mapper>