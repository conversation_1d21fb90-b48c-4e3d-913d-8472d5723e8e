<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.official.mapper.OfficialAccountMapper">

    <resultMap id="BaseResultMap" type="com.zainanjing.official.dto.OfficialAccountDTO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
        <result property="categoryType" column="category_type" jdbcType="TINYINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="logoImage" column="logo_image" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="recommended" column="recommended" jdbcType="BIT"/>
        <result property="defaulted" column="defaulted" jdbcType="BIT"/>
        <result property="followNum" column="follow_num" jdbcType="BIGINT"/>
        <result property="likeNum" column="like_num" jdbcType="BIGINT"/>
        <result property="applyType" column="apply_type" jdbcType="TINYINT"/>
        <result property="applyTypeDesc" column="apply_type_desc" jdbcType="VARCHAR"/>
        <result property="organName" column="organ_name" jdbcType="VARCHAR"/>
        <result property="organType" column="organ_type" jdbcType="VARCHAR"/>
        <result property="organTypeDesc" column="organ_type_desc" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="categoryName" column="categoryName" jdbcType="VARCHAR"/>
        <result property="followed" column="followed" jdbcType="VARCHAR"/>
        <result property="enabled" column="enabled" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,category_id,category_type,
        name,logo_image,description,
        recommended,defaulted,sort,
        deleted,follow_num,like_num,
        apply_id,apply_type,apply_type_desc,
        organ_name,organ_type,organ_type_desc,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="selectAccountById" resultMap="BaseResultMap">
        SELECT
            a.*,
            b.name as categoryName
        <if test="cUid != null and cUid != ''">
            ,(c.c_user_id IS NOT NULL) followed
        </if>
        FROM
            t_official_account a
                INNER JOIN t_official_account_category b ON a.category_id = b.id
        <if test="cUid != null and cUid != ''">
            LEFT JOIN t_official_follow c on a.id = c.official_account_id and c.c_user_id = #{cUid}
        </if>
        <if test="ids!=null and ids.size()>0">
            WHERE a.id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>