package com.zainanjing.article.controller;

import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.aspectj.LogAspect;
import com.ruoyi.framework.web.service.PermissionService;
import com.zainanjing.article.domain.GdmmDictionary;
import com.zainanjing.article.domain.GdmmVideo;
import com.zainanjing.article.event.VideoEvent;
import com.zainanjing.article.mapper.GdmmVideoMapper;
import com.zainanjing.article.service.IGdmmDictionaryService;
import com.zainanjing.article.service.IGdmmVideoService;
import com.zainanjing.article.util.ScheduleUtil;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.common.util.RedisUtil;
import io.netty.util.internal.StringUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 短视频Controller
 *
 * <AUTHOR>
 * @date 2023-10-20
 */
@RestController
@RequestMapping("/article/video")
public class GdmmVideoController extends BaseController {
    @Autowired
    private IGdmmVideoService gdmmVideoService;

    @Resource
    private IGdmmDictionaryService gdmmDictionaryService;

    @Resource(name = "ss")
    private PermissionService permissionService;

    @Resource
    private GdmmVideoMapper gdmmVideoMapper;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 查询审核数量
     */
    @PreAuthorize("@ss.hasPermi('article:article:audit')")
    @RequestMapping(value = "/auditCount", method = RequestMethod.GET)
    public AjaxResult auditCount() {
        JSONObject jsonObject = new JSONObject();
        if (permissionService.hasPermi("article:auditVideo:one")) {
            jsonObject.put("videoOne", gdmmVideoMapper.auditCount(Map.of("status", Constants.WAIT_PASS_AUDIT)));
        }
        if (permissionService.hasPermi("article:auditVideo:two")) {
            jsonObject.put("videoTwo", gdmmVideoMapper.auditCount(Map.of("status", Constants.WAIT_AUDIT_TWO)));
        }
        if (permissionService.hasPermi("article:auditVideo:three")) {
            jsonObject.put("videoThree", gdmmVideoMapper.auditCount(Map.of("status", Constants.WAIT_AUDIT_THREE)));
        }
        return AjaxResult.success(jsonObject);
    }


    /**
     * 查询短视频列表
     */
    @PreAuthorize("@ss.hasPermi('article:video:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        searchCondition(searchable);
        IPage<GdmmVideo> page = gdmmVideoService.search(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    private void searchCondition(Searchable searchable) {
        if (!Objects.isNull(searchable.getFilterCdtns().get("beginTime"))) {
            searchable.addSearchParam("beginTime", CommonUtil.getTime(searchable.getFilterCdtns().get("beginTime").toString()));
        }
        if (!Objects.isNull(searchable.getFilterCdtns().get("endTime"))) {
            searchable.addSearchParam("endTime", CommonUtil.getTime(searchable.getFilterCdtns().get("endTime").toString()));
        }

        searchable.addSearchParam("statusArr", new Integer[]{Constants.WAIT_PASS_AUDIT, Constants.NOT_PASS_AUDIT, Constants.YES_PASS_AUDIT, Constants.WAIT_AUDIT_TWO, Constants.WAIT_AUDIT_THREE});

        if (searchable.getFilterCdtns().get("catStatus") == null) {
            searchable.addSearchParam("catStatus", Constants.STATUS_NORMAL);
        }
    }

    /**
     * 获取短视频详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:video:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gdmmVideoService.getById(id));
    }

    /**
     * 新增短视频
     */
    @PreAuthorize("@ss.hasPermi('article:video:add')")
    @Log(title = "短视频", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmVideo gdmmVideo) {

        setAuditInfo(gdmmVideo);
        gdmmVideo.setManagerId(SecurityUtils.getUserId());
        if (StrUtil.isNotEmpty(gdmmVideo.getCreateTimeS())) {
            gdmmVideo.setCreateTime(CommonUtil.getIntegerTime(gdmmVideo.getCreateTimeS()));
        } else if (gdmmVideo.getCreateTime() == null) {
            gdmmVideo.setCreateTime(CommonUtil.getTimestamp());
        }
        gdmmVideo.setUpdateTime(CommonUtil.getTimestamp());

        setVideoImg(gdmmVideo);

        gdmmVideo.setAuthor(gdmmVideo.getAuthor() == null ? "" : gdmmVideo.getAuthor());
        gdmmVideo.setCardId(0);//默认为0
        gdmmVideo.setSubjectId(ObjUtil.defaultIfNull(gdmmVideo.getSubjectId(), 0));//默认为0
        gdmmVideo.setSubjectCatId(ObjUtil.defaultIfNull(gdmmVideo.getSubjectCatId(), 0));//默认为0
        if (gdmmVideoService.save(gdmmVideo)) {
            syncVideoSchedule(gdmmVideo, Constants.SHEQU_ADD);
            applicationEventPublisher.publishEvent(new VideoEvent(this, gdmmVideo, EventAction.builder().type(BusinessType.INSERT).build()));
            LogAspect.setErrMsg(StrUtil.format("短视频:{}，操作成功", gdmmVideo.getTitle()));
        }
        return success();
    }

    private void setVideoImg(GdmmVideo gdmmVideo) {
        if (StringUtil.isNullOrEmpty(gdmmVideo.getVideoImgUrl()) && gdmmVideo.getType().intValue() == 1) {
            String tempUrl = gdmmVideo.getVideoUrl() + "?x-oss-process=video/snapshot,t_100,m_fast";
            gdmmVideo.setVideoImgUrl(OSSUtil.getImageKey(tempUrl, null));
        }
        BufferedImage videoImage = OSSUtil.getBufferedImage(OSSUtil.getImageURL(gdmmVideo.getVideoImgUrl()));
        if (videoImage != null) {
            gdmmVideo.setVideoImgHeight(videoImage.getHeight());
            gdmmVideo.setVideoImgWidth(videoImage.getWidth());
        }
    }

    private void setAuditInfo(GdmmVideo gdmmVideo) {
        //是否开启视频审核
        String isAuditVideoStr = RedisUtil.findString(RedisKey.ARTICLE_IS_AUDITVIDEO.getKey());
        logger.info("添加短视频,根据key=" + RedisKey.ARTICLE_IS_AUDITVIDEO.getKey() + ",从Redis取 短视频是否开启视频审核" + isAuditVideoStr);
        Integer isAuditVideo = StrUtil.isBlank(isAuditVideoStr) ? 0 : Integer.valueOf(isAuditVideoStr);
        //未开启视频审核  新增时 状态直接设置为审核通过。
        if (Constants.FOU.equals(isAuditVideo)) {
            gdmmVideo.setStatus(Constants.YES_PASS_AUDIT);
        } else {
            GdmmDictionary gdmmDictionary = gdmmDictionaryService.getById(gdmmVideo.getCatId());
            if (gdmmDictionary != null && Constants.STATUS_DELETE.equals(gdmmDictionary.getStatus() + "")) {
                gdmmVideo.setStatus(Constants.YES_PASS_AUDIT);
            } else {
                gdmmVideo.setStatus(Constants.WAIT_PASS_AUDIT);
            }
        }
    }

    /**
     * 修改短视频
     */
    @PreAuthorize("@ss.hasPermi('article:video:edit')")
    @Log(title = "短视频", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmVideo gdmmVideo) {
        setAuditInfo(gdmmVideo);
        setVideoImg(gdmmVideo);

        if (StrUtil.isNotEmpty(gdmmVideo.getCreateTimeS())) {
            gdmmVideo.setCreateTime(CommonUtil.getIntegerTime(gdmmVideo.getCreateTimeS()));
        }

        gdmmVideo.setUpdateTime(CommonUtil.getTimestamp());
        if (gdmmVideoService.updateById(gdmmVideo)) {
            syncVideoSchedule(gdmmVideo, Constants.SHEQU_UPDATE);
            applicationEventPublisher.publishEvent(new VideoEvent(this, gdmmVideo, EventAction.builder().type(BusinessType.UPDATE).build()));
            LogAspect.setErrMsg(StrUtil.format("短视频:{}，操作成功", gdmmVideo.getTitle()));
        }

        return success();
    }

    /**
     * 删除短视频
     */
    @PreAuthorize("@ss.hasPermi('article:video:remove')")
    @Log(title = "短视频", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        StringBuilder sb = new StringBuilder("短视频：");
        for (Long id : ids) {
            GdmmVideo gdmmVideo = gdmmVideoService.getById(id);

            if (gdmmVideo.getCardId() > 0 && gdmmVideo.getSubjectId() == 0) {
                logger.error("后台删除视频操作:删除的视频已推首页卡片,不能进行删除操作！！！");
                return AjaxResult.error("稿件已推荐至卡片，请取消推荐后，再操作删除");
            }

            Map<String, Object> paramMap = new HashMap<String, Object>();
            //专题新闻 解绑专题
            LambdaUpdateWrapper<GdmmVideo> gdmmVideoWrapper = new LambdaUpdateWrapper<>();
            if (gdmmVideo.getSubjectId() > 0) {
                gdmmVideoWrapper.in(GdmmVideo::getId, id).set(GdmmVideo::getSubjectId, Constants.FOU).set(GdmmVideo::getSubjectCatId, Constants.FOU);
            } else {
                gdmmVideoWrapper.in(GdmmVideo::getId, id).set(GdmmVideo::getStatus, Constants.STATUS_DELETE);
                syncVideoSchedule(gdmmVideo, Constants.SHEQU_DELETE);
            }
            if (gdmmVideoService.update(gdmmVideoWrapper)) {
                applicationEventPublisher.publishEvent(new VideoEvent(this, gdmmVideo, EventAction.builder().type(BusinessType.DELETE).build()));
                sb.append(gdmmVideo.getTitle()).append("，");
            }
        }
        sb.append("操作成功");
        LogAspect.setErrMsg(StrUtil.format(sb.toString()));
        return AjaxResult.success();
    }

    /**
     * 短视频点击量从redis同步到数据库
     */
    @RequestMapping(value = "/redis2dbVideo", method = RequestMethod.GET)
    @ResponseBody
    public AjaxResult redis2dbVideo() {
        //先判断是否正在同步，同步状态存redis 1：正在同步 其他状态：未同步
        String value = RedisUtil.findString(RedisKey.VIDEO_IS_SYNC_CLICKNUM.getKey());
        if (Constants.YES.equals(value)) {
            logger.error("redis 持久化短视频点击量正在进行......");
            return AjaxResult.error("正在同步中，请不要重复操作");
        }
        gdmmVideoService.redis2dbVideo();
        return AjaxResult.success();
    }


    @PreAuthorize("@ss.hasPermi('article:video:edit')")
    @PostMapping(value = "/updateBatch")
    public AjaxResult updateBatch(Long[] ids, Integer value, String field) {
        logger.info("后台操作视频是否推荐首页");

        LambdaUpdateWrapper<GdmmVideo> gdmmVideoWrapper = new LambdaUpdateWrapper<>();
        gdmmVideoWrapper.in(GdmmVideo::getId, ids);
        if ("isEnabled".equals(field)) {
            gdmmVideoWrapper.set(GdmmVideo::getEnabled, value);
        }
        if ("isHome".equals(field)) {
            gdmmVideoWrapper.set(GdmmVideo::getIsRecommend, value);
        }
        if ("isComment".equals(field)) {
            gdmmVideoWrapper.set(GdmmVideo::getIsComment, value);
        }
        return AjaxResult.success(gdmmVideoService.update(gdmmVideoWrapper));
    }

    @PreAuthorize("@ss.hasAnyPermi('article:auditVideo:one,article:auditVideo:two,article:auditVideo:three')")
    @Log(title = "短视频", businessType = BusinessType.AUDIT)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody GdmmVideo gdmmVideo) {
        GdmmVideo oldVideo = gdmmVideoService.getById(gdmmVideo.getId());
        if (oldVideo == null) {
            return AjaxResult.error("视频不存在");
        }

        setVideoImg(gdmmVideo);
        gdmmVideo.setUpdateTime(CommonUtil.getTimestamp());

        if (StrUtil.isNotEmpty(gdmmVideo.getCreateTimeS())) {
            gdmmVideo.setCreateTime(CommonUtil.getIntegerTime(gdmmVideo.getCreateTimeS()));
        }
        String auditStr = "";
        if (Constants.YES_PASS_AUDIT.equals(gdmmVideo.getStatus())) {//根据配置和当前值进行判断下一个状态
            String isAuditNewsStr = RedisUtil.findString(RedisKey.ARTICLE_IS_AUDITVIDEO.getKey());
            int isAuditNews = StrUtil.isBlank(isAuditNewsStr) ? 0 : Integer.parseInt(isAuditNewsStr);
            gdmmVideo.setStatus(auditStatus(oldVideo.getStatus(), isAuditNews));
            auditStr = Constants.AUDIT_STATUS_MAP_BEFORE.get(oldVideo.getStatus()) + "通过，" + Constants.AUDIT_STATUS_MAP_AFTER.get(gdmmVideo.getStatus());
        } else {
            auditStr = Constants.AUDIT_STATUS_MAP_BEFORE.get(oldVideo.getStatus()) + "拒绝";
        }
        if (gdmmVideoService.updateById(gdmmVideo)) {
            applicationEventPublisher.publishEvent(new VideoEvent(this, gdmmVideo, EventAction.builder().type(BusinessType.AUDIT)
                    .operator(SecurityUtils.getLoginUser())
                    .build()));
            LogAspect.setErrMsg(StrUtil.format("短视频:[{}] {}，操作成功", gdmmVideo.getTitle(), auditStr));
            return success();
        }
        return error("审核失败");
    }


    private int auditStatus(int currentStatus, int configSetting) {
        // 如果当前状态是待三审，无论配置如何，都设置为已审批
        if (currentStatus == 9) {
            return 0;
        }

        switch (configSetting) {
            case 0: // 无需审核
                return 0; // 直接设置为已审批
            case 1: // 一审
                return 0; // 直接设置为已审批
            case 2: // 二审
                if (currentStatus == 8) {
                    // 如果当前状态是待二审，直接设置为已审批
                    return 0;
                } else if (currentStatus == 3) {
                    // 如果当前状态是待一审，则设置为待二审
                    return 8;
                } else {
                    // 其他情况，保持当前状态不变
                    return currentStatus;
                }

            case 3: // 三审
                if (currentStatus == 8) {
                    // 如果当前状态是待二审，则设置为待三审
                    return 9;
                } else if (currentStatus == 3) {
                    // 如果当前状态是待一审，则设置为待二审
                    return 8;
                } else {
                    // 其他情况，保持当前状态不变
                    return currentStatus;
                }

            default:
                // 非法配置设定，返回当前状态
                return currentStatus;
        }
    }

    @Resource
    private ScheduleUtil scheduleUtil;

    //保存修改删除短视频时，同步Schedule
    public void syncVideoSchedule(GdmmVideo gdmmVideo, String type) {
        if (Constants.SHEQU_ADD.equals(type)) {//创建定时触发器
            if (gdmmVideo != null && gdmmVideo.getPublishStatus() != null && gdmmVideo.getPublishStatus() > 0) {
                logger.error("新增新闻同步Schedule，id=" + gdmmVideo.getId());
                scheduleUtil.sendHTTPToScheduleForVideo(Constants.SHEQU_ADD, gdmmVideo.getId(), Constants.MAGAGE_BACK);
            }
            return;
        }
        if (Constants.SHEQU_UPDATE.equals(type)) {//修改定时触发器
            if (gdmmVideo != null && gdmmVideo.getPublishStatus() != null && gdmmVideo.getPublishStatus() > 0) {
                logger.error("修改新闻同步Schedule，id=" + gdmmVideo.getId());
                scheduleUtil.sendHTTPToScheduleForVideo(Constants.SHEQU_UPDATE, gdmmVideo.getId(), Constants.MAGAGE_BACK);
            }
            return;
        }
        if (Constants.SHEQU_DELETE.equals(type)) {//删除定时触发器
            if (gdmmVideo != null && gdmmVideo.getPublishStatus() != null && gdmmVideo.getPublishStatus() > 0) {
                logger.error("删除新闻同步Schedule，id=" + gdmmVideo.getId());
                scheduleUtil.sendHTTPToScheduleForVideo(Constants.SHEQU_DELETE, gdmmVideo.getId(), Constants.MAGAGE_BACK);
            }
            return;
        }
    }

}
