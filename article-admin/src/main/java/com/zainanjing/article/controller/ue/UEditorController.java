package com.zainanjing.article.controller.ue;

import com.ruoyi.common.utils.ObjUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.common.utils.id.IdUtil;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.oss.config.AliOssConfig;
import com.ruoyi.oss.utils.AliOssFileUploadUtils;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.Objects;

@RestController("articleUeServerController")
@RequestMapping("/article/ueServer")
public class UEditorController extends BaseController {

    @PostMapping
    public JSONObject upload(String action, MultipartFile upfile, String[] source) throws IOException {

        // 上传文件路径
        String filePath = RuoYiConfig.getProfile() + "/ueditor" + "/";
        if (upfile == null && source != null && source.length > 0 && Objects.equals(action, "catchimage")) {
            JSONObject res = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            OSS ossClient = (new OSSClientBuilder()).build(AliOssConfig.getEndpoint(), AliOssConfig.getAccessKeyId(), AliOssConfig.getAccessKeySecret());
            try {
                for (String url : source) {
                    JSONObject json = new JSONObject();
                    //转存图片
                    json.put("state", "SUCCESS");
                    json.put("source", url);
                    json.put("title", "转存图片");
                    //下载图片，并且获取图片格式，切上传到阿里云
                    URLConnection connection = new URL(url).openConnection();
                    InputStream inputStream = connection.getInputStream();
                    String fileUrl = filePath + extractFilename();
                    ossClient.putObject(AliOssConfig.getBucketName(), fileUrl, inputStream);
                    json.put("url", AliOssConfig.getUrl() + fileUrl);
                    jsonArray.add(json);
                }
            } catch (OSSException var12) {
                OSSException oe = var12;
                oe.printStackTrace();
            } catch (ClientException var13) {
                ClientException ce = var13;
                ce.printStackTrace();
            } catch (Exception var14) {
                Exception e = var14;
                e.printStackTrace();
            } finally {
                ossClient.shutdown();
            }
            res.put("state", "SUCCESS");
            res.put("list", jsonArray);
            return res;

        } else if (upfile != null) {
            // 上传并返回新文件名称
            JSONObject json = new JSONObject();
            String fileName = null;

            if (action.equals("uploadimage")) {
                if (ObjUtil.equals(1L, CommonUtil.redisCommonFind(RedisKey.ARTICLE_IS_USERWATERMARK.getKey()))) {
                    fileName = AliOssFileUploadUtils.uploadWater(filePath, upfile, MimeTypeUtils.IMAGE_EXTENSION, "image/watermark,image_" + Base64.encode(RuoYiConfig.getProfile() + "/upload/image/newwatermark.png"));
                } else {
                    fileName = AliOssFileUploadUtils.upload(filePath, upfile);
                }
                json.put("title", "图片文件");
            } else if (action.equals("uploadvideo")) {
                json.put("title", "视频文件");
                fileName = AliOssFileUploadUtils.upload(filePath, upfile);
            } else {
                json.put("title", "文件");
                fileName = AliOssFileUploadUtils.upload(filePath, upfile);
            }

            json.put("state", "SUCCESS");
            json.put("url", fileName);
            json.put("original", upfile.getOriginalFilename());

            return json;
        }
        JSONObject json = new JSONObject();
        json.put("state", "ERROR");
        return json;
    }

    public static final String extractFilename() {
        Date now = new Date();
        String month = DateFormatUtils.format(now, "yyyyMM");
        String day = DateFormatUtils.format(now, "dd");
        return StrUtil.format("{}/{}/{}.{}", new Object[]{month, day, IdUtil.getSnowflakeNextIdStr(), "png"});
    }
}
