package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.article.domain.GdmmLabel;
import com.zainanjing.article.service.IGdmmLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 标签Controller
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@RestController
@RequestMapping("/article/label")
public class GdmmLabelController extends BaseController {
    @Autowired
    private IGdmmLabelService gdmmLabelService;

    /**
     * 查询标签列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<GdmmLabel> page = gdmmLabelService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }
//
//    /**
//     * 获取标签详细信息
//     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Integer id)
//    {
//        return AjaxResult.success(gdmmLabelService.getById(id));
//    }
//
//    /**
//     * 新增标签
//     */
//    @Log(title = "标签", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody GdmmLabel gdmmLabel)
//    {
//        return toAjax(gdmmLabelService.save(gdmmLabel));
//    }
//
//    /**
//     * 修改标签
//     */
//    @Log(title = "标签", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody GdmmLabel gdmmLabel)
//    {
//        return toAjax(gdmmLabelService.updateById(gdmmLabel));
//    }
//
//    /**
//     * 删除标签
//     */
//    @Log(title = "标签", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable("ids") List<Integer> ids)
//    {
//        return toAjax(gdmmLabelService.removeByIds(ids));
//    }
}
