package com.zainanjing.article.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.dfa.WordTree;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.html.EscapeUtil;
import com.zainanjing.article.domain.GdmmSpecialWord;
import com.zainanjing.article.service.IGdmmSpecialWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 敏感词Controller
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/article/specialWord")
public class GdmmSpecialWordController extends BaseController {
    @Autowired
    private IGdmmSpecialWordService gdmmSpecialWordService;

    /**
     * 敏感词判定
     */
    @PostMapping("/check")
    public AjaxResult add(@RequestBody JSONObject jsonObject) {
        String content = EscapeUtil.clean(jsonObject.getString("content"));
        if (StrUtil.isBlank(content)) {
            return AjaxResult.success();
        }
        List<GdmmSpecialWord> list = gdmmSpecialWordService.lambdaQuery().eq(GdmmSpecialWord::getType, 0).list();
        if (CollUtil.isNotEmpty(list)) {
            WordTree tree = new WordTree();
            list.stream().map(GdmmSpecialWord::getName).forEach(tree::addWord);
            List<String> matchAllTitle = tree.matchAll(content, -1, false, false);
            //去重
            matchAllTitle = CollUtil.distinct(matchAllTitle);
            if (CollUtil.isNotEmpty(matchAllTitle)) {
                AjaxResult ajaxResult = new AjaxResult();
                ajaxResult.put("code", 1001);
                ajaxResult.put("msg", "包含敏感词:" + matchAllTitle);
                return ajaxResult;
            }
        }
        return AjaxResult.success();
    }


}
