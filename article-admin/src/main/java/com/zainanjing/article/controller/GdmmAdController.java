package com.zainanjing.article.controller;

import com.ruoyi.common.utils.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.Sort;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.constant.LinkModuleEnum;
import com.zainanjing.article.domain.GdmmAd;
import com.zainanjing.article.domain.GdmmAdPosition;
import com.zainanjing.article.domain.GdmmArticle;
import com.zainanjing.article.service.IGdmmAdPositionService;
import com.zainanjing.article.service.IGdmmAdService;
import com.zainanjing.article.service.IGdmmArticleService;
import com.zainanjing.article.util.FilterCdtnsUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告Controller
 *
 * <AUTHOR>
 * @date 2023-11-05
 */
@RestController
@RequestMapping("/article/ad")
public class GdmmAdController extends BaseController {
    @Autowired
    private IGdmmAdService gdmmAdService;

    @Resource
    private IGdmmArticleService gdmmArticleService;

    @Resource
    private IGdmmAdPositionService gdmmAdPositionService;

    /**
     * 查询广告列表
     */
    @PreAuthorize("@ss.hasPermi('article:ad:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {

        //广告位置存在上下级的问题，如果页面选择的是上级,那么该位置的下级信息也要展示
        Long positionId = FilterCdtnsUtil.getLong(searchable.getFilterCdtns().get("positionId"));
        //从新闻的banner图菜单进入广告列表
        if (positionId == null && searchable.getFilterCdtns().get("module") != null) {
            String module = FilterCdtnsUtil.getString(searchable.getFilterCdtns().get("module"));
            String code = FilterCdtnsUtil.getString(searchable.getFilterCdtns().get("code"));
            LambdaQueryWrapper<GdmmAdPosition> positionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            positionLambdaQueryWrapper.eq(GdmmAdPosition::getModule, module).eq(GdmmAdPosition::getCode, code);
            GdmmAdPosition one = gdmmAdPositionService.getOne(positionLambdaQueryWrapper);
            if (one != null) {
                positionId = one.getId();
            }
        }
        if (positionId != null) {
            LambdaQueryWrapper<GdmmAdPosition> positionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            positionLambdaQueryWrapper.eq(GdmmAdPosition::getId, positionId).or().eq(GdmmAdPosition::getParentId, positionId);
            List<GdmmAdPosition> list = gdmmAdPositionService.list(positionLambdaQueryWrapper);
            searchable.addSearchParam("positionId_in", list.stream().map(x -> x.getId()).collect(Collectors.toList())); //查询该广告位以及下级广告位
        }
        searchable.addSort(Sort.Direction.DESC, "sort");
        searchable.addSort(Sort.Direction.DESC, "id");

        IPage<GdmmAd> page = gdmmAdService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取广告详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:ad:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gdmmAdService.getById(id));
    }

    /**
     * 新增广告
     */
    @PreAuthorize("@ss.hasPermi('article:ad:add')")
    @Log(title = "广告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmAd gdmmAd) {
        if (ObjUtil.equals(Integer.valueOf(1), gdmmAd.getLinkType())) {
            if (null != gdmmAd.getResourceParamIds()) {
                gdmmAd.setLinkUrl(gdmmAd.getResourceParamIds().concat("-")
                        .concat(String.valueOf(gdmmAd.getResourceId())));
            } else {
                gdmmAd.setLinkUrl("-".concat(String.valueOf(gdmmAd.getResourceId())));
            }
        }
        return toAjax(gdmmAdService.save(gdmmAd));
    }

    /**
     * 修改广告
     */
    @PreAuthorize("@ss.hasPermi('article:ad:edit')")
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmAd gdmmAd) {
        if (ObjUtil.equals(Integer.valueOf(1), gdmmAd.getLinkType())) {
            if (null != gdmmAd.getResourceParamIds()) {
                gdmmAd.setLinkUrl(gdmmAd.getResourceParamIds().concat("-")
                        .concat(String.valueOf(gdmmAd.getResourceId())));
            } else {
                gdmmAd.setLinkUrl("-".concat(String.valueOf(gdmmAd.getResourceId())));
            }
        }
        return toAjax(gdmmAdService.updateById(gdmmAd));
    }

    /**
     * 删除广告
     */
    @PreAuthorize("@ss.hasPermi('article:ad:remove')")
    @Log(title = "广告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(gdmmAdService.removeByIds(ids));
    }

    @RequestMapping(value = "/getImgInfoByAdPositionId", method = RequestMethod.GET)
    public AjaxResult getImgInfoByAdPositionId(Long id) {
        GdmmAdPosition adPosition = null;
        List<GdmmAd> gdmmAdList = null;
        Map<String, Object> filterMap = new HashMap<String, Object>();
        adPosition = gdmmAdPositionService.getById(id);
        //查询该广告位下显示状态的广告类型
        LambdaQueryWrapper<GdmmAd> gdmmAdLambdaQueryWrapper = new LambdaQueryWrapper<>();
        gdmmAdLambdaQueryWrapper.lt(GdmmAd::getPositionCode, 0).eq(GdmmAd::getPositionId, id).eq(GdmmAd::getEnabled, Constants.STATUS_NORMAL);
        gdmmAdList = gdmmAdService.list(gdmmAdLambdaQueryWrapper);
        if (gdmmAdList != null && gdmmAdList.size() > 0) {
            adPosition.setMediaType(gdmmAdList.get(0).getMediaType());
        }
        return AjaxResult.success(adPosition);
    }

    /**
     * 检查新闻是否审核通过
     *
     * @param resourceId
     * @param linkModule
     * @param linkTo
     * @return
     * @throws Exception
     */
    @GetMapping(value = "/checkArticle")
    public AjaxResult checkArticle(String resourceId, Integer linkModule, Integer linkTo) {
        GdmmArticle gdmmArticle = null;
        if (!LinkModuleEnum.NEWS.getId().equals(linkModule) || Constants.SHI.equals(linkTo)) {
            return AjaxResult.success(true);
        }
        if (StrUtil.isEmpty(resourceId)) {
            return AjaxResult.success(false);
        }
        gdmmArticle = gdmmArticleService.getById(resourceId);
        if (null == gdmmArticle) {
            return AjaxResult.success(false);
        }
        return AjaxResult.success(Constants.YES_PASS_AUDIT.equals(gdmmArticle.getStatus()) ? true : false);
    }

}
