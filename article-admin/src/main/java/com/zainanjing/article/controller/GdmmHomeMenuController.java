package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmHomeMenu;
import com.zainanjing.article.service.IGdmmHomeMenuService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 频道管理Controller
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@RestController
@RequestMapping("/article/menu")
public class GdmmHomeMenuController extends BaseController {
    @Autowired
    private IGdmmHomeMenuService gdmmHomeMenuService;

    /**
     * 查询频道管理列表
     */
    @PreAuthorize("@ss.hasPermi('article:menu:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<GdmmHomeMenu> page = gdmmHomeMenuService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取频道管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:menu:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(gdmmHomeMenuService.getById(id));
    }

    /**
     * 新增频道管理
     */
    @PreAuthorize("@ss.hasPermi('article:menu:add')")
    @Log(title = "频道管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmHomeMenu gdmmHomeMenu) {
        return toAjax(gdmmHomeMenuService.save(gdmmHomeMenu));
    }

    /**
     * 修改频道管理
     */
    @PreAuthorize("@ss.hasPermi('article:menu:edit')")
    @Log(title = "频道管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmHomeMenu gdmmHomeMenu) {
        return toAjax(gdmmHomeMenuService.updateById(gdmmHomeMenu));
    }

    /**
     * 删除频道管理
     */
    @PreAuthorize("@ss.hasPermi('article:menu:remove')")
    @Log(title = "频道管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        LambdaUpdateWrapper<GdmmHomeMenu> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmHomeMenu::getId, ids).set(GdmmHomeMenu::getStatus, Constants.STATUS_DELETE);
        return toAjax(gdmmHomeMenuService.update(updateWrapper));
    }
}
