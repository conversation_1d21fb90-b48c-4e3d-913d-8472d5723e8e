package com.zainanjing.article.controller;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.Sort;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmNotice;
import com.zainanjing.article.service.IGdmmNoticeService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 官网公告Controller
 *
 * <AUTHOR>
 * @date 2023-10-19
 */
@RestController
@RequestMapping("/article/notice")
public class GdmmNoticeController extends BaseController {
    @Autowired
    private IGdmmNoticeService gdmmNoticeService;

    /**
     * 查询官网公告列表
     */
    @PreAuthorize("@ss.hasPermi('article:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        if (CollUtil.isEmpty(searchable.getSort())) {
            searchable.addSort(Sort.Direction.DESC, "sort");
            searchable.addSort(Sort.Direction.DESC, "id");
        }
        IPage<GdmmNotice> page = gdmmNoticeService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }


    /**
     * 获取官网公告详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:notice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(gdmmNoticeService.getById(id));
    }

    /**
     * 新增官网公告
     */
    @PreAuthorize("@ss.hasPermi('article:notice:add')")
    @Log(title = "官网公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmNotice gdmmNotice) {
        gdmmNotice.setUid(SecurityUtils.getUserId());
        gdmmNotice.setCreateTime(CommonUtil.getTimestamp());
        gdmmNotice.setUpdateTime(CommonUtil.getTimestamp());
        gdmmNotice.setStatus(Integer.parseInt(Constants.STATUS_NORMAL));
        return toAjax(gdmmNoticeService.save(gdmmNotice));
    }

    /**
     * 修改官网公告
     */
    @PreAuthorize("@ss.hasPermi('article:notice:edit')")
    @Log(title = "官网公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmNotice gdmmNotice) {
        gdmmNotice.setUpdateTime(CommonUtil.getTimestamp());
        return toAjax(gdmmNoticeService.updateById(gdmmNotice));
    }

    /**
     * 删除官网公告
     */
    @PreAuthorize("@ss.hasPermi('article:notice:remove')")
    @Log(title = "官网公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        LambdaUpdateWrapper<GdmmNotice> gdmmNoticeWrapper = new LambdaUpdateWrapper<>();
        gdmmNoticeWrapper.in(GdmmNotice::getId, ids).set(GdmmNotice::getStatus, Constants.STATUS_DELETE);
        return toAjax(gdmmNoticeService.update(gdmmNoticeWrapper));
    }
}
