package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.AsyncManager;
import com.ruoyi.common.utils.http.HttpUtil;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmComment;
import com.zainanjing.article.service.IGdmmCommentService;
import com.zainanjing.common.util.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.*;

/**
 * 通用评论Controller
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@RestController
@RequestMapping("/article/comment")
public class GdmmCommentController extends BaseController {
    @Autowired
    private IGdmmCommentService gdmmCommentService;

    @Value("${system.im-url}")
    private String imUrl;


    /**
     * 查询通用评论列表
     */
    @PreAuthorize("@ss.hasPermi('article:comment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        Integer resType = searchable.getFilterCdtns().get("resType") == null ? null : Integer.valueOf(searchable.getFilterCdtns().get("resType").toString());
        Integer status = searchable.getFilterCdtns().get("status") == null ? Constants.STATUS_NOW : Integer.valueOf(searchable.getFilterCdtns().get("status").toString());

        List<Integer> statusArr = Collections.singletonList(Constants.STATUS_NOW);

        //直播扩展 start
        if (Objects.equals(resType, Integer.valueOf(1))) {
            //默认显示状态
            if (status == 0) {
                statusArr = Collections.singletonList(Constants.STATUS_NOW);
            } else if (status == 1) {
                statusArr = Collections.singletonList(Constants.STATUS_YES);
            } else if (status == 3) {
                statusArr = Collections.singletonList(Constants.STATUS_NO);
            }
        }

        //直播扩展 end
        searchable.addSearchParam("statusArr", statusArr);
        IPage<GdmmComment> page = gdmmCommentService.selectGdmmCommentList(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    @RequestMapping(value = "/auditList", method = RequestMethod.GET)
    public TableDataInfo auditList(Searchable searchable) {
        Integer status = searchable.getFilterCdtns().get("status") == null ? Constants.STATUS_YES : Integer.valueOf(searchable.getFilterCdtns().get("status").toString());
        searchable.addSearchParam("status", status);
        IPage<GdmmComment> page = gdmmCommentService.selectGdmmCommentList(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取通用评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:comment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gdmmCommentService.getById(id));
    }

    /**
     * 新增通用评论
     */
    @PreAuthorize("@ss.hasPermi('article:comment:add')")
    @Log(title = "通用评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmComment gdmmComment) {
        return toAjax(gdmmCommentService.save(gdmmComment));
    }

    /**
     * 修改通用评论
     */
    @PreAuthorize("@ss.hasPermi('article:comment:edit')")
    @Log(title = "通用评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmComment gdmmComment) {
        gdmmComment.setUpdateTime(CommonUtil.getTimestamp());
        return toAjax(gdmmCommentService.updateById(gdmmComment));
    }

    /**
     * 删除通用评论
     */
    @PreAuthorize("@ss.hasPermi('article:comment:remove')")
    @Log(title = "通用评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        for (int i = 0; i < ids.size(); i++) {
            GdmmComment gdmmComment = gdmmCommentService.getById(ids.get(i));

            //计算要删除的评论条
            Map<String, Object> filterMap = new HashMap<String, Object>();
            filterMap.put("idDelete", ids.get(i));
            filterMap.put("isDelete", Constants.SHI);
            filterMap.put("resType", gdmmComment.getResType());
            filterMap.put("status", Constants.STATUS_NOW);
            Integer deleteNum = gdmmCommentService.count(filterMap);
            logger.debug("短视频 后台删除评论[id:" + ids.get(i) + ",parentId" + ids.get(i) + ",resType:" + gdmmComment.getResType() + ",deleteNum:" + deleteNum);

            //删除评论
            Map<String, Object> updateMap = new HashMap<String, Object>();
            updateMap.put("id", ids.get(i));
            updateMap.put("parentId", ids.get(i));
            updateMap.put("resType", gdmmComment.getResType());
            updateMap.put("status", Constants.STATUS_HIDE);
            updateMap.put("updateTime", CommonUtil.getTimestamp());
            Boolean isUpdate = gdmmCommentService.updateByMap(updateMap);

            //减去评论数
            if (isUpdate) {
                for (int j = 0; j < deleteNum; j++) {
                    CommonUtil.redisCommonMinus(Constants.SHORT_VIDEO_STR + "_" + Constants.COMMENTNUM + "_" + gdmmComment.getResId());
                }
            }
        }
        return AjaxResult.success();
    }


    /**
     * @return String
     * @Title:audit
     * @Description 审核通用评论
     */
    @RequestMapping(value = "/doAudit", method = RequestMethod.POST)
    public AjaxResult doAudit(@RequestBody GdmmComment gdmmComment) {
        GdmmComment dbComment = gdmmCommentService.getById(gdmmComment.getId());
        gdmmComment.setUpdateTime(CommonUtil.getTimestamp());
        gdmmCommentService.updateById(gdmmComment);

        updateRedis(dbComment.getResId(), dbComment.getStatus(), gdmmComment.getStatus());
        return AjaxResult.success();
    }

    private void updateRedis(Integer resId, Integer dbStatus, Integer updateStatus) {
        //状态发生了修改
        if (!dbStatus.equals(updateStatus)) {
            //如果改为审核通过 评论数+1
            if (Constants.FOU.equals(updateStatus)) {
                CommonUtil.redisCommonAdd(Constants.SHORT_VIDEO_STR + "_" + Constants.COMMENTNUM + "_" + resId);
            }
            //如果改为审核拒绝  评论数-1
            //else if(Constants.SHI.equals(updateStatus)) {
            //	CommonUtil.redisCommonMinus(jedisTemplate,Constants.SHORT_VIDEO_STR+"_"+Constants.COMMENTNUM+"_"+resId);
            //}
        }
    }


    /**
     * @param ids
     * @return Message
     * @Title:audit
     * @Description 批量审核通用评论
     */
    @PreAuthorize("@ss.hasPermi('article:comment:audit')")
    @Log(title = "资讯评论审核", businessType = BusinessType.OTHER)
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public AjaxResult audit(Long[] ids, Integer status) {
        //如果是审核通过，先查ids里待审核的id，再+1 （已经是审核通过的 不用加）
        if (Constants.FOU.equals(status)) {
            addNum(ids);
        }
        //如果是审核拒绝，先查ids里已审核的id，再-1 （已经是未审核的 不用减）
        //else if(Constants.SHI.equals(status)) {
        //	minusNum(ids);
        //}
        LambdaUpdateWrapper<GdmmComment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmComment::getId, ids).set(GdmmComment::getStatus, status).set(GdmmComment::getUpdateTime, CommonUtil.getTimestamp());
        boolean update = gdmmCommentService.update(updateWrapper);
        if (update) {
            gdmmCommentService.lambdaQuery().in(GdmmComment::getId, ids).in(GdmmComment::getResType, 1, 2).list().forEach(gdmmComment -> {
                if (Constants.FOU.equals(status)) {
                    AsyncManager.me().execute(() -> {
                        HttpUtil.get(imUrl + "/message/push?msgId=" + gdmmComment.getId());
                    });
                }
            });
        }
        return AjaxResult.success(update);
    }

//    @RequestMapping(value="/audit",method = RequestMethod.GET)
//    public String audit(Long id, Integer resType){
//        GdmmComment gdmmComment = null;
//        try {
//            gdmmComment = (GdmmComment) gdmmCommentService.findById(id);
//            List<BcImg> bcImgList = searchBcImgs(id.toString(),resType);
//            gdmmComment.setBcImgList(bcImgList);
//            if (gdmmComment.getResType()==1){ //直播展示图片
//                String imgUrls = gdmmComment.getImgUrls();
//                if (imgUrls!=null){
//                    String[] split = imgUrls.split(",");
//                    for (String str:split) {
//                        BcImg bcImg = new BcImg();
//                        bcImg.setImgUrl(str);
//                        gdmmComment.getBcImgList().add(bcImg);
//                    }
//                }
//            }
//            model.addAttribute("gdmmComment", gdmmComment);
//            model.addAttribute("resType", resType);
//        } catch (SQLException e) {
//            logger.error("跳转到通用评论审核页面报错",e);
//            shangbaoFailJianKong(e);
//            return ERROR_VIEW;
//        }catch (Exception e) {
//            logger.error("跳转到通用评论审核页面报错",e);
//            shangbaoFailJianKong(e);
//            return ERROR_VIEW;
//        }
//        return "/admin/article/comment/audit";
//    }

    /**
     * @param ids void
     * @throws SQLException
     * @Title:minusNum
     * @Description 已审核的状态修改，需要减去评论数
     */
    private void minusNum(Long[] ids) {
        LambdaQueryWrapper<GdmmComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GdmmComment::getId, ids).eq(GdmmComment::getStatus, Constants.FOU);
        List<GdmmComment> list = gdmmCommentService.list(queryWrapper);
        for (GdmmComment GdmmComment : list) {
            Integer resId = GdmmComment.getResId();
            CommonUtil.redisCommonMinus(Constants.SHORT_VIDEO_STR + "_" + Constants.COMMENTNUM + "_" + resId);
        }
    }

    /**
     * @param ids
     * @throws SQLException void
     * @Title:addNum
     * @Description 未审核的状态修改为已审核 ，评论数加+1
     */
    private void addNum(Long[] ids) {
        LambdaQueryWrapper<GdmmComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GdmmComment::getId, ids).eq(GdmmComment::getStatus, Constants.SHI);
        List<GdmmComment> list = gdmmCommentService.list(queryWrapper);
        for (GdmmComment gdmmComment : list) {
            Integer resId = gdmmComment.getResId();
            CommonUtil.redisCommonAdd(Constants.SHORT_VIDEO_STR + "_" + Constants.COMMENTNUM + "_" + resId);
        }
    }


    public String getImUrl() {
        return imUrl;
    }

    public void setImUrl(String imUrl) {
        this.imUrl = imUrl;
    }
}
