package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmArticleCat;
import com.zainanjing.article.domain.GdmmArticleHis;
import com.zainanjing.article.domain.GdmmDictionary;
import com.zainanjing.article.service.IGdmmArticleCatService;
import com.zainanjing.article.service.IGdmmArticleHisService;
import com.zainanjing.article.service.IGdmmDictionaryService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 新闻历史版本Controller
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@RestController
@RequestMapping("/article/articleHis")
public class GdmmArticleHisController extends BaseController {
    @Autowired
    private IGdmmArticleHisService gdmmArticleHisService;

    @Resource
    private IGdmmDictionaryService gdmmDictionaryService;

    @Resource
    private IGdmmArticleCatService gdmmArticleCatService;

    /**
     * 查询新闻历史版本列表
     */
    @PreAuthorize("@ss.hasPermi('article:articleHis:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<GdmmArticleHis> page = gdmmArticleHisService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取新闻历史版本详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        GdmmArticleHis gdmmArticleHis = gdmmArticleHisService.getById(id);
        GdmmDictionary gdmmDictionary = gdmmDictionaryService.getById(gdmmArticleHis.getSourceId());
        gdmmArticleHis.setSource(gdmmDictionary == null || gdmmDictionary.getName() == null ? "" : gdmmDictionary.getName());
        //获取标签名称
        if (StrUtil.isNotBlank(gdmmArticleHis.getLabelId()) && !"-1".equals(gdmmArticleHis.getLabelId())) {
            gdmmDictionary = gdmmDictionaryService.getById(gdmmArticleHis.getLabelId());
            gdmmArticleHis.setLabel(gdmmDictionary == null || gdmmDictionary.getName() == null ? "" : gdmmDictionary.getName());
        }

        GdmmArticleCat gdmmArticleCat = gdmmArticleCatService.getById(gdmmArticleHis.getArticleCatId());
        gdmmArticleHis.setCatName(Objects.isNull(gdmmArticleCat) ? "" : gdmmArticleCat.getCatName());
        return AjaxResult.success(gdmmArticleHis);
    }

}
