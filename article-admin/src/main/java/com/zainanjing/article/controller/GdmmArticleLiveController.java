package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmArticleLive;
import com.zainanjing.article.service.IGdmmArticleLiveService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新闻图文直播Controller
 *
 * <AUTHOR>
 * @date 2023-11-26
 */
@RestController
@RequestMapping("/article/live")
public class GdmmArticleLiveController extends BaseController {
    @Autowired
    private IGdmmArticleLiveService gdmmArticleLiveService;

    /**
     * 查询新闻图文直播列表
     */
    @PreAuthorize("@ss.hasPermi('article:live:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<GdmmArticleLive> page = gdmmArticleLiveService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取新闻图文直播详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:live:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(gdmmArticleLiveService.getById(id));
    }

    /**
     * 新增新闻图文直播
     */
    @PreAuthorize("@ss.hasPermi('article:live:add')")
    @Log(title = "新闻图文直播", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmArticleLive gdmmArticleLive) {
        gdmmArticleLive.setCreateTime(CommonUtil.getTimestamp());
        return toAjax(gdmmArticleLiveService.save(gdmmArticleLive));
    }

    /**
     * 修改新闻图文直播
     */
    @PreAuthorize("@ss.hasPermi('article:live:edit')")
    @Log(title = "新闻图文直播", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmArticleLive gdmmArticleLive) {
        return toAjax(gdmmArticleLiveService.updateById(gdmmArticleLive));
    }

    /**
     * 删除新闻图文直播
     */
    @PreAuthorize("@ss.hasPermi('article:live:remove')")
    @Log(title = "新闻图文直播", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        LambdaUpdateWrapper<GdmmArticleLive> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmArticleLive::getId, ids);
        updateWrapper.set(GdmmArticleLive::getStatus, Constants.SHI);
        return toAjax(gdmmArticleLiveService.update(updateWrapper));
    }

}
