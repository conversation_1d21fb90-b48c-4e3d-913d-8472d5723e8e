package com.zainanjing.article.controller;

import com.ruoyi.common.utils.BeanUtil;
import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.SearchOperator;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.aspectj.LogAspect;
import com.ruoyi.framework.web.service.PermissionService;
import com.ruoyi.oss.config.AliOssConfig;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.article.domain.*;
import com.zainanjing.article.event.ArticleEvent;
import com.zainanjing.article.service.*;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.common.util.RedisUtil;
import com.zainanjing.article.util.ScheduleUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;

/**
 * 新闻Controller
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@RestController
@RequestMapping("/article/article")
public class GdmmArticleController extends BaseController {
    @Autowired
    private IGdmmArticleService gdmmArticleService;

    @Resource
    private IGdmmParamsService gdmmParamsService;

    @Resource
    private IGdmmDictionaryService gdmmDictionaryService;

    @Resource
    private IGdmmArticleCommentService gdmmArticleCommentService;

    @Resource
    private IGdmmArticleCatService gdmmArticleCatService;

    @Resource(name = "ss")
    private PermissionService permissionService;

    @Resource
    private IGdmmArticleHisService gdmmArticleHisService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;


    /**
     * 查询新闻列表
     */
    @PreAuthorize("@ss.hasPermi('article:article:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {

        searchableInit(searchable);
        //配置新闻频率的管理员只能看到同频率的新闻
        IPage<GdmmArticle> page = gdmmArticleService.list(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }


    /**
     * 审核新闻列表
     */
    @PreAuthorize("@ss.hasPermi('article:article:audit')")
    @RequestMapping(value = "/auditList", method = RequestMethod.GET)
    public TableDataInfo auditList(Searchable searchable) {

        searchableInitAudit(searchable);

        IPage<GdmmArticle> page = gdmmArticleService.list(searchable);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            //从redis中获取新闻点击量
            for (GdmmArticle article : page.getRecords()) {
                article.setViewed(CommonUtil.redisCommonFind(RedisKey.ARTICLE_CLICKNUM + "_" + article.getId()));
            }
        }
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 查询审核数量
     */
    @PreAuthorize("@ss.hasPermi('article:article:audit')")
    @RequestMapping(value = "/auditCount", method = RequestMethod.GET)
    public AjaxResult auditCount() {
        JSONObject jsonObject = new JSONObject();
        if (permissionService.hasPermi("article:audit:one")) {
            jsonObject.put("newsOne", gdmmArticleService.lambdaQuery().eq(GdmmArticle::getStatus, Constants.WAIT_PASS_AUDIT).count());
        }
        if (permissionService.hasPermi("article:audit:two")) {
            jsonObject.put("newsTwo", gdmmArticleService.lambdaQuery().eq(GdmmArticle::getStatus, Constants.WAIT_AUDIT_TWO).count());
        }
        if (permissionService.hasPermi("article:audit:three")) {
            jsonObject.put("newsThree", gdmmArticleService.lambdaQuery().eq(GdmmArticle::getStatus, Constants.WAIT_AUDIT_THREE).count());
        }
        return AjaxResult.success(jsonObject);
    }


    private void searchableInit(Searchable searchable) {
        if (!Objects.isNull(searchable.getFilterCdtns().get("beginTime"))) {
            searchable.addSearchParam("beginTime", CommonUtil.getTime(searchable.getFilterCdtns().get("beginTime").toString()));
        }
        if (!Objects.isNull(searchable.getFilterCdtns().get("endTime"))) {
            searchable.addSearchParam("endTime", CommonUtil.getTime(searchable.getFilterCdtns().get("endTime").toString()));
        }
        if (searchable.getFilterCdtns().get("statusArr") == null) {
            Integer[] statusArr = new Integer[]{Constants.SAVE_AS_DRAFT,
                    Constants.WAIT_PASS_AUDIT,
                    Constants.NOT_PASS_AUDIT,
                    Constants.YES_PASS_AUDIT,
                    Constants.WAIT_AUDIT_TWO,
                    Constants.WAIT_AUDIT_THREE,
            };
            if (searchable.getFilterCdtns().get("status") != null) {
                statusArr = new Integer[]{Integer.valueOf(searchable.getFilterCdtns().get("status").toString())};
            }
            searchable.addSearchParam("statusArr", statusArr);
        } else {
            searchable.addSearchParam("statusArr", searchable.getFilterCdtns().get("statusArr").toString().split(","));
        }
        searchable.removeSearchFilter("orderProperty");
        searchable.addSearchParam("orderProperty", "sort");
    }

    private void searchableInitAudit(Searchable searchable) {
        if (!Objects.isNull(searchable.getFilterCdtns().get("beginTime"))) {
            searchable.addSearchParam("beginTime", CommonUtil.getTime(searchable.getFilterCdtns().get("beginTime").toString()));
        }
        if (!Objects.isNull(searchable.getFilterCdtns().get("endTime"))) {
            searchable.addSearchParam("endTime", CommonUtil.getTime(searchable.getFilterCdtns().get("endTime").toString()));
        }

        Integer[] statusArr = Constants.AUDIT_ALL;
        if (searchable.getFilterCdtns().get("status") != null) {
            Integer status = Integer.valueOf(searchable.getFilterCdtns().get("status").toString());
            if (Constants.WAIT_PASS_AUDIT.equals(status)) {
                statusArr = new Integer[]{Constants.WAIT_PASS_AUDIT};
            }
            if (Constants.YES_AUDIT.equals(status)) {
                statusArr = new Integer[]{Constants.YES_PASS_AUDIT};
            }
            if (Constants.NOT_SCORE.equals(status)) {
                statusArr = Constants.AUDIT_ALL;
                searchable.addSearchParam("score", Constants.NOT_SCORE);
            }
            if (Constants.YES_SCORE.equals(status)) {
                statusArr = Constants.AUDIT_ALL;
                searchable.addSearchParam("score", Constants.YES_SCORE);
            }
            //已一审，待二审
            if (Constants.WAIT_AUDIT_TWO.equals(status)) {
                statusArr = new Integer[]{Constants.WAIT_AUDIT_TWO};
            }
            //已二审，待三审
            if (Constants.WAIT_AUDIT_THREE.equals(status)) {
                statusArr = new Integer[]{Constants.WAIT_AUDIT_THREE};
            }
            searchable.removeSearchFilter("status", SearchOperator.eq);
        }
        searchable.addSearchParam("statusArr", statusArr);
    }


    @PreAuthorize("@ss.hasAnyPermi('article:article:edit,article:article:pushAdd,article:article:pushRemove,article:article:homeAdd,article:article:homeRemove,article:article:commentAdd,article:article:commentRemove')")
    @PostMapping(value = "/updateBatch")
    public AjaxResult updateBatch(Long[] ids, Integer value, String field) {
        LambdaUpdateWrapper<GdmmArticle> gdmmArticleWrapper = new LambdaUpdateWrapper<>();
        gdmmArticleWrapper.in(GdmmArticle::getId, Arrays.asList(ids));
        if ("sort".equals(field)) {
            gdmmArticleWrapper.set(GdmmArticle::getSort, value);
        }
        if ("isEnabled".equals(field)) {
            gdmmArticleWrapper.set(GdmmArticle::getEnabled, value);
        }
        if ("isHome".equals(field)) {
            gdmmArticleWrapper.set(GdmmArticle::getRecommendHome, value);
        }
        if ("isComment".equals(field)) {
            gdmmArticleWrapper.set(GdmmArticle::getIsComment, value);
        }
        return AjaxResult.success(gdmmArticleService.update(gdmmArticleWrapper));
    }

    private record ArticlePushReq(List<Long> ids,Integer enabled,Integer publishStatus,Integer publishTime){}

    @PreAuthorize("@ss.hasAnyPermi('article:article:pushAdd,article:article:pushRemove')")
    @PostMapping(value = "/pushUpdate")
    public AjaxResult pushUpdate(@RequestBody ArticlePushReq articlePushReq) {

        if(CollUtil.isNotEmpty(articlePushReq.ids)){
            for (Long id : articlePushReq.ids()) {
                if(gdmmArticleService.lambdaUpdate().eq(GdmmArticle::getId,id)
                .set(GdmmArticle::getPublishStatus,articlePushReq.publishStatus()).set(GdmmArticle::getPublishTime,articlePushReq.publishTime())
                .set(GdmmArticle::getEnabled,articlePushReq.enabled())
                .update()){
                    if(ObjUtil.equal(articlePushReq.enabled(), 0)){
                        GdmmArticle gdmmArticle = new GdmmArticle();
                        gdmmArticle.setId(id);
                        gdmmArticle.setPublishStatus(articlePushReq.publishStatus());
                        gdmmArticle.setPublishTime(articlePushReq.publishTime());
                        syncArticleSchedule(gdmmArticle, Constants.SHEQU_UPDATE);
                    }
                }
            }
        }
        return AjaxResult.success();
    }

    /**
     * 接受推送新闻
     */
    @PostMapping("/updateSort")
    public Map updateSort(@RequestBody List<GdmmArticle> gdmmArticles) {
        if(CollUtil.isNotEmpty(gdmmArticles)){
            List<GdmmArticle> updateLists = new ArrayList<>(gdmmArticles.size());
            for (GdmmArticle gdmmArticle : gdmmArticles) {
                GdmmArticle gdmmArticleUpdate = new GdmmArticle();
                gdmmArticleUpdate.setGuilinType(null);
                gdmmArticleUpdate.setId(gdmmArticle.getId());
                gdmmArticleUpdate.setSort(gdmmArticle.getSort());
                updateLists.add(gdmmArticleUpdate);
            }
            return AjaxResult.success(gdmmArticleService.updateBatchById(updateLists));
        }
        return AjaxResult.error();
    }

    /**
     * 获取新闻详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:article:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        GdmmArticle gdmmArticle = gdmmArticleService.getById(id);
        //获取来源名称
        GdmmDictionary gdmmDictionary = gdmmDictionaryService.getById(gdmmArticle.getSourceId());
        gdmmArticle.setSource(gdmmDictionary == null || gdmmDictionary.getName() == null ? "" : gdmmDictionary.getName());
        //获取标签名称
        if (StrUtil.isNotBlank(gdmmArticle.getLabelId()) && !"-1".equals(gdmmArticle.getLabelId())) {
            gdmmDictionary = gdmmDictionaryService.getById(gdmmArticle.getLabelId());
            gdmmArticle.setLabel(gdmmDictionary == null || gdmmDictionary.getName() == null ? "" : gdmmDictionary.getName());
        }

        LambdaQueryWrapper<GdmmArticleComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GdmmArticleComment::getStatus, Constants.STATUS_NORMAL).eq(GdmmArticleComment::getType, Constants.ONE).eq(GdmmArticleComment::getArticleId, gdmmArticle.getId());
        gdmmArticle.setCommentNum(gdmmArticleCommentService.count(wrapper));
        //从redis中获取新闻点击量(点击量按表字段排序，为保证排序一致，不再从redis获取)
        //gdmmArticle.setViewed(CommonUtil.redisCommonFind(RedisKey.ARTICLE_CLICKNUM+"_"+gdmmArticle.getId(),jedisTemplate).intValue());

        GdmmArticleCat gdmmArticleCat = gdmmArticleCatService.getById(gdmmArticle.getArticleCatId());
        gdmmArticle.setCatName(Objects.isNull(gdmmArticleCat) ? "" : gdmmArticleCat.getCatName());
        return AjaxResult.success(gdmmArticle);
    }

    /**
     * 新增新闻
     */
    @PreAuthorize("@ss.hasPermi('article:article:add')")
    @Log(title = "新闻", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmArticle gdmmArticle) {

        if (!Constants.SAVE_AS_DRAFT.equals(gdmmArticle.getStatus())) {
            //是否开启新闻审核
            String isAuditNewsStr = RedisUtil.findString(RedisKey.ARTICLE_IS_AUDITNEWS.getKey());
            logger.info("添加新闻,根据key=" + RedisKey.ARTICLE_IS_AUDITNEWS.getKey() + ",从Redis取 新闻是否开启新闻审核" + isAuditNewsStr);
            Integer isAuditNews = StrUtil.isBlank(isAuditNewsStr) ? 0 : Integer.valueOf(isAuditNewsStr);
            //未开启新闻审核 和图文直播新闻  新增时 状态直接设置为审核通过。
            if (Constants.FOU.equals(isAuditNews) || Constants.ARTICLE_TYPE_TUWENZHIBO == gdmmArticle.getType()) {
                gdmmArticle.setStatus(Constants.YES_PASS_AUDIT);
            } else {
                gdmmArticle.setStatus(Constants.WAIT_PASS_AUDIT);
            }
        }
        if (StrUtil.isNotEmpty(gdmmArticle.getCreateTimeS())) {
            gdmmArticle.setCreateTime(CommonUtil.getIntegerTime(gdmmArticle.getCreateTimeS()));
        } else if (gdmmArticle.getCreateTime() == null) {
            gdmmArticle.setCreateTime(CommonUtil.getTimestamp());
        }
        //无图处理一下
        if (Constants.ARTICLE_TYPE_NO_PICTURE.equals(gdmmArticle.getDisplayMode())) {
            gdmmArticle.setImgUrl("");
        }
        gdmmArticle.setAudioUrl(StrUtil.isBlank(gdmmArticle.getAudioUrl()) ? "" : gdmmArticle.getAudioUrl());
        gdmmArticle.setAuthorEmail(gdmmArticle.getAuthorEmail() == null ? "" : gdmmArticle.getAuthorEmail());
        gdmmArticle.setAuthor(gdmmArticle.getAuthor() == null ? "" : gdmmArticle.getAuthor());
        gdmmArticle.setKeywords(gdmmArticle.getKeywords() == null ? "" : gdmmArticle.getKeywords());
        gdmmArticle.setArticleDetailUrl(gdmmArticle.getArticleDetailUrl() == null ? "" : gdmmArticle.getArticleDetailUrl());
        gdmmArticle.setRemark(gdmmArticle.getRemark() == null ? "" : gdmmArticle.getRemark());
        gdmmArticle.setSubjectCatId(gdmmArticle.getSubjectCatId() == null ? 0 : gdmmArticle.getSubjectCatId());
        gdmmArticle.setSubjectId(gdmmArticle.getSubjectId() == null ? 0 : gdmmArticle.getSubjectId());
        gdmmArticle.setManagerId(SecurityUtils.getUserId());
        gdmmArticle.setIdForChinaSearch(null);
        gdmmArticle.setTimeForChinaSearch(0L);
        gdmmArticle.setOpenType(gdmmArticle.getOpenType() == null ? 0 : gdmmArticle.getOpenType());//打开方式为默认的内容
        gdmmArticle.setLinkUrl(gdmmArticle.getLinkUrl() == null ? "" : gdmmArticle.getLinkUrl());//外链默认为空
        gdmmArticle.setContent(gdmmArticle.getContent() == null ? "" : gdmmArticle.getContent());//内容
        gdmmArticle.setIsHot(0);//外链默认为空
        gdmmArticle.setCardId(0);//默认为0
        gdmmArticle.setGuilinType(0);
        gdmmArticle.setGuilinJson("");
        //会面郑州新闻描述设置为固定值：分享自会面新闻客户端
//        if (CityEnum.HUIMIANZHENGZHOU.getLOCATION_APP().equals(CityEnum.getLOCATION())) {
//            gdmmArticle.setRemark("分享自会面新闻客户端");
//        }

        if (gdmmArticleService.save(gdmmArticle)) {
            GdmmArticle old = gdmmArticleService.getById(gdmmArticle.getId());
            GdmmArticleHis gdmmArticleHis = BeanUtil.copyProperties(old, GdmmArticleHis.class, "id");
            gdmmArticleHis.setArticleId(old.getId());
            gdmmArticleHis.setOperTime(new Date());
            gdmmArticleHis.setOperId(SecurityUtils.getUserId().toString());
            gdmmArticleHis.setOperName(SecurityUtils.getUsername());
            gdmmArticleHisService.save(gdmmArticleHis);
            syncArticleSchedule(gdmmArticle, Constants.SHEQU_ADD);
            applicationEventPublisher.publishEvent(new ArticleEvent(this, gdmmArticle, EventAction.builder().type(BusinessType.INSERT).build()));
            LogAspect.setErrMsg(StrUtil.format("新闻:{}，操作成功", gdmmArticle.getTitle()));
        }
        return AjaxResult.success(gdmmArticle.getId());
    }

    /**
     * 修改新闻
     */
    @PreAuthorize("@ss.hasPermi('article:article:edit')")
    @Log(title = "新闻", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmArticle gdmmArticle) {
        if (!Constants.SAVE_AS_DRAFT.equals(gdmmArticle.getStatus())) {
            //是否开启新闻审核
            String isAuditNewsStr = RedisUtil.findString(RedisKey.ARTICLE_IS_AUDITNEWS.getKey());
            logger.info("添加新闻,根据key=" + RedisKey.ARTICLE_IS_AUDITNEWS.getKey() + ",从Redis取 新闻是否开启新闻审核" + isAuditNewsStr);
            Integer isAuditNews = StrUtil.isBlank(isAuditNewsStr) ? 0 : Integer.valueOf(isAuditNewsStr);
            //未开启新闻审核 和图文直播新闻  新增时 状态直接设置为审核通过。
            if (Constants.FOU.equals(isAuditNews) || Constants.ARTICLE_TYPE_TUWENZHIBO == gdmmArticle.getType()) {
                gdmmArticle.setStatus(Constants.YES_PASS_AUDIT);
            } else {
                gdmmArticle.setStatus(Constants.WAIT_PASS_AUDIT);
            }
        }

        if (StrUtil.isNotEmpty(gdmmArticle.getCreateTimeS())) {
            gdmmArticle.setCreateTime(CommonUtil.getIntegerTime(gdmmArticle.getCreateTimeS()));
        }

        //无图处理一下
        if (Constants.ARTICLE_TYPE_NO_PICTURE.equals(gdmmArticle.getDisplayMode())) {
            gdmmArticle.setImgUrl("");
        }
        gdmmArticle.setVideoUrl(gdmmArticle.getVideoUrl() == null ? "" : gdmmArticle.getVideoUrl());
        gdmmArticle.setAudioUrl(gdmmArticle.getAudioUrl() == null ? "" : gdmmArticle.getAudioUrl());
        //防止批量修改
        gdmmArticle.setId(gdmmArticle.getId() == null ? -1 : gdmmArticle.getId());
        //增加编辑和记者的修改
        gdmmArticle.setEditor(StrUtil.isEmpty(gdmmArticle.getEditor()) ? "" : gdmmArticle.getEditor());
        gdmmArticle.setReporter(StrUtil.isEmpty(gdmmArticle.getReporter()) ? "" : gdmmArticle.getReporter());
        gdmmArticle.setContent(StrUtil.isEmpty(gdmmArticle.getContent()) ? "" : gdmmArticle.getContent());

        gdmmArticle.setQrCodeUrl("");//新闻修改时二维码图片置空

        if (gdmmArticleService.updateArticle(gdmmArticle)) {
            syncArticleSchedule(gdmmArticle, Constants.SHEQU_UPDATE);
            applicationEventPublisher.publishEvent(new ArticleEvent(this, gdmmArticle, EventAction.builder().type(BusinessType.AUDIT).operator(SecurityUtils.getLoginUser())
                    .build()));
            LogAspect.setErrMsg(StrUtil.format("新闻:{}，操作成功", gdmmArticle.getTitle()));
        }

        return success();
    }

    @Log(title = "新闻", businessType = BusinessType.AUDIT)
    @PreAuthorize("@ss.hasPermi('article:article:audit')")
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody GdmmArticle gdmmArticle) {
        GdmmArticle oldArticle = gdmmArticleService.lambdaQuery().select(GdmmArticle::getStatus).eq(GdmmArticle::getId, gdmmArticle.getId()).one();
        if (oldArticle == null) {
            return AjaxResult.error("新闻不存在");
        }

        if (StrUtil.isNotEmpty(gdmmArticle.getCreateTimeS())) {
            gdmmArticle.setCreateTime(CommonUtil.getIntegerTime(gdmmArticle.getCreateTimeS()));
        }

        //无图处理一下
        if (Constants.ARTICLE_TYPE_NO_PICTURE.equals(gdmmArticle.getDisplayMode())) {
            gdmmArticle.setImgUrl("");
        }
        gdmmArticle.setVideoUrl(gdmmArticle.getVideoUrl() == null ? "" : gdmmArticle.getVideoUrl());
        gdmmArticle.setAudioUrl(gdmmArticle.getAudioUrl() == null ? "" : gdmmArticle.getAudioUrl());
        //防止批量修改
        gdmmArticle.setId(gdmmArticle.getId() == null ? -1 : gdmmArticle.getId());
        //增加编辑和记者的修改
        gdmmArticle.setEditor(StrUtil.isEmpty(gdmmArticle.getEditor()) ? "" : gdmmArticle.getEditor());
        gdmmArticle.setReporter(StrUtil.isEmpty(gdmmArticle.getReporter()) ? "" : gdmmArticle.getReporter());
        String auditStr = "";
        if (Constants.YES_PASS_AUDIT.equals(gdmmArticle.getStatus())) {//根据配置和当前值进行判断下一个状态
            String isAuditNewsStr = RedisUtil.findString(RedisKey.ARTICLE_IS_AUDITNEWS.getKey());
            Integer isAuditNews = StrUtil.isBlank(isAuditNewsStr) ? 0 : Integer.valueOf(isAuditNewsStr);
            gdmmArticle.setStatus(auditStatus(oldArticle.getStatus(), isAuditNews));
            auditStr = Constants.AUDIT_STATUS_MAP_BEFORE.get(oldArticle.getStatus()) + "通过，" + Constants.AUDIT_STATUS_MAP_AFTER.get(gdmmArticle.getStatus());
        } else {
            auditStr = Constants.AUDIT_STATUS_MAP_BEFORE.get(oldArticle.getStatus()) + "拒绝";
        }

        if (gdmmArticleService.updateArticle(gdmmArticle)) {
            applicationEventPublisher.publishEvent(new ArticleEvent(this, gdmmArticle, EventAction.builder().type(BusinessType.AUDIT)
                    .operator(SecurityUtils.getLoginUser())
                    .build()));
            LogAspect.setErrMsg(StrUtil.format("新闻:[{}] {}，操作成功", gdmmArticle.getTitle(),
                    auditStr));
            return success();
        }
        return error("审核失败");
    }

    private int auditStatus(int currentStatus, int configSetting) {
        // 如果当前状态是待三审，无论配置如何，都设置为已审批
        if (currentStatus == 9) {
            return 0;
        }

        switch (configSetting) {
            case 0: // 无需审核
                return 0; // 直接设置为已审批

            case 1: // 一审
                return 0; // 直接设置为已审批

            case 2: // 二审
                if (currentStatus == 8) {
                    // 如果当前状态是待二审，直接设置为已审批
                    return 0;
                } else if (currentStatus == 3) {
                    // 如果当前状态是待一审，则设置为待二审
                    return 8;
                } else {
                    // 其他情况，保持当前状态不变
                    return currentStatus;
                }

            case 3: // 三审
                if (currentStatus == 8) {
                    // 如果当前状态是待二审，则设置为待三审
                    return 9;
                } else if (currentStatus == 3) {
                    // 如果当前状态是待一审，则设置为待二审
                    return 8;
                } else {
                    // 其他情况，保持当前状态不变
                    return currentStatus;
                }

            default:
                // 非法配置设定，返回当前状态
                return currentStatus;
        }
    }

    /**
     * 删除新闻
     */
    @PreAuthorize("@ss.hasPermi('article:article:remove')")
    @Log(title = "新闻", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        StringBuilder sb = new StringBuilder("新闻：");
        for (Long id : ids) {
            GdmmArticle gdmmArticle = gdmmArticleService.getById(id);

            if (gdmmArticle.getCardId() > 0 && gdmmArticle.getSubjectId() == 0) {
                logger.error("后台删除新闻操作:删除的新闻包含首页卡片新闻,不能进行删除操作！！！");
                return AjaxResult.error("稿件已推荐至卡片，请取消推荐后，再操作删除");
            }

            LambdaUpdateWrapper<GdmmArticle> gdmmArticleWrapper = new LambdaUpdateWrapper<>();
            //专题新闻 解绑专题
            if (gdmmArticle.getSubjectId() > 0) {
                gdmmArticleWrapper.set(GdmmArticle::getSubjectId, Constants.FOU).set(GdmmArticle::getSubjectCatId, Constants.FOU).eq(GdmmArticle::getId, id);
            } else {
                gdmmArticleWrapper.eq(GdmmArticle::getId, id).set(GdmmArticle::getStatus, Constants.STATUS_DELETE);
                syncArticleSchedule(gdmmArticle, Constants.SHEQU_DELETE);
            }
            if (gdmmArticleService.update(gdmmArticleWrapper)) {
                applicationEventPublisher.publishEvent(new ArticleEvent(this, gdmmArticle, EventAction.builder().type(BusinessType.DELETE).build()));
                sb.append(gdmmArticle.getTitle()).append("，");
            }
        }
        sb.append("操作成功");
        LogAspect.setErrMsg(StrUtil.format(sb.toString()));
        return AjaxResult.success();
    }

    /**
     * 获取配置资讯详情
     *
     * @return
     */
    @RequestMapping(value = "/setting", method = RequestMethod.GET)
    @PreAuthorize("@ss.hasPermi('article:article:setting')")
    public AjaxResult setting() {
        GdmmParams ecsParam = new GdmmParams();
        AjaxResult result = AjaxResult.success();
        try {
            //查询帮助表获取资讯设置参数
            LambdaQueryWrapper<GdmmParams> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(GdmmParams::getModule, "ARTICLE").eq(GdmmParams::getCode, "PARAM");
            List<GdmmParams> ecsParamList = gdmmParamsService.list(lambdaQueryWrapper);
            if (ecsParamList != null && ecsParamList.size() > 0) {
                ecsParam = ecsParamList.get(0);
            }

            String value = ecsParam.getValue();
            if (value != null && !"".equals(value)) {
                JSONObject json = JSONObject.parseObject(value);
                String isComment = json.containsKey("isComment") ? json.getString("isComment") : "0";
                String isExamine = json.containsKey("isExamine") ? json.getString("isExamine") : "0";
                String isClickNum = json.containsKey("isClickNum") ? json.getString("isClickNum") : "0";
                String isSource = json.containsKey("isSource") ? json.getString("isSource") : "0";
                String isLabel = json.containsKey("isLabel") ? json.getString("isLabel") : "0";
                String isShowNews = json.containsKey("isShowNews") ? json.getString("isShowNews") : "0";
                String isAuditNews = json.containsKey("isAuditNews") ? json.getString("isAuditNews") : "0";
                String isOpenShortVideoComment = json.containsKey("isOpenShortVideoComment") ? json.getString("isOpenShortVideoComment") : "0";
                String isAuditShortVideoComment = json.containsKey("isAuditShortVideoComment") ? json.getString("isAuditShortVideoComment") : "0";
                Object isUserWaterMark = json.get("isUserWaterMark");//是否使用水印(为啥不使用 getString,如果该key不存在时会报错)
                String initViewMin = json.containsKey("initViewMin") ? json.getString("initViewMin") : "0";
                String initViewMax = json.containsKey("initViewMax") ? json.getString("initViewMax") : "0";
                String hotDayNum = json.containsKey("hotDayNum") ? json.getString("hotDayNum") : "0";

                //红云视频直播
                //是否开启红云评论审核
                String isAuditLiveComment = json.containsKey("isAuditLiveComment") ? json.getString("isAuditLiveComment") : "0";
                String isOpentLiveComment = json.containsKey("isOpentLiveComment") ? json.getString("isOpentLiveComment") : "0";

                Map<String, String> model = new HashMap<>();
                //参数返回页面
                model.put("isComment", isComment);
                model.put("isExamine", isExamine);
                model.put("isClickNum", isClickNum);
                model.put("isSource", isSource);
                model.put("isLabel", isLabel);
                model.put("isShowNews", isShowNews);
                model.put("isAuditNews", isAuditNews);
                model.put("isOpenShortVideoComment", isOpenShortVideoComment);
                model.put("isAuditShortVideoComment", isAuditShortVideoComment);
                model.put("isAuditLiveComment", isAuditLiveComment);
                model.put("isOpentLiveComment", isOpentLiveComment);
                model.put("isUserWaterMark", isUserWaterMark == null ? "0" : String.valueOf(isUserWaterMark));//是否使用水印默认是不使用
                model.put("initViewMin", initViewMin);
                model.put("initViewMax", initViewMax);
                model.put("hotDayNum", hotDayNum);
                //如果开启水印,获取水印图片路径
                if (isUserWaterMark != null && "1".equals(String.valueOf(isUserWaterMark))) {
                    model.put("waterMarkImgUrlOss", RuoYiConfig.getProfile() + "/upload/image/newwatermark.png");//水印图片路径
                }
                result.put("data", model);
            }
        } catch (Exception e) {
            logger.error("查询系统设置出错", e);
        }
        result.put("ecsParam", ecsParam);
        return result;
    }

    @PreAuthorize("@ss.hasPermi('article:article:setting')")
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) {
        // 上传并返回新文件名称
        OSS ossClient = (new OSSClientBuilder()).build(AliOssConfig.getEndpoint(), AliOssConfig.getAccessKeyId(), AliOssConfig.getAccessKeySecret());
        try {
            ossClient.putObject(AliOssConfig.getBucketName(), RuoYiConfig.getProfile() + "/upload/image/newwatermark.png", file.getInputStream());
            String url = AliOssConfig.getUrl() + RuoYiConfig.getProfile() + "/upload/image/newwatermark.png";
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", RuoYiConfig.getProfile() + "/upload/image/newwatermark.png");
            ajax.put("newFileName", RuoYiConfig.getProfile() + "/upload/image/newwatermark.png");
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (OSSException var12) {
            var12.printStackTrace();
        } catch (ClientException var13) {
            var13.printStackTrace();
        } catch (Exception var14) {
            var14.printStackTrace();
        } finally {
            ossClient.shutdown();
        }
        return AjaxResult.success();
    }

    /**
     * 保存资讯设置
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('article:article:setting')")
    @RequestMapping(value = "/saveSetting", method = RequestMethod.POST)
    public AjaxResult saveSetting(@RequestBody GdmmParams ecsParam) {
        JSONObject jsonObject = JSONObject.parseObject(ecsParam.getValue());
        String isComment = jsonObject.getString("isComment");
        String isExamine = jsonObject.getString("isExamine");
        String isClickNum = jsonObject.getString("isClickNum");
        String isSource = jsonObject.getString("isSource");
        String isLabel = jsonObject.getString("isLabel");
        String isShowNews = jsonObject.getString("isShowNews");
        String isAuditNews = jsonObject.getString("isAuditNews");
        String isOpenShortVideoComment = jsonObject.getString("isOpenShortVideoComment");
        String isAuditShortVideoComment = jsonObject.getString("isAuditShortVideoComment");
        String isAuditLiveComment = StrUtil.defaultIfBlank(jsonObject.getString("isAuditLiveComment"),"0");
        String isOpentLiveComment = StrUtil.defaultIfBlank(jsonObject.getString("isOpentLiveComment"),"0");
        String isUserWaterMark = jsonObject.getString("isUserWaterMark");
        String initViewMin = jsonObject.getString("initViewMin");
        String initViewMax = jsonObject.getString("initViewMax");
        String hotDayNum = jsonObject.getString("hotDayNum");
        String isAuditVideo = jsonObject.getString("isAuditVideo");
        //开启/关闭 红云直播 评论
        if (StrUtil.isNotEmpty(isOpentLiveComment)) {
            HashMap liveMap = new HashMap();
            liveMap.put("isComment", Integer.parseInt(isOpentLiveComment));
        }

        //使用水印上传水印图片到本地
//        if ("1".equals(isUserWaterMark) && waterMarkImgFile != null && !waterMarkImgFile.isEmpty()) {
//            ImageUtil.uploadFileToLocal(waterMarkImgFile, request, "/upload/image/", "newwatermark.png");
//        }

        ecsParam.setUpdateTime(CommonUtil.getTimestamp());
        gdmmParamsService.updateById(ecsParam);

        //资讯设置保存到 redis
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_COMMENT.getKey(), isComment);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_EXAMINE.getKey(), isExamine);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_CLICKNUM.getKey(), isClickNum);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_SOURCE.getKey(), isSource);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_LABEL.getKey(), isLabel);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_SHOWNEWS.getKey(), isShowNews);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_AUDITNEWS.getKey(), isAuditNews);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_AUDITVIDEO.getKey(), isAuditVideo);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_OPEN_SHORT_VIDEO_COMMENT.getKey(), isOpenShortVideoComment);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_AUDIT_SHORT_VIDEO_COMMENT.getKey(), isAuditShortVideoComment);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_OPENT_LIVE_COMMENT.getKey(), isOpentLiveComment);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_AUDIT_LIVE_COMMENT.getKey(), isAuditLiveComment);
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_USERWATERMARK.getKey(), isUserWaterMark);//是否使用水印
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_INITVIEW_MIN.getKey(), initViewMin);//初始点击量最小值
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_INITVIEW_MAX.getKey(), initViewMax);//初始点击量最大值
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_HOT_DAYNUM.getKey(), hotDayNum);//热榜查询天数
        return toAjax(true);
    }

    /**
     * 新闻点击量从redis同步到数据库
     */
    @RequestMapping(value = "/redis2dbArticle", method = RequestMethod.GET)
    @ResponseBody
    public AjaxResult redis2dbArticle() {
        //先判断是否正在同步，同步状态存redis 1：正在同步 其他状态：未同步
        String value = RedisUtil.findString(RedisKey.ARTICLE_IS_SYNC_CLICKNUM.getKey());
        if (Constants.YES.equals(value)) {
            logger.error("redis 持久化新闻点击量正在进行......");
            return AjaxResult.error("正在同步中，请不要重复操作");
        }
        gdmmArticleService.redis2dbArticle();
        return AjaxResult.success();
    }

    @Resource
    private ScheduleUtil scheduleUtil;

    //保存修改删除新闻时，同步Schedule
    private void syncArticleSchedule(GdmmArticle gdmmArticle, String type) {
        if (Constants.SHEQU_ADD.equals(type)) {//创建定时触发器
            if (gdmmArticle != null && gdmmArticle.getPublishStatus() != null && gdmmArticle.getPublishStatus() > 0) {
                logger.debug("新增新闻同步Schedule，id={}", gdmmArticle.getId());
                scheduleUtil.sendHTTPToScheduleForArticle(Constants.SHEQU_ADD, gdmmArticle.getId(), Constants.MAGAGE_BACK);
            }
            return;
        }
        if (Constants.SHEQU_UPDATE.equals(type)) {//修改定时触发器
            if (gdmmArticle != null && gdmmArticle.getPublishStatus() != null && gdmmArticle.getPublishStatus() > 0) {
                logger.debug("修改新闻同步Schedule，id={}", gdmmArticle.getId());
                scheduleUtil.sendHTTPToScheduleForArticle(Constants.SHEQU_UPDATE, gdmmArticle.getId(), Constants.MAGAGE_BACK);
            }
            return;
        }
        if (Constants.SHEQU_DELETE.equals(type)) {//删除定时触发器
            if (gdmmArticle != null && gdmmArticle.getPublishStatus() != null && gdmmArticle.getPublishStatus() > 0) {
                logger.debug("删除新闻同步Schedule，id={}", gdmmArticle.getId());
                scheduleUtil.sendHTTPToScheduleForArticle(Constants.SHEQU_DELETE, gdmmArticle.getId(), Constants.MAGAGE_BACK);
            }
            return;
        }
    }

}
