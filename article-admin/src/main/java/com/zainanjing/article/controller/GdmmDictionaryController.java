package com.zainanjing.article.controller;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.Sort;
import com.ruoyi.common.core.search.SearchOperator;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.constant.DictionaryEnum;
import com.zainanjing.article.domain.GdmmDictionary;
import com.zainanjing.article.domain.GdmmVideo;
import com.zainanjing.article.service.IGdmmDictionaryService;
import com.zainanjing.article.service.IGdmmVideoService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 数据字典Controller
 *
 * <AUTHOR>
 * @date 2023-10-19
 */
@RestController
@RequestMapping("/article/dictionary")
public class GdmmDictionaryController extends BaseController {
    @Autowired
    private IGdmmDictionaryService gdmmDictionaryService;

    @Resource
    private IGdmmVideoService gdmmVideoService;

    /**
     * 查询数据字典列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        if (!searchable.getFilterCdtns().containsKey("status")) {
            searchable.addSearchFilter("status", SearchOperator.ne, Constants.STATUS_DELETE);
        }
        if (Objects.isNull(searchable.getSort())) {
            searchable.addSort(Sort.Direction.DESC, "sort");
            searchable.addSort(Sort.Direction.DESC, "id");
        }
        IPage<GdmmDictionary> page = gdmmDictionaryService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取数据字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:dictionary:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(gdmmDictionaryService.getById(id));
    }

    /**
     * 新增数据字典
     */
    @PreAuthorize("@ss.hasPermi('article:dictionary:add')")
    @Log(title = "数据字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmDictionary gdmmDictionary) {
        gdmmDictionary.setCreateTime(CommonUtil.getLongTimestamp());
        gdmmDictionary.setUpdateTime(CommonUtil.getLongTimestamp());
        return toAjax(gdmmDictionaryService.save(gdmmDictionary));
    }

    /**
     * 修改数据字典
     */
    @PreAuthorize("@ss.hasPermi('article:dictionary:edit')")
    @Log(title = "数据字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmDictionary gdmmDictionary) {
        gdmmDictionary.setUpdateTime(CommonUtil.getLongTimestamp());
        return toAjax(gdmmDictionaryService.updateById(gdmmDictionary));
    }

    /**
     * 获取初始化值
     */
    @RequestMapping(value = "/gdmmDictionary", method = RequestMethod.GET)
    public AjaxResult GdmmDictionary(GdmmDictionary gdmmDictionary) {
        DictionaryEnum.initGdmmDictionary(gdmmDictionary);
        return AjaxResult.success(gdmmDictionary);
    }

    /**
     * 删除数据字典
     */
    @PreAuthorize("@ss.hasPermi('article:dictionary:remove')")
    @Log(title = "数据字典", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        try {
            if (CollUtil.isNotEmpty(ids)) {
                for (Long id : ids) {
                    GdmmDictionary gd = gdmmDictionaryService.getById(id);
                    if ((DictionaryEnum.VIDEO_CAT.getModule().equals(gd.getModule()) && DictionaryEnum.VIDEO_CAT.getCode().equals(gd.getCode()))
                            || (DictionaryEnum.DOCUMENTARY_CAT.getModule().equals(gd.getModule()) && DictionaryEnum.DOCUMENTARY_CAT.getCode().equals(gd.getCode()))) {
                        LambdaQueryWrapper<GdmmVideo> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(GdmmVideo::getCatId, id).eq(GdmmVideo::getStatus, Constants.STATUS_NORMAL);
                        Long total = gdmmVideoService.count(queryWrapper);
                        if (total > 0) {
                            logger.error("选中的分类下已存在短视频，暂不可删除。");
                            return AjaxResult.error("选中的分类下已存在短视频，暂不可删除。");
                        }
                    }
                }
                paramMap.put("ids", ids);
                paramMap.put("status", Constants.STATUS_DELETE);
                LambdaUpdateWrapper<GdmmDictionary> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(GdmmDictionary::getId, ids).set(GdmmDictionary::getStatus, Constants.STATUS_DELETE);
                gdmmDictionaryService.update(updateWrapper);
            } else {
                logger.error("删除字典表的id不能为空");
                return AjaxResult.error("删除字典表的id不能为空");
            }
        } catch (Exception e) {
            logger.error("删除字典表报错", e);
            return AjaxResult.error("删除字典表报错");
        }
        return AjaxResult.success();
    }
}
