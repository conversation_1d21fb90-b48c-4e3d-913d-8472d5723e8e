package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.page.Sort;
import com.ruoyi.common.core.search.SearchOperator;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmPaper;
import com.zainanjing.article.service.IGdmmPaperService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 广电报Controller
 *
 * <AUTHOR>
 * @date 2023-10-19
 */
@RestController
@RequestMapping("/article/paper")
public class GdmmPaperController extends BaseController {
    @Autowired
    private IGdmmPaperService gdmmPaperService;

    /**
     * 查询广电报列表
     */
    @PreAuthorize("@ss.hasPermi('article:paper:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        searchable.addSearchFilter("status", SearchOperator.eq, Constants.STATUS_NORMAL);
        searchable.addSort(Sort.Direction.DESC, "id");
        IPage<GdmmPaper> page = gdmmPaperService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取广电报详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:paper:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(gdmmPaperService.getById(id));
    }

    /**
     * 新增广电报
     */
    @PreAuthorize("@ss.hasPermi('article:paper:add')")
    @Log(title = "广电报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmPaper gdmmPaper) {
        gdmmPaper.setCreateTime(CommonUtil.getTimestamp());
        gdmmPaper.setUpdateTime(CommonUtil.getTimestamp());
        gdmmPaper.setManagerId(SecurityUtils.getUserId().intValue());
        return toAjax(gdmmPaperService.save(gdmmPaper));
    }

    /**
     * 修改广电报
     */
    @PreAuthorize("@ss.hasPermi('article:paper:edit')")
    @Log(title = "广电报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmPaper gdmmPaper) {
        gdmmPaper.setUpdateTime(CommonUtil.getTimestamp());
        return toAjax(gdmmPaperService.updateById(gdmmPaper));
    }

    /**
     * 删除广电报
     */
    @PreAuthorize("@ss.hasPermi('article:paper:remove')")
    @Log(title = "广电报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        LambdaUpdateWrapper<GdmmPaper> gdmmNoticeWrapper = new LambdaUpdateWrapper<>();
        gdmmNoticeWrapper.in(GdmmPaper::getId, ids).set(GdmmPaper::getStatus, Constants.STATUS_DELETE);
        return toAjax(gdmmPaperService.update(gdmmNoticeWrapper));
    }
}
