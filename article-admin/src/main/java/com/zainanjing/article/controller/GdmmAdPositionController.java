package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.article.domain.GdmmAdPosition;
import com.zainanjing.article.service.IGdmmAdPositionService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 广告位置Controller
 *
 * <AUTHOR>
 * @date 2023-11-05
 */
@RestController
@RequestMapping("/article/position")
public class GdmmAdPositionController extends BaseController {
    @Autowired
    private IGdmmAdPositionService gdmmAdPositionService;

    /**
     * 查询广告位置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<GdmmAdPosition> page = gdmmAdPositionService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取广告位置详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gdmmAdPositionService.getById(id));
    }

    /**
     * 新增广告位置
     */
    @Log(title = "广告位置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmAdPosition gdmmAdPosition) {
        return toAjax(gdmmAdPositionService.save(gdmmAdPosition));
    }

    /**
     * 修改广告位置
     */
    @Log(title = "广告位置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmAdPosition gdmmAdPosition) {
        return toAjax(gdmmAdPositionService.updateById(gdmmAdPosition));
    }

    /**
     * 删除广告位置
     */
    @Log(title = "广告位置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(gdmmAdPositionService.removeByIds(ids));
    }
}
