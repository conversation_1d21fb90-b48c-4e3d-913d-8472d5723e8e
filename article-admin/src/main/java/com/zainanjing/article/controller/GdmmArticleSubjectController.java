package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.page.Sort;
import com.ruoyi.common.core.search.SearchOperator;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmArticle;
import com.zainanjing.article.domain.GdmmArticleSubject;
import com.zainanjing.article.service.IGdmmArticleService;
import com.zainanjing.article.service.IGdmmArticleSubjectService;
import com.zainanjing.article.service.IGdmmVideoService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新闻专题Controller
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@RestController
@RequestMapping("/article/subject")
public class GdmmArticleSubjectController extends BaseController {

    @Autowired
    private IGdmmArticleSubjectService gdmmArticleSubjectService;

    @Autowired
    private IGdmmArticleService gdmmArticleService;

    @Autowired
    private IGdmmVideoService gdmmVideoService;

    /**
     * 查询新闻专题列表
     */
    @PreAuthorize("@ss.hasPermi('article:subject:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        searchable.addSearchFilter("status", SearchOperator.eq, Constants.STATUS_NORMAL);
        searchable.addSort(Sort.Direction.DESC, "id");
        IPage<GdmmArticleSubject> page = gdmmArticleSubjectService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取新闻专题详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:subject:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(gdmmArticleSubjectService.getById(id));
    }

    /**
     * 新增新闻专题
     */
    @PreAuthorize("@ss.hasPermi('article:subject:add')")
    @Log(title = "新闻专题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmArticleSubject gdmmArticleSubject) {
        gdmmArticleSubject.setCreateTime(CommonUtil.getTimestamp());
        gdmmArticleSubject.setUpdateTime(CommonUtil.getTimestamp());
        gdmmArticleSubject.setManagerId(SecurityUtils.getUserId().intValue());
        gdmmArticleSubject.setCardId(0);//卡片默认为0
        gdmmArticleSubject.setType(gdmmArticleSubject.getType() == null ? 0 : gdmmArticleSubject.getType());//默认为0
        return toAjax(gdmmArticleSubjectService.save(gdmmArticleSubject));
    }

    /**
     * 修改新闻专题
     */
    @PreAuthorize("@ss.hasPermi('article:subject:edit')")
    @Log(title = "新闻专题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmArticleSubject gdmmArticleSubject) {
        gdmmArticleSubject.setUpdateTime(CommonUtil.getTimestamp());
        return toAjax(gdmmArticleSubjectService.updateById(gdmmArticleSubject));
    }

    /**
     * 删除新闻专题
     */
    @PreAuthorize("@ss.hasPermi('article:subject:remove')")
    @Log(title = "新闻专题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        LambdaUpdateWrapper<GdmmArticleSubject> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmArticleSubject::getId, ids).set(GdmmArticleSubject::getStatus, Constants.STATUS_DELETE);
        if (gdmmArticleSubjectService.update(updateWrapper)) {
            LambdaUpdateWrapper<GdmmArticle> updateArticle = new LambdaUpdateWrapper<>();
            updateArticle.in(GdmmArticle::getSubjectId, ids).set(GdmmArticle::getSubjectId, Constants.FOU).set(GdmmArticle::getSubjectCatId, Constants.FOU);
            gdmmArticleService.update(updateArticle);
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.success("删除失败");
    }

    @RequestMapping(value = "/updateBatch", method = RequestMethod.POST)
    public AjaxResult updateBatch(@RequestParam("ids") List<Long> ids, Integer value, String field) {
        //后台操作新闻专题是否推荐首页
        logger.debug("后台操作新闻专题是否推荐首页");

        LambdaUpdateWrapper<GdmmArticleSubject> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmArticleSubject::getId, ids);
        if ("isEnabled".equals(field)) {
            updateWrapper.set(GdmmArticleSubject::getEnabled, value);
        }
        if ("isHome".equals(field)) {
            updateWrapper.set(GdmmArticleSubject::getRecommendHome, value);
        }
        return AjaxResult.success(gdmmArticleSubjectService.update(updateWrapper));
    }

    @RequestMapping(value = "/batchToSubject", method = RequestMethod.POST)
    public AjaxResult batchAdd(Long[] ids, Integer subjectId, Integer subjectCatId, Integer type) {
        //后台操作新闻专题是否推荐首页
        logger.debug("后台操作新闻批量导入专题");
        UpdateWrapper updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids);
        updateWrapper.set("subject_id", subjectId);
        updateWrapper.set("subject_cat_id", subjectCatId);
        if (type != null && type.intValue() == 0) {//新闻
            gdmmArticleService.update(updateWrapper);
        }
        if (type != null && type.intValue() == 1) {//短视频
            gdmmVideoService.update(updateWrapper);
        }
        return AjaxResult.success();

    }

}
