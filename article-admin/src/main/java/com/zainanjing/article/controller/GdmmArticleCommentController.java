package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmArticleComment;
import com.zainanjing.article.service.IGdmmArticleCommentService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

/**
 * 资讯评论Controller
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@RestController
@RequestMapping("/article/articleComment")
public class GdmmArticleCommentController extends BaseController {
    @Autowired
    private IGdmmArticleCommentService gdmmArticleCommentService;

    /**
     * 查询资讯评论列表
     */
    @PreAuthorize("@ss.hasPermi('article:articleComment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        List<Integer> statusArr = Arrays.asList(Constants.STATUS_NOW, Constants.STATUS_YES);
        searchable.addSearchParam("statusArr", statusArr);
        IPage<GdmmArticleComment> page = gdmmArticleCommentService.selectGdmmArticleCommentList(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取资讯评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:articleComment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gdmmArticleCommentService.getById(id));
    }

    /**
     * 新增资讯评论
     */
    @PreAuthorize("@ss.hasPermi('article:articleComment:add')")
    @Log(title = "资讯评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmArticleComment gdmmArticleComment) {
        return toAjax(gdmmArticleCommentService.save(gdmmArticleComment));
    }

    /**
     * 修改资讯评论
     */
    @PreAuthorize("@ss.hasPermi('article:articleComment:edit')")
    @Log(title = "资讯评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmArticleComment gdmmArticleComment) {
        GdmmArticleComment dbArticleComment = gdmmArticleCommentService.getById(gdmmArticleComment.getId());
        gdmmArticleComment.setUpdateTime(CommonUtil.getTimestamp());
        updateRedis(dbArticleComment.getArticleId(), dbArticleComment.getStatus(), gdmmArticleComment.getStatus());
        return toAjax(gdmmArticleCommentService.updateById(gdmmArticleComment));
    }

    /**
     * 删除资讯评论
     */
    @PreAuthorize("@ss.hasPermi('article:articleComment:remove')")
    @Log(title = "资讯评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        LambdaUpdateWrapper<GdmmArticleComment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmArticleComment::getId, ids).set(GdmmArticleComment::getStatus, Constants.AUCTION_SCHEDULE_STATUS_DELETE);
        minusNum(ids);
        return toAjax(gdmmArticleCommentService.update(updateWrapper));
    }


    /**
     * @param ids
     * @return Message
     * @Title:audit
     * @Description 审核新闻评论
     */
    @Log(title = "资讯评论审核", businessType = BusinessType.OTHER)
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public AjaxResult audit(Long[] ids, Integer status) {
        LambdaUpdateWrapper<GdmmArticleComment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmArticleComment::getId, ids).set(GdmmArticleComment::getStatus, status);
        if (Constants.FOU.equals(status)) {
            addNum(Arrays.asList(ids));
            //如果是审核拒绝，先查ids里已审核的id，再-1 （已经是未审核的 不用减）
        } else if (Constants.SHI.equals(status)) {
            minusNum(Arrays.asList(ids));
        }
        return toAjax(gdmmArticleCommentService.update(updateWrapper));
    }

    private void updateRedis(Integer articleId, Integer dbStatus, Integer updateStatus) {
        //状态发生了修改
        if (!dbStatus.equals(updateStatus)) {
            //如果改为审核通过 评论数+1
            if (Constants.FOU.equals(updateStatus)) {
                CommonUtil.redisCommonAdd(Constants.ARTICLE + "_" + Constants.COMMENTNUM + "_" + articleId);
                //如果改为审核拒绝  评论数-1
            } else if (Constants.SHI.equals(updateStatus)) {
                CommonUtil.redisCommonMinus(Constants.ARTICLE + "_" + Constants.COMMENTNUM + "_" + articleId);
            }
        }
    }

    /**
     * @param ids void
     * @throws SQLException
     * @Title:minusNum
     * @Description 已审核的状态修改，需要减去评论数
     */
    private void minusNum(List<Long> ids) {
        LambdaQueryWrapper<GdmmArticleComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GdmmArticleComment::getId, ids).eq(GdmmArticleComment::getStatus, Constants.FOU);
        List<GdmmArticleComment> list = gdmmArticleCommentService.list(queryWrapper);
        for (GdmmArticleComment gdmmArticleComment : list) {
            Integer articleId = gdmmArticleComment.getArticleId();
            CommonUtil.redisCommonMinus(Constants.ARTICLE + "_" + Constants.COMMENTNUM + "_" + articleId);
        }
    }


    /**
     * @param ids
     * @throws SQLException void
     * @Title:addNum
     * @Description 未审核的状态修改为已审核 ，评论数加+1
     */
    private void addNum(List<Long> ids) {
        LambdaQueryWrapper<GdmmArticleComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GdmmArticleComment::getId, ids).eq(GdmmArticleComment::getStatus, Constants.SHI);
        List<GdmmArticleComment> list = gdmmArticleCommentService.list(queryWrapper);
        for (GdmmArticleComment gdmmArticleComment : list) {
            Integer articleId = gdmmArticleComment.getArticleId();
            CommonUtil.redisCommonAdd(Constants.ARTICLE + "_" + Constants.COMMENTNUM + "_" + articleId);
        }
    }
}
