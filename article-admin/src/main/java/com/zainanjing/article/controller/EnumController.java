package com.zainanjing.article.controller;


import com.ruoyi.common.core.domain.AjaxResult;
import com.zainanjing.article.constant.LinkModuleEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/article/enum")
public class EnumController {


    @GetMapping("/linkModule")
    public AjaxResult linkModule() {
        Map<Integer, String> linkModuleMap = new HashMap<Integer, String>();//链接模块Map集合
        for (LinkModuleEnum linkModule : LinkModuleEnum.values()) {
            linkModuleMap.put(linkModule.getId(), linkModule.getDisplay());
        }
        return AjaxResult.success(linkModuleMap);

    }

}
