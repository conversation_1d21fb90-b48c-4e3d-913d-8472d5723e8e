package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.article.domain.BcImg;
import com.zainanjing.article.service.IGdmmImgService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 帖子评论图片Controller
 *
 * <AUTHOR>
 * @date 2023-11-26
 */
@RestController
@RequestMapping("/article/img")
public class BcImgController extends BaseController {
    @Autowired
    private IGdmmImgService gdmmImgService;

    /**
     * 查询帖子评论图片列表
     */
    @PreAuthorize("@ss.hasPermi('article:img:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcImg> page = gdmmImgService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取帖子评论图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:img:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gdmmImgService.getById(id));
    }

    /**
     * 新增帖子评论图片
     */
    @PreAuthorize("@ss.hasPermi('article:img:add')")
    @Log(title = "帖子评论图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcImg bcImg) {
        return toAjax(gdmmImgService.save(bcImg));
    }

    /**
     * 修改帖子评论图片
     */
    @PreAuthorize("@ss.hasPermi('article:img:edit')")
    @Log(title = "帖子评论图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcImg bcImg) {
        return toAjax(gdmmImgService.updateById(bcImg));
    }

    /**
     * 删除帖子评论图片
     */
    @PreAuthorize("@ss.hasPermi('article:img:remove')")
    @Log(title = "帖子评论图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(gdmmImgService.removeByIds(ids));
    }
}
