package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.article.domain.GdmmArticleCat;
import com.zainanjing.article.service.IGdmmArticleCatService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新闻分类Controller
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@RestController
@RequestMapping("/article/articleCat")
public class GdmmArticleCatController extends BaseController {
    @Autowired
    private IGdmmArticleCatService gdmmArticleCatService;

    /**
     * 查询新闻分类列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<GdmmArticleCat> page = gdmmArticleCatService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取新闻分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:articleCat:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gdmmArticleCatService.getById(id));
    }

    /**
     * 新增新闻分类
     */
    @PreAuthorize("@ss.hasPermi('article:articleCat:add')")
    @Log(title = "新闻分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmArticleCat gdmmArticleCat) {
        if (gdmmArticleCat.getIsDefault() == null) {
            gdmmArticleCat.setIsDefault(0);
        }
        return toAjax(gdmmArticleCatService.save(gdmmArticleCat));
    }

    /**
     * 修改新闻分类
     */
    @PreAuthorize("@ss.hasPermi('article:articleCat:edit')")
    @Log(title = "新闻分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmArticleCat gdmmArticleCat) {
        if (gdmmArticleCat.getIsDefault() == null) {
            gdmmArticleCat.setIsDefault(0);
        }
        return toAjax(gdmmArticleCatService.update(gdmmArticleCat));
    }

    /**
     * 删除新闻分类
     */
    @PreAuthorize("@ss.hasPermi('article:articleCat:remove')")
    @Log(title = "新闻分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(gdmmArticleCatService.deleteArticleCat(ids));
    }
}
