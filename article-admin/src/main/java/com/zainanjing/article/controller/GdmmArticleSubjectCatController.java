package com.zainanjing.article.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.Sort;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmArticleSubjectCat;
import com.zainanjing.article.service.IGdmmArticleSubjectCatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新闻专题分类Controller
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@RestController
@RequestMapping("/article/subjectCat")
public class GdmmArticleSubjectCatController extends BaseController {
    @Autowired
    private IGdmmArticleSubjectCatService gdmmArticleSubjectCatService;
    /**
     * 查询新闻专题分类列表
     */
    @PreAuthorize("@ss.hasPermi('article:subjectCat:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        searchable.addSearchParam("status_eq", Constants.STATUS_NORMAL);
        searchable.addSort(Sort.Direction.DESC, "id");
        IPage<GdmmArticleSubjectCat> page = gdmmArticleSubjectCatService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取新闻专题分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('article:subjectCat:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(gdmmArticleSubjectCatService.getById(id));
    }

    /**
     * 新增新闻专题分类
     */
    @PreAuthorize("@ss.hasPermi('article:subjectCat:add')")
    @Log(title = "新闻专题分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GdmmArticleSubjectCat gdmmArticleSubjectCat) {
        return toAjax(gdmmArticleSubjectCatService.save(gdmmArticleSubjectCat));
    }

    /**
     * 修改新闻专题分类
     */
    @PreAuthorize("@ss.hasPermi('article:subjectCat:edit')")
    @Log(title = "新闻专题分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmArticleSubjectCat gdmmArticleSubjectCat) {
        return toAjax(gdmmArticleSubjectCatService.updateById(gdmmArticleSubjectCat));
    }

    /**
     * 删除新闻专题分类
     */
    @PreAuthorize("@ss.hasPermi('article:subjectCat:remove')")
    @Log(title = "新闻专题分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        LambdaUpdateWrapper<GdmmArticleSubjectCat> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmArticleSubjectCat::getId, ids).set(GdmmArticleSubjectCat::getStatus, Constants.STATUS_DELETE);
        return toAjax(gdmmArticleSubjectCatService.update(updateWrapper));
    }
}
