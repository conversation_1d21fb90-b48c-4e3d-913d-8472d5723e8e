package com.zainanjing.article.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.sign.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class ScheduleUtil {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${system.schedule.url:}")
    private String scheduleUrl;

    @Value("${system.schedule.key:}")
    private String key;


    /**
     * 新闻定时发布
     */
    public void sendHTTPToScheduleForArticle(String type, Long id,
                                             Integer source) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("id", id.toString());
        param.put("source", source.toString());

        param.put("sign", SignUtil.md5("id=" + id + key));
        String url = scheduleUrl + "/schedule/article/buildGdmmArticle";
        logger.debug("保存定时新闻调用schedule开始：url={}", url);
        JsonNode jsonNode = HttpUtil.get(url, param);

        if (Objects.nonNull(jsonNode)) {
            //解析
            if (jsonNode == null || !"200".equals(jsonNode.get("error").asText())) {
                //创建schedule失败
                logger.error("保存定时新闻调用schedule返回错误，errorCode={},message={}", jsonNode.get("error"), jsonNode.get("msg"));
                throw new ServiceException("创建schedule失败,url=" + url);
            }
        } else {
            //创建schedule失败
            logger.error("保存定时新闻调用schedule返回为空");
            throw new ServiceException("创建schedule失败,url=" + url);
        }
    }


    /**
     * 短视频定时发布
     */
    public void sendHTTPToScheduleForVideo(String type, Long id,
                                           Integer source) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("id", id.toString());
        param.put("source", source.toString());
        param.put("sign", SignUtil.md5("id=" + id + key));
        String url = scheduleUrl + "/schedule/video/buildGdmmVideo";
        logger.debug("保存定时短视频调用schedule开始：url={}", url);
        JsonNode jsonObject = HttpUtil.get(url, param);

        if (Objects.nonNull(jsonObject)) {
            //解析
            if (jsonObject == null || !"200".equals(jsonObject.get("error").asText())) {
                //创建schedule失败
                logger.error("保存定时短视频调用schedule返回错误，errorCode={},message={}", jsonObject.get("error").asText(), jsonObject.get("msg").asText());
                throw new ServiceException("创建schedule失败,url=" + url);
            }
        } else {
            //创建schedule失败
            logger.error("保存定时短视频调用schedule返回为空");
            throw new ServiceException("创建schedule失败,url=" + url);
        }
    }
}
