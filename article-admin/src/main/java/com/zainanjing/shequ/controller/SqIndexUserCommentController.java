package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.shequ.domain.SqIndexUserComment;
import com.zainanjing.shequ.service.ISqIndexUserCommentService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区版主收藏评论关系Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/userComment")
public class SqIndexUserCommentController extends BaseController {
    @Autowired
    private ISqIndexUserCommentService sqIndexUserCommentService;

    /**
     * 查询社区版主收藏评论关系列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:userComment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqIndexUserComment> page = sqIndexUserCommentService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取社区版主收藏评论关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:userComment:query')")
    @GetMapping(value = "/{uid}")
    public AjaxResult getInfo(@PathVariable("uid") Integer uid) {
        return AjaxResult.success(sqIndexUserCommentService.getById(uid));
    }

    /**
     * 新增社区版主收藏评论关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:userComment:add')")
    @Log(title = "社区版主收藏评论关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqIndexUserComment sqIndexUserComment) {
        return toAjax(sqIndexUserCommentService.save(sqIndexUserComment));
    }

    /**
     * 修改社区版主收藏评论关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:userComment:edit')")
    @Log(title = "社区版主收藏评论关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqIndexUserComment sqIndexUserComment) {
        return toAjax(sqIndexUserCommentService.updateById(sqIndexUserComment));
    }

    /**
     * 删除社区版主收藏评论关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:userComment:remove')")
    @Log(title = "社区版主收藏评论关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{uids}")
    public AjaxResult remove(@PathVariable("uids") List<Integer> uids) {
        return toAjax(sqIndexUserCommentService.removeByIds(uids));
    }
}
