package com.zainanjing.shequ.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.search.SearchOperator;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.framework.web.service.PermissionService;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.util.CommonUtil;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.shequ.domain.*;
import com.zainanjing.shequ.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区板块数据Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/forum")
public class SqForumController extends BaseController {
    @Autowired
    private ISqForumService sqForumService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private ISqIndexForumUserService sqIndexForumUserService;

    @Resource
    private IGdmmUsersService gdmmUsersService;

    @Resource
    private IGdmmGoodsLabelService gdmmGoodsLabelService;

    @Resource
    private ISqIndexManagerUserService managerUserService;

    @Resource
    private ISqIndexUsersRoleService sqIndexUsersRoleService;


    /**
     * 查询社区板块数据列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:forum:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        //仅有社区板块权限
        Long uid = SecurityUtils.getUserId();
        if (!permissionService.hasRole("admin")) {

            SqIndexManagerUser managerUser = managerUserService.getById(uid);
            if (managerUser == null) {
                return new TableDataInfo();
            }

            //内部管理员
            if (!sqIndexUsersRoleService.lambdaQuery().eq(SqIndexUsersRole::getUid, managerUser.getUid()).eq(SqIndexUsersRole::getRoleId, 1).exists()) {
                List<SqIndexForumUser> list = sqIndexForumUserService.lambdaQuery().eq(SqIndexForumUser::getUid, managerUser.getUid()).list();
                if (CollUtil.isEmpty(list)) {
                    return new TableDataInfo();
                }
                searchable.addSearchFilter("id", SearchOperator.in, list.stream().map(SqIndexForumUser::getForumId).toArray());
            }
        }
        IPage<SqForum> page = sqForumService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取社区板块数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:forum:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        SqForum sqForum = sqForumService.getById(id);
        if (sqForum != null) {
            if (!StrUtil.isEmpty(sqForum.getLogo())) {
                sqForum.setLogo(OSSUtil.getImageURL(sqForum.getLogo()));
            }
            if (!StrUtil.isEmpty(sqForum.getImgUrl())) {
                sqForum.setImgUrl(OSSUtil.getImageURL(sqForum.getImgUrl()));
            }
            //从redis中读取
            sqForum.setReplyNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.REPLYNUM + "_" + sqForum.getId()));
            sqForum.setSubjectNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sqForum.getId()));
            sqForum.setTodayPostNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sqForum.getId()));

            if (sqForum.getIsLabelBrowse() != null && sqForum.getIsLabelBrowse() > 0) {
                //查询已经选择的浏览标签
                gdmmGoodsLabelService.lambdaQuery().eq(GdmmGoodsLabel::getGoodsId, id).eq(GdmmGoodsLabel::getType, Constants.LABEL_TYPE_SQFORUM).eq(GdmmGoodsLabel::getStatus, 1)
                        .select(GdmmGoodsLabel::getLabelId).list().forEach(gdmmGoodsLabel -> {
                            sqForum.getLabelBrowse().add(gdmmGoodsLabel.getLabelId());
                        });
            }
            if (sqForum.getIsLabelPost() != null && sqForum.getIsLabelPost() > 0) {
                //查询已经选择的发帖标签
                gdmmGoodsLabelService.lambdaQuery().eq(GdmmGoodsLabel::getGoodsId, id).eq(GdmmGoodsLabel::getType, Constants.LABEL_TYPE_SQFORUM_POST).eq(GdmmGoodsLabel::getStatus, 1)
                        .select(GdmmGoodsLabel::getLabelId).list().forEach(gdmmGoodsLabel -> {
                            sqForum.getLabelPost().add(gdmmGoodsLabel.getLabelId());
                        });
            }

            List<SqIndexForumUser> forumUsers = sqIndexForumUserService.lambdaQuery().eq(SqIndexForumUser::getForumId, id).list();
            if (CollUtil.isNotEmpty(forumUsers)) {
                List<Integer> queryUids = forumUsers.stream().map(SqIndexForumUser::getUid).toList();
                sqForum.setForumUids(String.join(",",queryUids.stream().map(String::valueOf).toList()));
                List<GdmmUsers> gdmmUsersList = gdmmUsersService.lambdaQuery().in(GdmmUsers::getUserId, queryUids).list();
                if (CollUtil.isNotEmpty(gdmmUsersList)) {
                    sqForum.setForumUserName(gdmmUsersList.stream().map(GdmmUsers::getMobilePhone).reduce((a, b) -> a + "," + b).get());
                }
            }
        }
        return AjaxResult.success(sqForum);
    }

    /**
     * 新增社区板块数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:forum:add')")
    @Log(title = "社区板块数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqForum sqForum) {
        checkUnique(sqForum.getName(), null);
        sqForum.setCreateTime(CommonUtil.getTimestamp());
        initData(sqForum);
        return toAjax(this.sqForumService.saveForum(sqForum));
    }


    /**
     * 修改社区板块数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:forum:edit')")
    @Log(title = "社区板块数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqForum sqForum) {
        checkUnique(sqForum.getName(), sqForum.getId());
        initData(sqForum);
        return toAjax(sqForumService.updateForum(sqForum));
    }

    /**
     * 删除社区板块数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:forum:remove')")
    @Log(title = "社区板块数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(sqForumService.lambdaUpdate().set(SqForum::getStatus, 1).set(SqForum::getIsShow, 0).in(SqForum::getId, ids).update());
    }

    /**
     * check 唯一性
     */
    private void checkUnique(String name, Integer id) {
        if (sqForumService.lambdaQuery()
                .eq(SqForum::getName, name)
                .ne(id != null, SqForum::getId, id)
                .exists()) {
            throw new ServiceException("板块名称已存在");
        }
    }


    private void initData(SqForum sqForum) {

        sqForum.setUpdateTime(CommonUtil.getTimestamp());
        sqForum.setSubjectNum(0L);
        sqForum.setReplyNum(0L);
        sqForum.setTodayPostNum(0L);
        sqForum.setIsLabelBrowse(0);
        sqForum.setIsLabelPost(0);
        if (sqForum.getUserLevelBrowse() == null) {
            sqForum.setUserLevelBrowse(0);
        }
        if (sqForum.getUserLevelPost() == null) {
            sqForum.setUserLevelPost(0);
        }
        if (sqForum.getSort() == null) {
            sqForum.setSort(0L);
        }
        if (!StrUtil.isEmpty(sqForum.getLogo())) {
            sqForum.setLogo(OSSUtil.getImageKey(sqForum.getLogo(), null));
        }
        if (!StrUtil.isEmpty(sqForum.getImgUrl())) {
            sqForum.setImgUrl(OSSUtil.getImageKey(sqForum.getImgUrl(), null));
        }
        if (sqForum.getIsShow() != null && sqForum.getIsShow() == 1) {
            sqForum.setStatus(0);
        } else {
            sqForum.setStatus(1);
        }

        if (StrUtil.isEmpty(sqForum.getUserName())) {
            sqForum.setUserName("");
        }
        if (StrUtil.isEmpty(sqForum.getDescription())) {
            sqForum.setDescription("");
        }
        //6.2.0需求社区板块中添加链接跳转的模块信息
        //选择无跳转链接,跳转信息都设置为默认值
        if (sqForum.getLinkType() == null || sqForum.getLinkType() == 0) {
            sqForum.setLinkType(0);
            sqForum.setLinkModule(0);
            sqForum.setResourceId(0);
            sqForum.setLinkTo(1);
            sqForum.setLinkUrl("");
        }
        //选择链接为内链
        else if (sqForum.getLinkType() == 1) {
            sqForum.setLinkModule(sqForum.getLinkModule() == null ? 0 : sqForum.getLinkModule());
            sqForum.setResourceId(sqForum.getResourceId() == null ? 0 : sqForum.getResourceId());
            sqForum.setLinkTo(sqForum.getLinkTo() == null ? 1 : sqForum.getLinkTo());
            sqForum.setLinkUrl("");
        }//选择链接为外链
        else if (sqForum.getLinkType() == 2) {
            sqForum.setLinkModule(0);
            sqForum.setResourceId(0);
            sqForum.setLinkTo(1);
            sqForum.setLinkUrl(sqForum.getLinkUrl() == null ? "" : sqForum.getLinkUrl());
        }
    }
}
