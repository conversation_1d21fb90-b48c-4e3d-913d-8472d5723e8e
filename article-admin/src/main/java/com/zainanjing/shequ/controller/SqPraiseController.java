package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.shequ.domain.SqPraise;
import com.zainanjing.shequ.service.ISqPraiseService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 点赞数据Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/praise")
public class SqPraiseController extends BaseController {
    @Autowired
    private ISqPraiseService sqPraiseService;

    /**
     * 查询点赞数据列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:praise:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqPraise> page = sqPraiseService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取点赞数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:praise:query')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable("postId") Integer postId) {
        return AjaxResult.success(sqPraiseService.getById(postId));
    }

    /**
     * 新增点赞数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:praise:add')")
    @Log(title = "点赞数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqPraise sqPraise) {
        return toAjax(sqPraiseService.save(sqPraise));
    }

    /**
     * 修改点赞数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:praise:edit')")
    @Log(title = "点赞数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqPraise sqPraise) {
        return toAjax(sqPraiseService.updateById(sqPraise));
    }

    /**
     * 删除点赞数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:praise:remove')")
    @Log(title = "点赞数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable("postIds") List<Integer> postIds) {
        return toAjax(sqPraiseService.removeByIds(postIds));
    }
}
