package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SimpleQuery;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.shequ.domain.SqIndexUsersRole;
import com.zainanjing.shequ.domain.SqRole;
import com.zainanjing.shequ.mapper.SqIndexUsersRoleMapper;
import com.zainanjing.shequ.service.ISqIndexUsersRoleService;
import com.zainanjing.shequ.service.ISqRoleService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户角色关联Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/userRole")
public class SqIndexUsersRoleController extends BaseController {
    @Autowired
    private ISqIndexUsersRoleService sqIndexUsersRoleService;


    /**
     * 查询用户角色关联列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:userRole:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqIndexUsersRole> page = sqIndexUsersRoleService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取用户角色关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:userRole:query')")
    @GetMapping(value = "/{uid}")
    public AjaxResult getInfo(@PathVariable("uid") Integer uid) {
        return AjaxResult.success(sqIndexUsersRoleService.getById(uid));
    }

    /**
     * 新增用户角色关联
     */
    @PreAuthorize("@ss.hasPermi('shequ:userRole:add')")
    @Log(title = "用户角色关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqIndexUsersRole sqIndexUsersRole) {
        if (sqIndexUsersRoleService.lambdaQuery().eq(SqIndexUsersRole::getUid, sqIndexUsersRole.getUid()).eq(SqIndexUsersRole::getRoleId, sqIndexUsersRole.getRoleId()).exists()) {
            return error("用户角色已存在");
        } else {
            return toAjax(sqIndexUsersRoleService.save(sqIndexUsersRole));
        }
    }

    /**
     * 删除用户角色关联
     */
    @PreAuthorize("@ss.hasPermi('shequ:userRole:remove')")
    @Log(title = "用户角色关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{uids}")
    public AjaxResult remove(@PathVariable("uids") List<Integer> uids) {
        List<Integer> roleIds = SimpleQuery.list(
                new LambdaQueryWrapper<SqRole>().eq(SqRole::getIsSystem, 1).select(SqRole::getId).select(SqRole::getId), SqRole::getId);
        return toAjax(sqIndexUsersRoleService.lambdaUpdate().in(SqIndexUsersRole::getUid, uids).notIn(SqIndexUsersRole::getRoleId, roleIds).remove());
    }
}
