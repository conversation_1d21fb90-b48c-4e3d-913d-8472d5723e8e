package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.shequ.domain.SqIndexForumUser;
import com.zainanjing.shequ.service.ISqIndexForumUserService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 板块用户关联数据Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/forumUser")
public class SqIndexForumUserController extends BaseController {
    @Autowired
    private ISqIndexForumUserService sqIndexForumUserService;

    /**
     * 查询板块用户关联数据列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:forumUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqIndexForumUser> page = sqIndexForumUserService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取板块用户关联数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:forumUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(sqIndexForumUserService.getById(id));
    }

    /**
     * 新增板块用户关联数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:forumUser:add')")
    @Log(title = "板块用户关联数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqIndexForumUser sqIndexForumUser) {
        return toAjax(sqIndexForumUserService.save(sqIndexForumUser));
    }

    /**
     * 修改板块用户关联数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:forumUser:edit')")
    @Log(title = "板块用户关联数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqIndexForumUser sqIndexForumUser) {
        return toAjax(sqIndexForumUserService.updateById(sqIndexForumUser));
    }

    /**
     * 删除板块用户关联数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:forumUser:remove')")
    @Log(title = "板块用户关联数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(sqIndexForumUserService.removeByIds(ids));
    }
}
