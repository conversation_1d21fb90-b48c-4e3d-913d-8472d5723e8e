package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.shequ.domain.SqIndexUserPost;
import com.zainanjing.shequ.service.ISqIndexUserPostService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区版主收藏帖子关系Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/userPost")
public class SqIndexUserPostController extends BaseController {
    @Autowired
    private ISqIndexUserPostService sqIndexUserPostService;

    /**
     * 查询社区版主收藏帖子关系列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:userPost:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqIndexUserPost> page = sqIndexUserPostService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取社区版主收藏帖子关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:userPost:query')")
    @GetMapping(value = "/{uid}")
    public AjaxResult getInfo(@PathVariable("uid") Integer uid) {
        return AjaxResult.success(sqIndexUserPostService.getById(uid));
    }

    /**
     * 新增社区版主收藏帖子关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:userPost:add')")
    @Log(title = "社区版主收藏帖子关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqIndexUserPost sqIndexUserPost) {
        return toAjax(sqIndexUserPostService.save(sqIndexUserPost));
    }

    /**
     * 修改社区版主收藏帖子关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:userPost:edit')")
    @Log(title = "社区版主收藏帖子关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqIndexUserPost sqIndexUserPost) {
        return toAjax(sqIndexUserPostService.updateById(sqIndexUserPost));
    }

    /**
     * 删除社区版主收藏帖子关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:userPost:remove')")
    @Log(title = "社区版主收藏帖子关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{uids}")
    public AjaxResult remove(@PathVariable("uids") List<Integer> uids) {
        return toAjax(sqIndexUserPostService.removeByIds(uids));
    }
}
