package com.zainanjing.shequ.controller;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.search.SearchOperator;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.util.CommonUtil;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.shequ.domain.SqIndexManagerUser;
import com.zainanjing.shequ.domain.SqMessage;
import com.zainanjing.shequ.mapper.GdmmUsersMapper;
import com.zainanjing.shequ.service.ISqIndexManagerUserService;
import com.zainanjing.shequ.service.ISqMessageService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 私信Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/message")
public class SqMessageController extends BaseController {
    @Autowired
    private ISqMessageService sqMessageService;

    @Resource
    private GdmmUsersMapper gdmmUsersMapper;

    @Resource
    private ISqIndexManagerUserService managerUserService;

    /**
     * 查询私信列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:message:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        Long uid = SecurityUtils.getUserId();
        SqIndexManagerUser managerUser = managerUserService.getById(uid);
        if (managerUser != null) {
            uid = managerUser.getUid();
        }

        searchable.addSearchFilter("uid", SearchOperator.eq, uid);
        IPage<SqMessage> page = sqMessageService.messagePage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    @PreAuthorize("@ss.hasPermi('shequ:message:query')")
    @GetMapping(value = "/detail")
    public TableDataInfo detail(@RequestParam("friendUid") Long friendUid,
                                @RequestParam("page") Integer page,
                                @RequestParam("size") Integer size) {
        Integer uid = SecurityUtils.getUserId().intValue();
        SqIndexManagerUser managerUser = managerUserService.getById(uid);
        if (managerUser != null) {
            uid = managerUser.getUid().intValue();
        }

        Page pagination = new Page(page, size);
        Integer finalUid = uid;
        Page<SqMessage> pageRest = sqMessageService.lambdaQuery()
                .and(i -> i.eq(SqMessage::getStatus, 0).or(j -> j.ne(SqMessage::getStatus, finalUid).ne(SqMessage::getStatus, 1)))
                .and(i -> i.and(j -> j.eq(SqMessage::getReceiver, finalUid).eq(SqMessage::getSender, friendUid))
                        .or(j -> j.eq(SqMessage::getSender, finalUid).eq(SqMessage::getReceiver, friendUid)))
                .orderByDesc(SqMessage::getId).page(pagination);

        if (CollUtil.isNotEmpty(pageRest.getRecords())) {
//            查到当前信息置为已读
            sqMessageService.lambdaUpdate().in(SqMessage::getId, pageRest.getRecords().stream().map(SqMessage::getId).toList())
                    .set(SqMessage::getReceiverRead, Constants.SHI).update();
            pageRest.getRecords().forEach(x -> {
                if (Constants.SQ_MESSAGE_TYPE_IMAGE.equals(String.valueOf(x.getType()))) {
                    x.setContent(OSSUtil.getImageURL(x.getContent()));
                }
            });
        }
        return new TableDataInfo(pageRest.getRecords(), pageRest.getTotal());

    }


    /**
     * 获取私信详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sqMessageService.getById(id));
    }

    /**
     * 新增私信
     */
    @PreAuthorize("@ss.hasPermi('shequ:message:add')")
    @Log(title = "私信", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqMessage sqMessage) {
        Long uid = SecurityUtils.getUserId();
        SqIndexManagerUser managerUser = managerUserService.getById(uid);
        if (managerUser != null) {
            uid = managerUser.getUid();
        }

        String towUidString = makeTwoUidString(uid,sqMessage.getReceiver());
        sqMessage.setType(sqMessage.getType());
        sqMessage.setSender(uid);
        sqMessage.setTwoUid(towUidString);
        sqMessage.setSenderDelete(Constants.FOU);
        sqMessage.setSenderRead(Constants.SHI);
        sqMessage.setReceiverDelete(Constants.FOU);
        sqMessage.setReceiverRead(Constants.FOU);
        Integer now = CommonUtil.getTimestamp();
        sqMessage.setCreateTime(now);
        sqMessage.setUpdateTime(now);
        sqMessage.setStatus(Integer.valueOf(Constants.STATUS_NORMAL));
        return toAjax(sqMessageService.save(sqMessage));
    }

    private String makeTwoUidString(Long uid, Long friendUidLong) {
        StringBuffer sb = new StringBuffer();
        //这里不考虑2个人的uid相等的情况
        if(uid.compareTo(friendUidLong) == -1){
            sb.append(uid);
            sb.append("_");
            sb.append(friendUidLong);
        }else if(uid.compareTo(friendUidLong) == 1){
            sb.append(friendUidLong);
            sb.append("_");
            sb.append(uid);
        }
        return sb.toString();
    }

    /**
     * 修改私信
     */
    @PreAuthorize("@ss.hasPermi('shequ:message:edit')")
    @Log(title = "私信", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqMessage sqMessage) {
        return toAjax(sqMessageService.updateById(sqMessage));
    }

    /**
     * 删除私信
     */
    @PreAuthorize("@ss.hasPermi('shequ:message:remove')")
    @Log(title = "私信", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(sqMessageService.removeByIds(ids));
    }
}
