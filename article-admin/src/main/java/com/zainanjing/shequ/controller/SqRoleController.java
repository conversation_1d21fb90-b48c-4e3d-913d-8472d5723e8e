package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.shequ.domain.SqIndexUsersRole;
import com.zainanjing.shequ.domain.SqRole;
import com.zainanjing.shequ.service.ISqIndexUsersRoleService;
import com.zainanjing.shequ.service.ISqRoleService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色Controller
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@RestController
@RequestMapping("/shequ/role")
public class SqRoleController extends BaseController {
    @Autowired
    private ISqRoleService sqRoleService;

    @Resource
    private ISqIndexUsersRoleService sqIndexUsersRoleService;

    /**
     * 查询角色列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:role:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqRole> page = sqRoleService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }


    /**
     * 获取角色详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:role:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(sqRoleService.getById(id));
    }

    /**
     * 新增角色
     */
    @PreAuthorize("@ss.hasPermi('shequ:role:add')")
    @Log(title = "角色", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqRole sqRole) {
        sqRole.setCreateTime(CommonUtil.getTimestamp());
        sqRole.setUpdateTime(CommonUtil.getTimestamp());
        if (sqRole.getDescription() == null) {
            sqRole.setDescription("");
        }
        return toAjax(sqRoleService.save(sqRole));
    }

    /**
     * 修改角色
     */
    @PreAuthorize("@ss.hasPermi('shequ:role:edit')")
    @Log(title = "角色", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqRole sqRole) {
        sqRole.setUpdateTime(CommonUtil.getTimestamp());
        return toAjax(sqRoleService.updateById(sqRole));
    }

    /**
     * 删除角色
     */
    @PreAuthorize("@ss.hasPermi('shequ:role:remove')")
    @Log(title = "角色", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        if (sqIndexUsersRoleService.lambdaQuery().in(SqIndexUsersRole::getRoleId, ids).exists()) {
            return AjaxResult.error("所选角色已分配给用户,删除失败");
        }
        return toAjax(sqRoleService.removeByIds(ids));
    }
}
