package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.shequ.domain.SqIndexManagerUser;
import com.zainanjing.shequ.service.ISqIndexManagerUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理用户和会员对应关系Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/shequ/managerUser")
public class SqIndexManagerUserController extends BaseController {
    @Autowired
    private ISqIndexManagerUserService sqIndexManagerUserService;

    /**
     * 查询管理用户和会员对应关系列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:managerUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqIndexManagerUser> page = sqIndexManagerUserService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取管理用户和会员对应关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:managerUser:query')")
    @GetMapping(value = "/{managerId}")
    public AjaxResult getInfo(@PathVariable("managerId") Long managerId) {
        return AjaxResult.success(sqIndexManagerUserService.getById(managerId));
    }

    /**
     * 修改管理用户和会员对应关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:managerUser:edit')")
    @Log(title = "管理用户和会员对应关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqIndexManagerUser sqIndexManagerUser) {
        if (sqIndexManagerUserService.getById(sqIndexManagerUser.getManagerId()) == null) {
            return toAjax(sqIndexManagerUserService.save(sqIndexManagerUser));
        } else {
            return toAjax(sqIndexManagerUserService.updateById(sqIndexManagerUser));
        }
    }

    /**
     * 删除管理用户和会员对应关系
     */
    @PreAuthorize("@ss.hasPermi('shequ:managerUser:remove')")
    @Log(title = "管理用户和会员对应关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{managerIds}")
    public AjaxResult remove(@PathVariable("managerIds") List<Long> managerIds) {
        return toAjax(sqIndexManagerUserService.removeByIds(managerIds));
    }
}
