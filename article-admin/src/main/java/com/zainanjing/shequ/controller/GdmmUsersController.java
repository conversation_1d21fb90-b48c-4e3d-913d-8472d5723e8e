package com.zainanjing.shequ.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.shequ.domain.GdmmUsers;
import com.zainanjing.shequ.domain.SqComment;
import com.zainanjing.shequ.domain.SqIndexUsersRole;
import com.zainanjing.shequ.domain.SqPost;
import com.zainanjing.shequ.mapper.GdmmUsersMapper;
import com.zainanjing.shequ.mapper.SqIndexUsersRoleMapper;
import com.zainanjing.shequ.service.IGdmmUsersService;
import com.zainanjing.shequ.service.ISqCommentService;
import com.zainanjing.shequ.service.ISqIndexUsersRoleService;
import com.zainanjing.shequ.service.ISqPostService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@RestController
@RequestMapping("/shequ/gdmmUsers")
public class GdmmUsersController extends BaseController {
    @Autowired
    private IGdmmUsersService gdmmUsersService;

    @Resource
    private ISqPostService iSqPostService;

    @Resource
    private ISqCommentService sqCommentService;

    @Resource
    private ISqIndexUsersRoleService sqIndexUsersRoleService;

    @Resource
    private GdmmUsersMapper gdmmUsersMapper;

    @Resource
    private SqIndexUsersRoleMapper usersRoleMapper;

    /**
     * 查询用户信息列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:gdmmUsers:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<GdmmUsers> page = gdmmUsersService.findByPage(searchable);
        if (page.getRecords().size() > 0) {

            Map<Integer, String> userRoleMaps = new HashMap<>();
            Map<Integer, Integer> userRoleIdMaps = new HashMap<>();

            List<SqIndexUsersRole> sqIndexUsersRoles = usersRoleMapper.selectUserRoleByUids(page.getRecords().stream().map(GdmmUsers::getUserId).toList());
            if (CollUtil.isNotEmpty(sqIndexUsersRoles)) {
                //根据用户id分组，使用逗号拼接角色名称
                userRoleMaps = sqIndexUsersRoles.stream()
                        .collect(Collectors.groupingBy(SqIndexUsersRole::getUid, Collectors.mapping(SqIndexUsersRole::getRoleName, Collectors.joining(","))));

                for (SqIndexUsersRole sqIndexUsersRole : sqIndexUsersRoles) {
                    userRoleIdMaps.put(sqIndexUsersRole.getUid(), sqIndexUsersRole.getRoleId());
                }
            }


            for (GdmmUsers gdmmUsers : page.getRecords()) {
                gdmmUsers.setRoleId(userRoleIdMaps.get(gdmmUsers.getUserId()));
                gdmmUsers.setRoleName(StrUtil.blankToDefault(userRoleMaps.get(gdmmUsers.getUserId()), "普通用户"));
                gdmmUsers.setPostNum(iSqPostService.lambdaQuery().eq(SqPost::getUid, gdmmUsers.getUserId()).count());
                gdmmUsers.setCommentNum(sqCommentService.lambdaQuery().eq(SqComment::getUid, gdmmUsers.getUserId()).count());
            }
        }

        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取用户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:gdmmUsers:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId) {
        GdmmUsers gdmmUsers = gdmmUsersMapper.selectUser(userId);
        if (StrUtil.isNotBlank(gdmmUsers.getImageUrl())) {
            gdmmUsers.setImageUrl(OSSUtil.getImageURL(gdmmUsers.getImageUrl()));
        }
        return AjaxResult.success(gdmmUsers);
    }

    /**
     * 修改用户信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:gdmmUsers:edit')")
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GdmmUsers gdmmUsers) {
        return toAjax(gdmmUsersService.updateUser(gdmmUsers));
    }


}
