package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.shequ.domain.SqArea;
import com.zainanjing.shequ.service.ISqAreaService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区分区Controller
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@RestController
@RequestMapping("/shequ/area")
public class SqAreaController extends BaseController {
    @Autowired
    private ISqAreaService sqAreaService;

    /**
     * 查询社区分区列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:area:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqArea> page = sqAreaService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取社区分区详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:area:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(sqAreaService.getById(id));
    }

    /**
     * 新增社区分区
     */
    @PreAuthorize("@ss.hasPermi('shequ:area:add')")
    @Log(title = "社区分区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqArea sqArea) {
        if (sqAreaService.lambdaQuery().eq(SqArea::getName, sqArea.getName()).exists()) {
            return AjaxResult.error("分区名称已存在");
        }

        sqArea.setCreateTime(CommonUtil.getLongTimestamp());
        sqArea.setUpdateTime(CommonUtil.getLongTimestamp());
        if (sqArea.getIsShow() != null && sqArea.getIsShow() == 1) {
            sqArea.setStatus(0);
        } else {
            sqArea.setStatus(1);
        }
        return toAjax(sqAreaService.save(sqArea));
    }

    /**
     * 修改社区分区
     */
    @PreAuthorize("@ss.hasPermi('shequ:area:edit')")
    @Log(title = "社区分区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqArea sqArea) {
        if (sqAreaService.lambdaQuery().eq(SqArea::getName, sqArea.getName()).ne(SqArea::getId, sqArea.getId()).exists()) {
            return AjaxResult.error("分区名称已存在");
        }

        sqArea.setUpdateTime(CommonUtil.getLongTimestamp());
        if (sqArea.getIsShow() != null && sqArea.getIsShow() == 1) {
            sqArea.setStatus(0);
        } else {
            sqArea.setStatus(1);
        }
        return toAjax(sqAreaService.updateById(sqArea));
    }

    /**
     * 删除社区分区
     */
    @PreAuthorize("@ss.hasPermi('shequ:area:remove')")
    @Log(title = "社区分区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(sqAreaService.lambdaUpdate().in(SqArea::getId, ids).set(SqArea::getStatus, 1).set(SqArea::getIsShow, 0).update());
    }
}
