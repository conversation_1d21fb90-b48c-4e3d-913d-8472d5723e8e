package com.zainanjing.shequ.controller;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.SearchOperator;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.PermissionService;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.article.domain.BcImg;
import com.zainanjing.article.domain.GdmmParams;
import com.zainanjing.article.service.IGdmmImgService;
import com.zainanjing.article.service.IGdmmParamsService;
import com.zainanjing.common.util.CommonUtil;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.shequ.domain.*;
import com.zainanjing.shequ.dto.SqPostDTO;
import com.zainanjing.shequ.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 帖子数据Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/post")
public class SqPostController extends BaseController {

    @Autowired
    private ISqPostService sqPostService;

    @Resource
    private IGdmmParamsService gdmmParamsService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private ISqIndexForumUserService sqIndexForumUserService;

    @Resource
    private ISqIndexUserPostService sqIndexUserPostService;

    @Autowired
    private IGdmmImgService gdmmImgService;

    @Resource
    private ISqCommentService sqCommentService;

    @Resource
    private IGdmmUsersService gdmmUsersService;

    @Resource
    private ISqForumService sqForumService;

    @Resource
    private ISqIndexManagerUserService managerUserService;

    @Resource
    private ISqIndexUsersRoleService sqIndexUsersRoleService;

    /**
     * 查询帖子数据列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<SqPostDTO> page = search(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    private IPage<SqPostDTO> search(Searchable searchable) {
        IPage<SqPostDTO> page = new Page<>();
        Long userId = SecurityUtils.getUserId();
        searchable.addSearchFilter("uidForStar", SearchOperator.eq, userId);
        //仅有社区板块权限
//        if (permissionService.lacksPermi("shequ:forum:list") && permissionService.hasAnyPermi("shequ:forum:myList")) {
        if (!permissionService.hasRole("admin")) {
            Long uid = SecurityUtils.getUserId();
            SqIndexManagerUser managerUser = managerUserService.getById(uid);
            if (managerUser != null) {
                uid = managerUser.getUid();
            }
            if (!sqIndexUsersRoleService.lambdaQuery().eq(SqIndexUsersRole::getUid, uid).eq(SqIndexUsersRole::getRoleId, 1).exists()) {
                List<SqIndexForumUser> list = sqIndexForumUserService.lambdaQuery().eq(SqIndexForumUser::getUid, uid).list();
                if (CollUtil.isEmpty(list)) {
                    return page;
                }
                searchable.addSearchParam("forumIds", list.stream().map(SqIndexForumUser::getForumId).toList());
            }
        }
        page = sqPostService.list(searchable);

        //从redis从获取点击率
        if (CollUtil.isNotEmpty(page.getRecords())) {
            for (SqPost sqPost : page.getRecords()) {
                sqPost.setCreateTimeStr(CommonUtil.getDate2(sqPost.getCreateTime().longValue()));
                sqPost.setClickNum(CommonUtil.redisCommonFind(Constants.SQPOST + "_" + Constants.CLICKNUM + "_" + sqPost.getId()).longValue());
                sqPost.setPraiseNum(CommonUtil.redisCommonFind(Constants.SQPOST + "_" + Constants.PRAISENUM + "_" + sqPost.getId()).longValue());
                sqPost.setCommentNum(CommonUtil.redisCommonFind(Constants.SQPOST + "_" + Constants.COMMENTNUM + "_" + sqPost.getId()).longValue());
            }
        }
        return page;
    }


    /**
     * 收藏帖子
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:list')")
    @PostMapping(value = "/star/{id}")
    public AjaxResult star(@PathVariable("id") Long postId) {
        Long uid = SecurityUtils.getUserId();
        SqIndexUserPost sqIndexUserPost = new SqIndexUserPost();
        sqIndexUserPost.setUid(Long.valueOf(uid).intValue());
        sqIndexUserPost.setPostId(postId);
        return toAjax(sqIndexUserPostService.save(sqIndexUserPost));
    }

    /**
     * 取消收藏帖子
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:list')")
    @PostMapping(value = "/unStar/{id}")
    public AjaxResult unStar(@PathVariable("id") Long postId) {
        Long uid = SecurityUtils.getUserId();
        return toAjax(sqIndexUserPostService.lambdaUpdate().eq(SqIndexUserPost::getUid, Long.valueOf(uid).intValue()).eq(SqIndexUserPost::getPostId, postId).remove());
    }

    /**
     * 获取帖子数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        SqPost sqPost = sqPostService.getById(id);
        if (sqPost.getSort() > 1000000) {
            sqPost.setSort(sqPost.getSort() - 1000000);
        } else {
            sqPost.setSort(0L);
        }
        if (StrUtil.isNotEmpty(sqPost.getVideoImg())) {
            sqPost.setVideoImg(OSSUtil.getImageURL(sqPost.getVideoImg()));
        }
        List<BcImg> sqPostImgList = searchPostImgs(id.toString());
        sqPost.setBcImgList(sqPostImgList);
        sqForumService.lambdaQuery().eq(SqForum::getId, sqPost.getForumId()).oneOpt().ifPresent(sqForum -> {
            sqPost.setForumName(sqForum.getName());
        });
        if (sqPost.getUid() != null) {
            gdmmUsersService.lambdaQuery().eq(GdmmUsers::getUserId, sqPost.getUid()).oneOpt().ifPresent(gdmmUsers -> {
                sqPost.setUserName(gdmmUsers.getUserName());
            });
        }
        return AjaxResult.success(sqPost);
    }

    /**
     * 新增帖子数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:add')")
    @Log(title = "帖子数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqPost sqPost) {
        Long uid = SecurityUtils.getUserId();
        SqIndexManagerUser managerUser = managerUserService.getById(uid);
        if (managerUser != null) {
            uid = managerUser.getUid();
        }

        sqPost.setUid(uid.intValue());
        //end

        sqPost.setClickNum(0L);
        sqPost.setCommentNum(0L);
        sqPost.setPraiseNum(0L);
        sqPost.setType(1);
        sqPost.setCreateTime(CommonUtil.getTimestamp());
        sqPost.setIsHot(0);
        sqPost.setIsShowIndex(0);
        sqPost.setStatus(0);

        if (sqPost.getSort() == null) sqPost.setSort(0L);
        if (sqPost.getIsRec() == null) sqPost.setIsRec(0);
        if (sqPost.getIsAnonymous() == null) sqPost.setIsAnonymous(0);

        sqPost.setUpdateTime(CommonUtil.getTimestamp());
        if (sqPost.getSort() > 0) sqPost.setSort(sqPost.getSort() + 1000000);
        if (StrUtil.isNotEmpty(sqPost.getHiddenContent())) {
            sqPost.setHaveHidden(1);
        } else {
            sqPost.setHaveHidden(0);
        }

        if (StrUtil.isEmpty(sqPost.getVideoImg())) {
            sqPost.setVideoImg("");
        } else {
            sqPost.setVideoImg(OSSUtil.getImageKey(sqPost.getVideoImg(), null));
        }
        if (StrUtil.isEmpty(sqPost.getVideoUrl())) sqPost.setVideoUrl("");
        //后台保存帖子，商品id默认为0
        if (sqPost.getGoodsId() == null) sqPost.setGoodsId(0);
        //后台发帖，先设个空默认值 防止报错
        sqPost.setIp("");
        sqPost.setRegion("");
        if(sqPost.getContent() == null) sqPost.setContent("");
        sqPostService.savePost(sqPost);
        return success();
    }

    /**
     * 修改帖子数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:edit')")
    @Log(title = "帖子数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqPost sqPost) {
        sqPost.setUpdateTime(CommonUtil.getTimestamp());
        if (sqPost.getSort() > 0) {
            sqPost.setSort(sqPost.getSort() + 1000000);
        } else {
            sqPost.setSort(sqPost.getId());
        }
        if (StrUtil.isNotEmpty(sqPost.getHiddenContent())) {
            sqPost.setHaveHidden(1);
        } else {
            sqPost.setHaveHidden(0);
        }
        if (StrUtil.isEmpty(sqPost.getVideoImg())) {
            sqPost.setVideoImg("");
        } else {
            sqPost.setVideoImg(OSSUtil.getImageKey(sqPost.getVideoImg(), null));
        }
        if (StrUtil.isEmpty(sqPost.getVideoUrl())) sqPost.setVideoUrl("");
        //后台保存帖子，商品id默认为0
        if (sqPost.getGoodsId() == null) sqPost.setGoodsId(0);
        sqPostService.updatePost(sqPost);
        return success();
    }

    /**
     * 修改帖子数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:status')")
    @Log(title = "帖子数据", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/editStatus")
    public AjaxResult editStatus(@RequestBody SqPost sqPost) {
        if (sqPost.getId() == null) {
            return AjaxResult.error("数据不存在");
        }
        sqPost.setUpdateTime(CommonUtil.getTimestamp());
        sqPostService.lambdaUpdate().eq(SqPost::getId, sqPost.getId())
                .set(ObjUtil.isNotNull(sqPost.getIsRec()), SqPost::getIsRec, sqPost.getIsRec())
                .set(ObjUtil.isNotNull(sqPost.getIsHot()), SqPost::getIsHot, sqPost.getIsHot())
                .set(ObjUtil.isNotNull(sqPost.getIsShowIndex()), SqPost::getIsShowIndex, sqPost.getIsShowIndex())
                .update();
        return success();
    }

    /**
     * 删除帖子数据
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:remove')")
    @Log(title = "帖子数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        if (sqPostService.lambdaUpdate().in(SqPost::getId, ids).set(SqPost::getStatus, Constants.STATUS_DELETE_BY_HOST).update()) {
            for (Long id : ids) {
                //TODO 删除mangodb 暂时不动
                //删除帖子时 修改mongodb sqmessage 社区相关的   status改为2
                Map map = new HashMap();
                map.put("type", Constants.SQMESSAGE_TYPE_SQ);
                map.put("code", Arrays.asList(Constants.SQMESSAGE_CODE_LIST_POST));
                map.put("sqType", Constants.SQMESSAGE_SQTYPE_SQPOST);
                map.put("resourceId", id);
                Map setMap = new HashMap();
                setMap.put("status", Constants.TWO);//status=2  表示消息里的帖子被删
//                long temp = MongoDBUtil.batchUpdate(map, setMap, SqMessage.class);
//                logger.info("删除社区帖子id=" + id + ",修改mongodb status=2数量=" + temp);

                SqPost sqPost = sqPostService.getById(id);
                if (null != sqPost) {
                    CommonUtil.redisCommonMinus(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sqPost.getForumId());
                    //判断今日发帖总数和回复数大于0
                    Long todayPostNum = CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sqPost.getForumId());
                    if (todayPostNum != null && todayPostNum > 0) {
                        CommonUtil.redisCommonMinus(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sqPost.getForumId());
                    }
                }
                //社区帖子删除，帖子下面的评论也要删除
                sqCommentService.lambdaUpdate().eq(SqComment::getPostId, id).set(SqComment::getStatus, Constants.STATUS_DELETE).update();
            }
        }
        return toAjax(sqPostService.removeByIds(ids));
    }


    /**
     * 跳转到帖子设置页面
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:setting')")
    @GetMapping(value = "/setting")
    public AjaxResult setting() {
        GdmmParams ecsParam = new GdmmParams();
        try {
            //查询帮助表获取帖子设置参数
            List<GdmmParams> ecsParamList = gdmmParamsService.lambdaQuery().eq(GdmmParams::getModule, "SQ").eq(GdmmParams::getCode, "PARAM").list();
            if (ecsParamList != null && ecsParamList.size() > 0) {
                ecsParam = ecsParamList.get(0);
            }
            String value = ecsParam.getValue();
            if (value != null && !"".equals(value)) {
                JSONObject json = JSONObject.parseObject(value);
                return AjaxResult.success(json);
            }
        } catch (Exception e) {
            logger.error("获取帖子设置失败", e);
        }
        return AjaxResult.error("获取帖子设置失败");
    }

    /**
     * 保存帖子设置
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('shequ:post:setting')")
    @PostMapping(value = "/saveSetting")
    public AjaxResult saveSetting(@RequestBody JSONObject jsonObject) {
        //获取帖子设置
        String isCloseSqPost = jsonObject.getString("isCloseSqPost");
        String isCloseSqComment = jsonObject.getString("isCloseSqComment");
        String isAuditSqPost = jsonObject.getString("isAuditSqPost");
        String isAuditSqComment = jsonObject.getString("isAuditSqComment");
        try {
            //帖子设置转json保存到param表
            List<GdmmParams> ecsParamList = gdmmParamsService.lambdaQuery().eq(GdmmParams::getModule, "SQ").eq(GdmmParams::getCode, "PARAM").list();
            if (ecsParamList != null && ecsParamList.size() > 0) {
                GdmmParams ecsParam = ecsParamList.get(0);
                ecsParam.setValue(jsonObject.toJSONString());
                ecsParam.setUpdateTime(CommonUtil.getTimestamp());
                gdmmParamsService.updateById(ecsParam);
            }
            //帖子设置保存到 redis
            CommonUtil.redisCommonPut(RedisKey.SQ_POST_IS_CLOSE.getKey(), isCloseSqPost);
            CommonUtil.redisCommonPut(RedisKey.SQ_COMMENT_IS_CLOSE.getKey(), isCloseSqComment);
            CommonUtil.redisCommonPut(RedisKey.SQ_POST_IS_AUDIT.getKey(), isAuditSqPost);
            CommonUtil.redisCommonPut(RedisKey.SQ_COMMENT_IS_AUDIT.getKey(), isAuditSqComment);
        } catch (Exception e) {
            logger.error("保存帖子设置失败", e);
        }
        return AjaxResult.success();
    }


    @Log(title = "发帖审核", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('shequ:post:audit')")
    @PostMapping(value = "/audit/{ids}")
    public AjaxResult auditBatch(@PathVariable("ids") List<Long> ids, @RequestBody SqPost sqPost) {
        if (sqPostService.lambdaUpdate().set(SqPost::getStatus, sqPost.getStatus()).set(SqPost::getRejectReason, sqPost.getRejectReason()).set(SqPost::getAuditTime, CommonUtil.getTimestamp())
                .in(SqPost::getId, ids).update()) {
            //审核通过添加回帖数量
            if (Constants.ZERO.equals(sqPost.getStatus())) {
                addSqPostNum(ids);
            }
            return success();
        }
        return error();
    }

    /**
     * 查询帖子图片
     *
     * @param postId
     * @return
     */
    private List<BcImg> searchPostImgs(String postId) {
        List<BcImg> sqPostImgList = gdmmImgService.lambdaQuery()
                .eq(BcImg::getPostId, postId)
                .eq(BcImg::getType, Constants.IMAGE_TYPE_SQPOST)
                .eq(BcImg::getStatus, Constants.STATUS_NORMAL).list();
        if (CollUtil.isNotEmpty(sqPostImgList)) {
            for (BcImg bcImg : sqPostImgList) {
                bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
            }
        }
        return sqPostImgList;
    }

    private void addSqPostNum(List<Long> ids) {
        for (Long id : ids) {
            logger.info("审核通过，添加帖子数量开始");
            SqPost sqPost = sqPostService.getById(id);
            CommonUtil.redisCommonAdd(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sqPost.getForumId());
            CommonUtil.redisCommonAdd(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sqPost.getForumId());
            logger.info("审核通过，添加帖子数量结束：" + sqPost.getForumId());
        }
    }
}
