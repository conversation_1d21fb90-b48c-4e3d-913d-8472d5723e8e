<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zainanjing</groupId>
        <artifactId>news-parent</artifactId>
        <version>1.2.0</version>
    </parent>
    <groupId>com.zainanjing</groupId>
    <artifactId>article-admin</artifactId>
    <name>article-admin</name>
    <description>资讯社区-后台</description>

    <dependencies>
        <dependency>
            <groupId>com.ruoyi.zainanjing365</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zainanjing</groupId>
            <artifactId>article</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <!-- mangodb -->
<!--        <dependency>-->
<!--            <groupId>org.mongodb</groupId>-->
<!--            <artifactId>mongodb-driver-sync</artifactId>-->
<!--            <version>3.12.14</version>-->
<!--        </dependency>-->
    </dependencies>

</project>
