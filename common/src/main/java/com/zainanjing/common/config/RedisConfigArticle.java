package com.zainanjing.common.config;

import com.zainanjing.common.util.RedisUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * redis配置
 *
 * <AUTHOR>
 */
@Configuration
public class RedisConfigArticle {
    @Lazy(value = false)
    @Bean("jedisTemplate")
    public RedisTemplate<Object, Object> jedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericToStringSerializer<String>(String.class));

        template.afterPropertiesSet();
        RedisUtil.setJedisTemplate(template);
        return template;
    }

    
}
