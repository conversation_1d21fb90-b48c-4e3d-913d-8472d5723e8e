package com.zainanjing.common.constant;

public class SensitiveException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private Integer code;
    private String message;
    private String detailMessage;

    public SensitiveException() {
    }

    public SensitiveException(String message) {
        this.message = message;
    }

    public SensitiveException(Integer code, String message) {
        this.message = message;
        this.code = code;
    }

    public SensitiveException(Integer code, String message, String detailMessage) {
        this.message = message;
        this.code = code;
        this.detailMessage = detailMessage;
    }

    public String getDetailMessage() {
        return this.detailMessage;
    }

    public String getMessage() {
        return this.message;
    }

    public Integer getCode() {
        return this.code;
    }
}
