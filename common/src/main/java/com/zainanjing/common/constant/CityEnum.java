package com.zainanjing.common.constant;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy(false)
@ConfigurationProperties(prefix = "city-enums")
public class CityEnum {

    public static String LOCATION_ZAI;//在城市名称
    public static String LOCATION;//城市拼音
    public static String LOCATION_APP;//城市拼音
    public static String CITY_ADDRESS_NAME;    //城市名
    public static String CITY_ABBREVIATION;//城市名缩写
    public static String PREFIX_SPORDER_ID;//话费订单前缀
    public static String OFPAYMENT_USERID;//充话费账户
    public static String OFPAYMENT_PWD;//充话费密码
    public static String OFPAYMENT_KEY;//充话费密钥
    public static String CITY_NAME_FOR_BUSINESS; //商家宝名称
    public static String BUSINESS_URL; //商家宝现网域名
    public static String APP_SCHEMA; //分享页面下载用

    public static String getLOCATION_ZAI() {
        return LOCATION_ZAI;
    }

    public void setLOCATION_ZAI(String LOCATION_ZAI) {
        this.LOCATION_ZAI = LOCATION_ZAI;
    }

    public static String getLOCATION() {
        return LOCATION;
    }

    public void setLOCATION(String LOCATION) {
        this.LOCATION = LOCATION;
    }

    public static String getLOCATION_APP() {
        return LOCATION_APP;
    }

    public void setLOCATION_APP(String LOCATION_APP) {
        this.LOCATION_APP = LOCATION_APP;
    }

    public static String getCITY_ADDRESS_NAME() {
        return CITY_ADDRESS_NAME;
    }

    public void setCITY_ADDRESS_NAME(String CITY_ADDRESS_NAME) {
        this.CITY_ADDRESS_NAME = CITY_ADDRESS_NAME;
    }

    public static String getCITY_ABBREVIATION() {
        return CITY_ABBREVIATION;
    }

    public void setCITY_ABBREVIATION(String CITY_ABBREVIATION) {
        this.CITY_ABBREVIATION = CITY_ABBREVIATION;
    }

    public static String getPREFIX_SPORDER_ID() {
        return PREFIX_SPORDER_ID;
    }

    public void setPREFIX_SPORDER_ID(String PREFIX_SPORDER_ID) {
        this.PREFIX_SPORDER_ID = PREFIX_SPORDER_ID;
    }

    public static String getOFPAYMENT_USERID() {
        return OFPAYMENT_USERID;
    }

    public void setOFPAYMENT_USERID(String OFPAYMENT_USERID) {
        this.OFPAYMENT_USERID = OFPAYMENT_USERID;
    }

    public static String getOFPAYMENT_PWD() {
        return OFPAYMENT_PWD;
    }

    public void setOFPAYMENT_PWD(String OFPAYMENT_PWD) {
        this.OFPAYMENT_PWD = OFPAYMENT_PWD;
    }

    public static String getOFPAYMENT_KEY() {
        return OFPAYMENT_KEY;
    }

    public void setOFPAYMENT_KEY(String OFPAYMENT_KEY) {
        this.OFPAYMENT_KEY = OFPAYMENT_KEY;
    }

    public static String getCITY_NAME_FOR_BUSINESS() {
        return CITY_NAME_FOR_BUSINESS;
    }

    public void setCITY_NAME_FOR_BUSINESS(String CITY_NAME_FOR_BUSINESS) {
        this.CITY_NAME_FOR_BUSINESS = CITY_NAME_FOR_BUSINESS;
    }

    public static String getBUSINESS_URL() {
        return BUSINESS_URL;
    }

    public void setBUSINESS_URL(String BUSINESS_URL) {
        this.BUSINESS_URL = BUSINESS_URL;
    }

    public static String getAPP_SCHEMA() {
        return APP_SCHEMA;
    }

    public void setAPP_SCHEMA(String APP_SCHEMA) {
        this.APP_SCHEMA = APP_SCHEMA;
    }
}
