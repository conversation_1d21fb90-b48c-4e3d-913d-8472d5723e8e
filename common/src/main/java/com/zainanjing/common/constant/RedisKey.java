package com.zainanjing.common.constant;


import com.ruoyi.common.utils.StrUtil;

public enum RedisKey {

    TENCENT_ACCESS_TOKEN_TIMESTAMP("TENCENT_ACCESS_TOKEN_TIMESTAMP", "腾讯云accesstoken时间戳"),
    TENCENT_ACCESS_TOKEN("TENCENT_ACCESS_TOKEN", "腾讯云accesstoken"),

    SMS_CHANNEL("SMS_CHANNEL", "0:原短信平台1:阿里云短信平台:2滨礼短信"),
    SMS_FIRST_URL("SMS_FIRST_URL", "原短信平台url"),
    SMS_FIRST_USERID("SMS_FIRST_USERID", "原短信平台userid"),
    SMS_FIRST_ACCOUNT("SMS_FIRST_ACCOUNT", "原短信平台account"),
    SMS_FIRST_PASSWORD("SMS_FIRST_PASSWORD", "原短信平台password"),
    SMS_FIRST_ACTION("SMS_FIRST_ACTION", "原短信平台action"),
    SMS_ALI_ACCESSKEYID("SMS_ALI_ACCESSKEYID", "阿里云短信帐号"),
    SMS_ALI_ACCESSKEYSECRET("SMS_ALI_ACCESSKEYSECRET", "阿里云短信密码"),

    AUTHENTICATION_FAILNUM("AUTHENTICATION_FAILNUM", "实名认证失败次数"),
    YAOYAOGETTOKENVALUE("YAOYAOGETTOKENVALUE", "瑶瑶水电煤缴费获取token值"),

    ARTICLE_CLICKNUM("ARTICLE_CLICKNUM", "新闻点击量"),
    ARTICLE_IS_SYNC_CLICKNUM("ARTICLE_IS_SYNC_CLICKNUM", "是否正在同步新闻阅读量"),


    ARTICLE_IS_COMMENT("ARTICLE_IS_COMMENT", "新闻是否开启评论"),
    ARTICLE_IS_EXAMINE("ARTICLE_IS_EXAMINE", "新闻是否开启评论审核"),
    ARTICLE_IS_CLICKNUM("ARTICLE_IS_CLICKNUM", "新闻是否显示阅读量"),
    ARTICLE_IS_SOURCE("ARTICLE_IS_SOURCE", "新闻是否显示来源"),
    ARTICLE_IS_LABEL("ARTICLE_IS_LABEL", "新闻是否显示标签"),
    ARTICLE_IS_SHOWNEWS("ARTICLE_IS_SHOWNEWS", "是否显示相关新闻"),
    ARTICLE_IS_AUDITNEWS("ARTICLE_IS_AUDITNEWS", "是否开启新闻审核"),
    ARTICLE_IS_AUDITVIDEO("ARTICLE_IS_AUDITVIDEO", "是否开启短视频审核"),
    ARTICLE_IS_OPEN_SHORT_VIDEO_COMMENT("ARTICLE_IS_OPEN_SHORT_VIDEO_COMMENT", "是否开启短视频评论功能"),
    ARTICLE_IS_AUDIT_SHORT_VIDEO_COMMENT("ARTICLE_IS_AUDIT_SHORT_VIDEO_COMMENT", "是否开启短视频评论审核"),
    ARTICLE_IS_OPENT_LIVE_COMMENT("ARTICLE_IS_OPENT_LIVE_COMMENT", "是否开启红云直播评论功能"),
    ARTICLE_IS_AUDIT_LIVE_COMMENT("ARTICLE_IS_AUDIT_LIVE_COMMENT", "是否开启红云直播评论审核功能"),
    ARTICIE_IS_OPEN_BC_COMMENT("ARTICLE_IS_OPENT_BC_COMMENT", "是否开启紫金评论功能"),
    ARTICLE_IS_AUDIT_BC_COMMENT("ARTICLE_IS_AUDIT_BC_COMMENT", "是否开启紫金评论审核功能"),
    ARTICLE_IS_USERWATERMARK("ARTICLE_IS_USERWATERMARK", "是否使用水印"),
    ARTICLE_INITVIEW_MIN("ARTICLE_INITVIEW_MIN", "初始点击量最小值"),
    ARTICLE_INITVIEW_MAX("ARTICLE_INITVIEW_MAX", "初始点击量最大值"),
    ARTICLE_HOT_DAYNUM("ARTICLE_HOT_DAYNUM", "资讯热榜查询天数"),
    ARTICLE_COMMENT_PRAISE("ARTICLE_COMMENT_PRAISE", "新闻评论点赞"),
    ARTICLE_COMMENT_PRAISE_NUM("ARTICLE_COMMENT_PRAISE_NUM", "新闻评论点赞数"),

    GOODS_CLICKNUM("GOODS_CLICKNUM", "商品点击量"),
    GOODS_BIDNUM("GOODS_BIDNUM", "出价人数"),
    GOODS_COMMENT_IS_CLOSE("GOODS_COMMENT_IS_CLOSE", "商品评论是否关闭"),
    GOODS_COMMENT_IS_AUDIT("GOODS_COMMENT_IS_AUDIT", "商品评论是否审核"),
    AUDIT_GOODS_AREA("AUDIT_GOODS_AREA", "商品分区审核"),

    SQ_POST_IS_CLOSE("SQ_POST_IS_CLOSE", "社区发帖是否开启"),
    SQ_COMMENT_IS_CLOSE("SQ_COMMENT_IS_CLOSE", "社区回帖是否开启"),
    SQ_POST_IS_AUDIT("SQ_POST_IS_AUDIT", "社区帖子是否审核"),
    SQ_COMMENT_IS_AUDIT("SQ_COMMENT_IS_AUDIT", "社区回帖是否审核"),


    LABEL_OWN_NUM("LABEL_OWN_NUM", "标签拥有数量"),

    GOODS_STOCK("GOODS_STOCK", "商品库存"),
    GOODS_SALE_NUM("GOODS_SALE_NUM", "商品销量"),
    PRODUCT_STOCK("PRODUCT_STOCK", "产品库存"),
    ALL_PRODUCT_DEFAULT_STOCK("ALL_PRODUCT_DEFAULT_STOCK", "产品原始库存"),
    PRODUCT_SALE_NUM("PRODUCT_SALE_NUM", "产品销量"),

    SHOP_SALE_NUM("SHOP_SALE_NUM", "店铺销量"),
    SHOP_SALE_NUM_MANUAL("SHOP_SALE_NUM_MANUAL", "店铺人工销量"),
    RUSH_GOODS_STOCK("GOODS_STOCK_RUSH", "抢购商品库存"),
    RUSH_GOODS_SALE_NUM("GOODS_SALE_NUM_RUSH", "抢购商品销量"),
    RUSH_PRODUCT_STOCK("PRODUCT_STOCK_RUSH", "抢购产品库存"),
    RUSH_PRODUCT_SALE_NUM("PRODUCT_SALE_NUM_RUSH", "抢购产品销量"),
    RUSH_COUPON_NUM("COUPON_NUM_RUSH", "抢购类优惠券数量"),
    RUSH_COUPON_RECEIVED_NUM("COUPON_RECEIVED_NUM_RUSH", "抢购类优惠券已领取数量"),

    WEATHER("WEATHER", "天气预报"),
    REGION_LASTMODIFYTIME("REGION_LASTMODIFYTIME", "地区最后更新时间"),
    HOTSEARCH_LASTMODIFYTIME("HOTSEARCH_LASTMODIFYTIME", "热门搜索最后更新时间"),
    ATTENT("ATTENT", "关注"),
    BE_ATTENTED("BE_ATTENTED", "被关注"),
    ATTENT_TOTAL("ATTENT_TOTAL", "关注总数"),
    BE_ATTENT_TOTAL("BE_ATTENT_TOTAL", "被关注总数"),
    MSG_PUSH_TOTAL("MSG_PUSH_TOTAL", "后台推送消息总数"),
    CLOSE("CLOSE", "屏蔽"),
    CLOSE_TOTAL("CLOSE_TOTAL", "被屏蔽总数"),
    SQ_MESSAGE_ID("SQ_MESSAGE_ID", "系统消息id 自增"),
    QIANGGOU_REMIND("QIANGGOU_REMIND", "抢购商品提醒 QIANGGOU_REMIND_goodsId_uid"),
    SEND_MOBILE_TIME("SEND_MOBILE_TIME", "用户上次调发短信时间戳 用于发短信1分钟只能调一次需求 SEND_MOBILE_TIME_15812345678"),
    VALIDATE_VCODE_TIMES("VALIDATE_VCODE_TIMES", "某一天内调验证码次数  VALIDATE_VCODE_TIMES_15812345678_20180507135803"),
    VALIDATE_VCODE_TIMES_IP("VALIDATE_VCODE_TIMES_IP", "同一ip某一天内调验证码次数  VALIDATE_VCODE_TIMES_IP_218.94.40.188"),
    ALI_FACE_TRANSACTION_ID("ALI_FACE_TRANSACTION_ID", "阿里人脸识别串号  ALI_FACE_TRANSACTION_ID"),
    SHOP_MSG_NEW_ORDER("SHOP_MSG_NEW_ORDER", "有新订单推送"),
    SHOP_MSG_SCAN_ORDER("SHOP_MSG_SCAN_ORDER", "有新扫码支付订单"),
    SHOP_MSG_RECEIVE_GOODS("SHOP_MSG_RECEIVE_GOODS", "用户确认收货"),
    SHOP_MSG_NEW_COMMENT("SHOP_MSG_NEW_COMMENT", "有新的评价"),

    BINDPHONE("BINDPHONE", "绑定手机号"),
    //20220907 用户中心修改手机号码 //包含哪些城市 表示对应城市手机号码已改。ID为gdmm_wap_mq_log主键 , 即生产者id
    //key = nanjing_uc_BINDPHONE_CITYS_53 ,value=[nanjing,nanning,liupanshui]  value包含的城市表示该城市消费成功, 当 包含所有城市 ，更新生产者 状态为1 表示整个流程成功。
    BINDPHONE_CITYS("BINDPHONE_CITYS", "根据reids值里包含的城市判断 该绑定手机的生产者 有没有被全部消费 BINDPHONE_CITYS_ID"),

    UP_QUERYID_ORDERSN("UP_ORDERID_", "云闪付订单对应的queryid"),

    MQ_ERROR("MQ_ERROR", "mq错误"),

    //	HOT_ARTICLE_CLICK_TOP("HOT_ARTICLE_CLICK_TOP","资讯点击量top10"),
    HOT_GOODS_NOW_COMMENT("HOT_GOODS_NOW_COMMENT", "实时商品评价"),
    HOT_ORDER_NOW_ORDER("HOT_ORDER_NOW_ORDER", "实时订单"),
    HOT_SQ_NEW_COMMENT("HOT_SQ_NEW_COMMENT", "最新话题评论"),
    HOT_USERS_NOW_REGISTER("HOT_USERS_NOW_REGISTER", "当前注册用户信息"),
    HOT_USERS_NOW_AUTH("HOT_USERS_NOW_AUTH", "当前实名认证用户"),

    //短视频相关
    VIDEO_PRAISE("VIDEO_PRAISE", "短视频点赞_uid_videoId"),
    VIDEO_PRAISE_NUM("VIDEO_PRAISE_NUM", "短视频点赞_videoId"),
    VIDEO_CLICKNUM("VIDEO_CLICKNUM", "短视频点击量_videoId"),
    VIDEO_IS_SYNC_CLICKNUM("VIDEO_IS_SYNC_CLICKNUM", "是否正在同步短视频点击量"),

    SYSTEM_PARAMS("SYSTEM_PARAMS", "系统参数"),

    ORDER_LEFT_NUM("ORDER_LEFT_NUM", "下单每人限购_uid_productId"),

    SALE_LOCK("SALE_LOCK", "下单加锁_productId_uid"),
    IS_CLOSE_LOCK("IS_CLOSE_LOCK", "是否关闭锁"),

    ACCESS_COUNT_PREFIX_("ACCESS_COUNT_PREFIX_", "访问控制"),

    VALIDATE_ORDER_TIMES("VALIDATE_ORDER_TIMES", "用户上次调下单时间戳 用于下单接口15秒只能调一次需求 VALIDATE_ORDER_TIMES_uid"),

    AGENT_LOG_ID("AGENT_LOG_ID", "供应商操作日志id 自增"),

    FIND_PRODUCT_DETAIL_CACHE("FIND_PRODUCT_DETAIL_CACHE", "查询产品详情缓存"),

    APP_PUSH_CONFIG("APP_PUSH_CONFIG", "用户在APP设置是否接收推送"),//目前可以设置 [系统消息  交易消息].1表示不接收   [00 01 10 11]

    XL_CONFIG("XL_CONFIG", "redis+lua限流配置 比如 nanjing_XL_CONFIG_UsersAction_findUserInfoById = 20"), //限流配置
    XL_LAST_MODIFY_TIME("XL_LAST_MODIFY_TIME", "redis+lua限流配置的最后修改时间"), //用于刷新限流配置 比如后台改了限流配置，也改下这个最后修改时间key，
    //按天统计 数据保留3天
    XL_STATISTICS("XL_STATISTICS", "redis+lua限流统计 比如 nanjing_XL_STATISTICS_UsersAction_findUserInfoById = 20"), //统计某个接口被限流了多少次
    //2ge xl_keyset 用于后台查询优化 先根据key查出set   再查redis 列表  【redis命令: lrange nanjing_XL_KEYSET_CONFIG 0 -1 】
    XL_KEYSET_CONFIG("XL_KEYSET_CONFIG", "限流配置keyset集合"), //限流配置keyset集合
    XL_KEYSET_STATISTICS("XL_KEYSET_STATISTICS", "限流统计keyset集合"), //限流统计keyset集合	//20211207 用于解决并发问题 新增一个标志位
    XL_KEYSET_STATISTICS_HOUR("XL_KEYSET_STATISTICS_HOUR", "接口请求数统计keyset集合按小时"), //接口请求数统计keyset集合	//2022021417 按小时
    PRODUCT_TEMP_STOCK_FLAG("PRODUCT_TEMP_STOCK_FLAG", "产品有这个key 表示有临时库存"),
    //20211204  用于解决并发问题，先从redis里 获取临时库存的id，再去更新gdmm_product_temp_stock，用于代替更新产品表的stock
    PRODUCT_TEMP_STOCK_IDS("PRODUCT_TEMP_STOCK_IDS", "用来存 产品临时库存表gdmm_product_temp_stock 的id 的 列表"), //

    TONGXB_ORDER_GOODS_CHACK_INTEGRAL("TONGXB_ORDER_GOODS_CHACK_INTEGRAL_%s", "通行宝积分商品id查询"),


    //20221124 job执行结果 放入redis 用于监控 告警
    JOB_RESULT("JOB_RESULT", "比如 nanjing_JOB__RESULT_tuiguang = success"), //定时任务执行结果
    JOB_RESULT_KEYSET("JOB_RESULT_KEYSET", "比如 nanjing_JOB_RESULT_KEYSET = [nanjing_job_result_20221124_cancelorder, nanjing_job_result_20221124_huaFeiBack]"), //定时任务执行结果keyset集合


    CAPTCHA("CAPTCHA", "图形验证码_uuid"), //图形验证码 CAPTCHA_12314-12414-525-6646

    //多次登录错误锁定，比如3次错误后当天不可登录（后台可人工解锁）
    //这个key 设置一个过期时间 第二天0点0分自动删除
    LOGIN_ERROR_KEYSET("LOGIN_ERROR_KEYSET", "值是列表，用来存 LOGIN_ERROR_TIEMS "), //值里存放【nanjing_LOGIN_ERROR_TIEMS_demo1_shequ,nanjing_LOGIN_ERROR_TIEMS_demo2_agent,...】
    //times写成tiems 字母写错了，已经上线了 暂时先不改了。
    LOGIN_ERROR_TIMES("LOGIN_ERROR_TIMES", "登录失败次数 超过3次 锁定,第二天自动解锁"), //登录失败次数 LOGIN_ERROR_TIEMS_15555555555
    LOGIN_ERROR_JSON("LOGIN_ERROR_JSON", "登录失败次数 超过3次 锁定,第二天自动解锁 具体信息 包括锁定时间,ip,解锁时间"), //登录失败次数具体信息 LOGIN_ERROR_JSON_15555555555

    //20230301  用于 6个月未更新口令弹框提示，提示完以后 往后延1个月 ，一个月后再弹提示
    LAST_MODIFY_PWD("LAST_MODIFY_PWD", "上一次更新密码的时间  默认从20230301开始算起 "), //上一次更新密码的时间 LAST_MODIFY_PWD_admin_shequ

    //20230315
    BIANMIN_FENSI("BIANMIN_FENSI", "便民粉丝，每个被关注的人对应一个随机码"), //BIANMIN_FENSI_1 :123456789

    //20250415社区帖子点赞
    SQ_POST_PRAISE("SQ_POST_PRAISE","社区帖子点赞_uid_postId"),
    SQ_POST_PRAISE_NUM("SQ_POST_PRAISE_NUM","社区帖子点赞数_postId"),
    //20250415社区评论点赞
    SQ_COMMENT_PRAISE("SQ_COMMENT_PRAISE","社区评论点赞_uid_commentId"),
    SQ_COMMENT_PRAISE_NUM("SQ_COMMENT_PRAISE_NUM","社区评论点赞数_commentId"),

    ;

    private RedisKey(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }


    private static String location = "";
    //这里注释掉 20171120
//	static{
//	Properties properties = PropertiesUtil.readProperties("param.properties");
//	location = properties.getProperty("location");
//} 

    private String key;
    private String desc;

    //不带资源的key
    public String getKey() {

        return key;
    }

    //带资源值
    public String getKey(String source) {
        String redisKey = "";
        if (StrUtil.isEmpty(location)) {
            redisKey = key + "_" + source;
        } else {
            redisKey = location + "_" + key + "_" + source;
        }
        return redisKey;
    }

    public String getKeyWithOutLocation(String source) {
        String redisKey = "";
        if (StrUtil.isEmpty(location)) {
            redisKey = key + "_" + source;
        } else {
            redisKey = key + "_" + source;
        }
        return redisKey;
    }

    //根据用户id获取的key  注：根据顺序传参
    //20211117 这个key 只有一个地方在用 就是用于存取屏蔽用户时间戳  . 屏蔽用户 关注用户 全都改为存RedisUtil.putSetNew了
    public String getKeyByUids(String uid, String friendUid) {

        return key + "_" + uid + "_" + friendUid;
    }

    //短视频 点赞用
    public String getKeyForVideo(String uid, String videoId) {

        return key + "_" + uid + "_" + videoId;
    }

    //通用  这里key+uid + 【业务id】
    public String getKeyForUidAndId(String uid, String id) {

        return key + "_" + uid + "_" + id;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public String getKeyForSqPost(String uid,String postId) {
        return key+"_"+uid+"_"+postId;
    }

    public String getKeyForSqComment(String uid,String commentId) {
        return key+"_"+uid+"_"+commentId;
    }
}
