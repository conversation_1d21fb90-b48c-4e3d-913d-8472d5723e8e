package com.zainanjing.common.constant;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: Constants
 * @Description: 常量类
 * @date 2015年2月13日 上午10:08:01
 */
public class Constants {

    public final static String STATUS_NORMAL = "0";
    public final static String STATUS_DELETE = "1";
    public final static String STATUS_HIDE = "2";
    public final static String STATUS_DELETE_BY_HOST = "2";

    public final static Integer SHI = 1;

    public final static Integer SHI_TWO = 2;

    public final static Integer SHI_THIRD = 3;
    public final static Integer FOU = 0;
    public final static Integer ZERO = 0;
    public final static Integer TWO = 2;// 扩展

    //状态
    public final static Integer STATUS_YES = 1;
    public final static Integer STATUS_NOW = 0;
    public final static Integer STATUS_NO = 3;

    public final static String YES = "1";
    public final static String NO = "0";

    /*
     * 点击率相关常量 新闻 ,广播帖子 , 团购商品(这里用于拍卖) ,摇一摇
     */
    public final static String ARTICLE = "ARTICLE";

    //点击率
    public final static String CLICKNUM = "CLICKNUM";
    //评论数
    public final static String COMMENTNUM = "COMMENTNUM";
    //点赞数
    public final static String PRAISENUM = "PRAISENUM";

    public static final String SHORT_VIDEO_NUM = "0";
    public static final String SHORT_VIDEO_STR = "SHORT_VIDEO";

    //新闻审核
    public final static Integer SAVE_AS_DRAFT = 2;//保存为草稿
    public final static Integer WAIT_PASS_AUDIT = 3;//待审核
    public final static Integer SQ_COMMENT_NOT_PASS = 4;//审核拒绝
    public final static Integer YES_PASS_AUDIT = 0;//审核通过
    public final static Integer NOT_PASS_AUDIT = 4;//审核拒绝
    public final static Integer YES_AUDIT = 5;//已审核
    public final static Integer NOT_SCORE = 6;//待打分
    public final static Integer YES_SCORE = 7;//已打分

    public final static Integer WAIT_AUDIT_TWO = 8;//已一审，待二审
    public final static Integer WAIT_AUDIT_THREE = 9;//已二审，待三审

    public final static Map<Integer,String> AUDIT_STATUS_MAP_BEFORE = new java.util.HashMap<Integer,String>(){
        {
            put(WAIT_PASS_AUDIT,"一审");
            put(Constants.WAIT_AUDIT_TWO,"二审");
            put(Constants.WAIT_AUDIT_THREE,"三审");
        }
    };

    public final static Map<Integer,String> AUDIT_STATUS_MAP_AFTER = new java.util.HashMap<Integer,String>(){
        {
            put(WAIT_PASS_AUDIT,"待一审");
            put(SQ_COMMENT_NOT_PASS,"审核拒绝");
            put(Constants.YES_PASS_AUDIT,"审核完成");
            put(Constants.WAIT_AUDIT_TWO,"待二审");
            put(Constants.WAIT_AUDIT_THREE,"待三审");
        }
    };

    public final static Integer DEFAULT = -1;//默认
    //新闻审核所有情况
    public final static Integer[] AUDIT_ALL = new Integer[]{Constants.WAIT_PASS_AUDIT, Constants.YES_PASS_AUDIT};

    public final static String RESULT_EROOR_SUCCESS = "200";//成功

    public final static int ONE = 1;


    // 新闻类型
    public final static Integer ARTICLE_TYPE_NOMAL = 1;// 普通新闻
    public final static Integer ARTICLE_TYPE_TUWENZHIBO = 2;// 图文直播 （不需要审核）
    public final static Integer ARTICLE_TYPE_ZHUANTI = 3;// 专题新闻

    //后台插入新闻类型
    public final static Integer ARTICLE_TYPE_NO_PICTURE = 0;//无图
    public final static Integer ARTICLE_TYPE_ORDINARY_PICTURE = 1;//普通
    public final static Integer ARTICLE_TYPE_MANY_PICTURE = 2;//多图
    public final static Integer ARTICLE_TYPE_SINGLE_PICTURE = 3;//单图

    public final static String ORDER_BY_SORT_ID_DESC = "2";


    public final static Integer AUCTION_SCHEDULE_STATUS_INIT = 0;
    public final static Integer AUCTION_SCHEDULE_STATUS_SUCCESS = 1;
    public final static Integer AUCTION_SCHEDULE_STATUS_DELETE = 2;
    public final static Integer AUCTION_SCHEDULE_STATUS_LOSS = 3;

    public final static String SQFORUM = "SQFORUM";

    //今日发帖总数和回复数
    public final static String TODAYPOSTNUM = "TODAYPOSTNUM";
    //主题数量
    public final static String SUBJECTNUM = "SUBJECTNUM";
    //回复数量
    public final static String REPLYNUM = "REPLYNUM";

    public final static  String LABEL_TYPE_SQFORUM = "4";
    public final static  String LABEL_TYPE_SQFORUM_POST = "5";


    public final static String SQPOST = "SQPOST";

    public final static Integer  SQMESSAGE_TYPE_SQ = 7;

    public final static String  SQMESSAGE_SQTYPE_SQPOST = "SQPOST";//"社区帖";

    public final static String  SQMESSAGE_CODE_COMMENT = "71";
    public final static String  SQMESSAGE_CODE_REWARD = "73";
    //71评论  73 打赏
    public final static String []  SQMESSAGE_CODE_LIST_POST = new String[]{SQMESSAGE_CODE_COMMENT,SQMESSAGE_CODE_REWARD};

    public final static String TYPE_REPLAY_POST = "1";
    public final static String TYPE_REPLAY_COMMENT = "2";

    public final static String TYPE_POST = "1";
    public final static String TYPE_COMMENT = "2";

    /*
     * 私信相关 私信类型  1文字   2图片  3,4,5预留  		6 所有交易  7 社区  8 关注
     */
    public  final static  String SQ_MESSAGE_TYPE_WORD = "1";
    public  final static  String SQ_MESSAGE_TYPE_IMAGE = "2";

    /**
     * 后台操作类型 1 创建  2更新   3删除
     */
    public final static String SHEQU_ADD ="1";
    public final static String SHEQU_UPDATE ="2";
    public final static String SHEQU_DELETE ="3";

    public final static Integer SITEAPP_INTERFACE=2;//接口
    public final static Integer MAGAGE_BACK=1;//管理后台

    //广播帖子相关
    public final static String POST_TYPE_COMMON = "1";
    public final static String POST_TYPE_CPMPERE = "2";

    //社区发帖、回帖
    public final static Integer SQ_WAIT_AUDIT = 4;//待审核
    public final static Integer SQ_POST_NOT_PASS = 5;//审核拒绝

    public final static Integer COMMENT_CONTENT_MAX_SIZE = 500;

    public final static String IMG_TYPE_POST = "1";  //这里理解为bcpost key=zjfm
    public final static String IMG_TYPE_COMMENT = "2";
    public final static String IMG_TYPE_ZJPOST = "11";//紫金帖子 20190828  【由于都是zj_topic_post表 都用图片都用11】 这里紫金帖子11 和互动 13 ，
    public final static String IMG_TYPE_ZJCOMMENT = "12";//紫金回帖  20190828
    public final static String IMAGE_TYPE_SQPOST = "3";//社区发帖
    public final static String IMAGE_TYPE_SQCOMMENT = "4";//社区回帖
    public final static String IMAGE_TYPE_AUDIO = "5";// 广播语音回帖
    public final static String IMAGE_TYPE_HOMEPAGE = "6";//个人主页相册
    public final static String IMAGE_TYPE_ORDER_REFUND = "7";//实体退货1-3张图
    public final static String IMAGE_TYPE_SUGGESTION = "8";//用户反馈
    public final static String IMAGE_TYPE_SHORT_VIDEO = "9";//短视频
    public final static String IMAGE_TYPE_HONGYUN_LIVE = "10";//红云直播评论

    public final static String SMALL_PIC_SIZE = "@48W_48H_100Q.";


    /**
     * 收藏类型APP
     */
    public final static String ELECTRIC_COLLECT  = "0";
    public final static String GROUP_COLLECT = "1";
    public final static String NEWS_COLLECT= "2";
    public final static String RADIO_COLLECT= "3";
    public final static String QINGLING_COLLECT= "4";
    public final static String FORUM_COLLECT= "5";//广播讨论版
    public final static String POST_COLLECT= "51";//广播帖子
    public final static String SQ_POST_COLLECT= "6";//社区帖子
    public final static String SQ_FORUM_COLLECT= "7";//社区讨论版
    public final static String VIDEO_COLLECT= "8";//短视频
    public final static String DOCUMENTARY_COLLECT= "9";//纪录片
    //收藏类型APP数组
    public final static String[] MY_COLLECT_TYPE_LIST = new String[]{
            GROUP_COLLECT, NEWS_COLLECT, RADIO_COLLECT, QINGLING_COLLECT, FORUM_COLLECT, POST_COLLECT, SQ_POST_COLLECT
            ,VIDEO_COLLECT,DOCUMENTARY_COLLECT
    };

}
