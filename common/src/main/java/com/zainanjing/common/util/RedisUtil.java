package com.zainanjing.common.util;

import com.zainanjing.common.constant.CityEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: RedisUtil
 * @Description: redis工具类
 * @date 2015年3月2日 上午10:57:38
 */
public class RedisUtil {

    private static Logger logger = LoggerFactory.getLogger(RedisUtil.class);

    private static RedisTemplate<Object, Object> jedisTemplate;

    public static void setJedisTemplate(RedisTemplate jedisTemplate) {
        RedisUtil.jedisTemplate = jedisTemplate;
    }

    /**
     * findString
     *
     * @param redisKey
     * @param needException
     * @return
     * @Description redis 取
     */

    public static String findString(Object redisKey, Boolean... needException) {
        try {
            redisKey = CityEnum.getLOCATION() + "_" + redisKey;

            return (String) jedisTemplate.opsForValue().get(redisKey);
        } catch (Exception e) {
            logger.error("redis  findObject查询异常，key={}--->{}", redisKey, e);
            if (needException != null && needException.length > 0 && needException[0]) {
                throw e;
            }
            return "";
        }
    }


    /**
     * @param redisKey
     * @return Long
     * @Title:findLong
     * @Description redis 取
     */

    public static Long findLong(String redisKey, Boolean... needException) {
        redisKey = CityEnum.getLOCATION() + "_" + redisKey;
        Long value = 0L;
        try {

            String resValue = (String) jedisTemplate.opsForValue().get(redisKey);
            if (resValue != null) {
                value = Long.parseLong(resValue);
            }
        } catch (Exception e) {
            logger.error("redis findLong查询异常，key={}--->{}", redisKey, e);
            if (needException != null && needException.length > 0 && needException[0]) {
                throw e;
            }
            return 0L;
        }
        return value;
    }

    //20211116 新增redis 查询列表

    public static Set<Object> findSetNew(Object pattern, Boolean... needException) {
        Set<Object> dataList = new HashSet<Object>();
        pattern = CityEnum.getLOCATION() + "_" + pattern;
        try {

            List tempList = jedisTemplate.opsForList().range(pattern, 0, -1);
            if (tempList != null && !tempList.isEmpty()) {
                dataList.addAll(tempList);
            }

        } catch (Exception e) {
            logger.error("redis findSetNew查询异常，key={}--->{}", pattern, e);
            Boolean isException = false;
            if (needException != null && needException.length > 0) {
                isException = needException[0];
            }
            if (isException) {
                throw e;
            }
        }
        return dataList;
    }


    /**
     * @param keyword
     * @return Map<Long, Long>
     * @Title:findMapByKeyword
     * @Description Job  redis2db  在用  不需要回滚
     */

    //20211122 keys 改为 scan
    public static Map<Long, Long> findMapByKeyword(String keyword) {
        keyword = CityEnum.getLOCATION() + "_" + keyword;
        Set<String> keys = scanKeys(keyword, 10L);
        Iterator<String> it = keys.iterator();
        Map<Long, Long> map = new HashMap<Long, Long>();

        while (it.hasNext()) {
            String key = it.next();
            String value = (String) jedisTemplate.opsForValue().get(key);
            try {
                String[] keyArray = key.split("_");
                if (keyArray != null && keyArray.length > 0) {
                    int length = keyArray.length;
                    map.put(Long.valueOf(keyArray[length - 1]),
                            Long.valueOf(value));
                }
            } catch (Exception e) {
                logger.error("redis获取放入map出错 key={}value{}{}", key, value, e);
            }
        }
        return map;
    }

    //20211122 新增 返回所有的key列表
    //20220929 这个 是redis遍历查询，效率太低 ，代码里尽量不要用，目前只有几个定时任务使用
    @Deprecated
    public static Set<String> findSetByKeyword(String keyword) {
        keyword = CityEnum.getLOCATION() + "_" + keyword;
        Set<String> keys = scanKeys(keyword, 10L);
        return keys;
    }

    /**
     * @param redisKey
     * @param addNum
     * @return Long
     * @Title:add
     * @Description redis 加
     */

    public static Long add(Boolean needException, String redisKey, Integer... addNum) {
        redisKey = CityEnum.getLOCATION() + "_" + redisKey;
        Long num = 0L;
        Integer incrementNum = 1;
        if (addNum.length > 0) {
            incrementNum = addNum[0];
        }
        try {

            String resValue = (String) jedisTemplate.opsForValue().get(redisKey);
            if (resValue != null) {
                num = jedisTemplate.opsForValue().increment(redisKey, incrementNum);
            } else {
                jedisTemplate.opsForValue().set(redisKey, incrementNum);
                num = incrementNum.longValue();
            }
        } catch (Exception e) {
            logger.error("redis add异常，key={}--->{}", redisKey, e);
            if (needException) {
                throw e;
            }
        }
        return num;
    }

    /**
     * @param redisKey
     * @param minusNum
     * @return Long
     * @Title:minus
     * @Description redis 减
     */

    public static Long minus(Boolean needException, String redisKey, Integer... minusNum) {
        redisKey = CityEnum.getLOCATION() + "_" + redisKey;
        Long num = 0L;
        Integer incrementNum = -1;
        if (minusNum.length > 0) {
            incrementNum = 0 - minusNum[0];
        }
        try {

            String resValue = (String) jedisTemplate.opsForValue().get(redisKey);
            if (resValue != null) {
                if ((Long.parseLong(resValue) + incrementNum.longValue()) > 0) {
                    num = jedisTemplate.opsForValue().increment(redisKey, incrementNum);
                } else {
                    num = 0L;
                    jedisTemplate.opsForValue().set(redisKey, num);
                }
            } else {
                jedisTemplate.opsForValue().set(redisKey, num);
            }
        } catch (Exception e) {
            logger.error("redis  minus异常，key={}--->{}", redisKey, e);
            if (needException) {
                throw e;
            }
        }
        return num;
    }


    /**
     * redis操作插入通用 可拓展
     */
    public static void put(RedisTemplate<String, String> jedisTemplate,
                           String redisKey, String value) {
        jedisTemplate.opsForValue().set(redisKey, value);
    }


    public static void putString(String redisKey, String value, Boolean... needException) {
        redisKey = CityEnum.getLOCATION() + "_" + redisKey;
        try {

            jedisTemplate.opsForValue().set(redisKey, value);
        } catch (Exception e) {
            logger.error("redis  putObject异常，key={}--->{}", redisKey, e);
            if (needException != null && needException.length > 0 && needException[0]) {
                throw e;
            }
        }
    }

    /**
     * delete
     *
     * @param redisKey
     * @throws Exception void
     * @Description redis 删除
     */

    public static void delete(String redisKey, Boolean... needException) throws Exception {
        redisKey = CityEnum.getLOCATION() + "_" + redisKey;
        try {

            jedisTemplate.delete(redisKey);
        } catch (Exception e) {
            logger.error("redis  delete异常，key={}--->{}", redisKey, e);
            if (needException != null && needException.length > 0 && needException[0]) {
                throw e;
            }
        }
    }

    public static Set<String> scanKeys(final String key, final Long limit) {
        final String logPrefix = "redis scan 查询 " + "key=" + key + ",limit=" + limit;
        Integer startTime = CommonUtil.getTimestamp();
        logger.error(logPrefix);

        Set<String> keys = jedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> binaryKeys = new HashSet<>();
            try (Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(key).count(limit).build())) {
                while (cursor.hasNext()) {
                    binaryKeys.add(new String(cursor.next(), StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
                logger.error("{} Exception occurred while scanning keys", logPrefix, e);
                return new HashSet<>();
            }
            return binaryKeys;
        });

        Integer now = CommonUtil.getTimestamp();
        Integer useTime = now - startTime;
        logger.error("{} 总共查到key数量={},用时={}秒", logPrefix, keys.size(), useTime);
        return keys;
    }

}
