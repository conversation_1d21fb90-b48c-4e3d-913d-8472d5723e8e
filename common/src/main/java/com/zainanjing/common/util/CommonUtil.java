package com.zainanjing.common.util;

import com.ruoyi.common.utils.DateUtil;
import com.ruoyi.common.utils.OSSUtil;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {

    protected static final Logger logger = LoggerFactory.getLogger(CommonUtil.class);

    public static Integer getTimestamp() {
        return Integer.parseInt(String.valueOf(System.currentTimeMillis())
                .substring(0, 10));
    }

    public static Long getLongTimestamp() {
        return Long.parseLong(String.valueOf(System.currentTimeMillis())
                .substring(0, 10));
    }

    /**
     * @param date
     * @return Long
     * @Title: getTime
     * @Description: 根据日期获得时间戳
     */
    public static Long getTime(String date) {
        return Long.parseLong(String
                .valueOf(DateUtil.parseDate(date).getTime()).substring(0, 10));
    }

    public static Integer getIntegerTime(String date) {
        return Integer.parseInt(String
                .valueOf(DateUtil.parseDate(date).getTime()).substring(0, 10));
    }

    public static Long redisCommonAdd(String redisKey) {
        return RedisUtil.add(false, redisKey);
    }

    public static Long redisCommonMinus(String redisKey) {

        return RedisUtil.minus(false, redisKey);
    }

    public static String nullToDefault(Object str, String defaultStr) {
        if (str == null || "".equals(str)) {
            return defaultStr;
        }
        return str.toString();
    }

    public static void redisCommonPut(String redisKey, String value) {
        RedisUtil.putString(redisKey, value);
    }

    //20220929 这个 是redis遍历查询，效率太低 ，代码里尽量不要用，目前只有几个定时任务在用
    @Deprecated
    public static Map<Long, Long> getRedisByKeyword(
            String keyword) {
        return RedisUtil.findMapByKeyword(keyword);
    }

    public static String getDate2(Long dateline) {
        return Util.getSdf(Util.patten2).format(new Date(dateline * 1000L));
    }

    public static Long redisCommonFind(String redisKey) {
        return RedisUtil.findLong(redisKey);
    }

    /**
     * 截取字符串中的图片路径
     *
     * @param htmlStr
     * @return
     */
    public static List<String> getImgSrc(String htmlStr) {
        if (htmlStr == null) {
            return null;
        }
        String goods = "<section[^>]*?>[\\s\\S]*?<\\/section>";
        htmlStr = htmlStr.replaceAll(goods, "");
        String videos = "<video[^>]*?>[\\s\\S]*?<\\/video>";
        htmlStr = htmlStr.replaceAll(videos, "");
        String img = "";
        List<String> pics = new ArrayList<String>();
        String regEx_img = "<img[^>]*\\s+src\\s*=\\s*\"([^\"]*?)\"[^>]*>";
        Pattern p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
        Matcher m_image = p_image.matcher(htmlStr);
        while (m_image.find()) {
            img = m_image.group(1);
            if (img.contains("img.")) {
                pics.add(img);
            }
        }
        return pics;
    }

    /**
     * 根据当前uid查询已屏蔽的用户id 逗号分隔
     * 此方法抛出异常
     */
    //20211116 改为 从列表里取  之前是keys*
    public static Set<String> getCloseUids(Object uid) {
        //存放屏蔽的用户ids
        Set<String> closeUids = new HashSet<>();
        //屏蔽列表 CLOSE_UID_xxx  模糊查询符合的keys
        if (uid != null) {
            String patternKey = RedisKey.CLOSE.getKey(uid.toString());
            try {
                Set<Object> set = CommonUtil.redisKeysFind(patternKey);
                if (set != null && set.size() > 0) {
                    for (Object obj : set) {
                        if (obj instanceof String closeId) {
                            closeUids.add(closeId);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("根据当前uid= {} 查询屏蔽的用户id报错=={}", uid, e);
            }

        }
        return closeUids;
    }

    /**
     * redis模糊查询
     * 此方法抛出异常
     */
    public static Set<Object> redisKeysFind(Object pattern){
        return RedisUtil.findSetNew(pattern);
    }

    /**
     * @param postId
     * @param isHot
     * @return Boolean
     * @Title:sqPostIsHot
     * @Description 判断帖子是否热门, 这里不考虑取消点赞
     */
    public static Boolean isSqPostNeedSetHot(String postId, Integer isHot) {
        Boolean flag = false;
        if (isHot > 0) {
            return flag;
        } else {
            long clickNum = redisCommonFind(
                    Constants.SQPOST + "_" + Constants.CLICKNUM + "_" + postId
            ).longValue();
            long praiseNum = redisCommonFind(
                    Constants.SQPOST + "_" + Constants.PRAISENUM + "_" + postId
            ).longValue();
            long commentNum = redisCommonFind(
                    Constants.SQPOST + "_" + Constants.COMMENTNUM + "_"
                            + postId).longValue();
            if (clickNum > 50L || praiseNum > 10L || commentNum > 5L) {
                flag = true;
            }
        }
        return flag;
    }

    public static String commonFindPicPath(Integer avaType, String imgUrl) {
        if (avaType == null) {
            imgUrl = "";
        } else if (avaType == 0) {
            // 表示从php那边导入的头像路径
            imgUrl = OSSUtil.getImageURL(imgUrl);
        } else {
            // 表示通过java接口上传的头像 数据库中存放的是原图 需要转换获取大图
            String imageType = imgUrl.split("\\.")[1];
            String key = imgUrl + Constants.SMALL_PIC_SIZE + imageType;
            imgUrl = OSSUtil.getImageURL(key);
        }
        return imgUrl;
    }
}
