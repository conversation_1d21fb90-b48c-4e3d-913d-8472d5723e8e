package com.zainanjing.common.util;

import com.ruoyi.common.utils.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @ClassName: Util
 * @Description: 工具类 ，可用来时间转换
 * @date 2015年2月13日 上午10:44:41
 */
public class Util {

    protected final static Logger logger = LoggerFactory.getLogger(Util.class);

    // 11
    // 22
    // 33
    public static boolean isNull(String s) {
        if (s == null) {
            return true;
        }
        if (s.trim().equals("")) {
            return true;
        }
        if (s.trim().equals("null")) {
            return true;
        }
        return false;
    }

    // public static SimpleDateFormat sdfDate = new
    // SimpleDateFormat("yyyy-MM-dd");
    //
    // public static SimpleDateFormat sdfDateTime = new SimpleDateFormat(
    // "yyyy-MM-dd HH:mm:ss");
    //
    public static SimpleDateFormat sdfYMDHMS = new SimpleDateFormat(
            "yyyyMMddHHmmss");

    public static final String patten1 = "yyyy-MM-dd";
    public static final String patten2 = "yyyy-MM-dd HH:mm:ss";
    public static final String patten3 = "yyyyMMddHHmmss";
    public static final String pattenCron = "ss mm HH dd MM ? yyyy";
    public static final String patten4 = "yyyy-MM-dd HH:mm";
    public static final String patten5 = "M.dd";
    public static final String patten6 = "MM-dd HH:mm";
    // static{
    // final SimpleDateFormat sdfDate = getSdf(patten1);
    //
    // final SimpleDateFormat sdfDateTime = getSdf(patten2);
    //
    // }
    @SuppressWarnings("unused")
    public static DecimalFormat decimalFormat = new DecimalFormat("0.00");

    public static SimpleDateFormat getSdf(final String pattern) {
        return new SimpleDateFormat(pattern);
    }

    /**
     * 把字符串解析成日期格式时间
     *
     * @param dateStr
     * @return
     */
    public static Date parseDate(String dateStr) {
        try {
            return getSdf(patten1).parse(dateStr);
        } catch (ParseException e) {
            logger.error("把字符串解析成日期格式时间报错dateStr=" + dateStr);
        }
        return null;
    }

    public static Date parseDateTime(String dateTimeStr) {
        try {
            return getSdf(patten2).parse(dateTimeStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String formatDate(Date date) {
        return getSdf(patten1).format(date);
    }

    public static String formatDateTime(Date date) {
        return getSdf(patten2).format(date);
    }

    public static String formatCron(Date date) {
        return getSdf(pattenCron).format(date);
    }

    /**
     * 获取验证码
     */
    public static String getValidateCode(int length) {
        String code = "";
        for (int i = 0; i < length; i++) {
            code += getRandomCode();
        }
        return code;
    }

    private static String getRandomCode() {
        Random r = new Random();
        int code = r.nextInt(10);
        if (code == 0) {
            return getRandomCode();
        } else {
            return "" + code;
        }
    }

    public static long formatMobileTime(Date time) {
        if (time == null) {
            return 0;
        } else {
            return time.getTime() / 1000;
        }
    }

    /**
     * 去除html标签
     *
     * @param inputString
     * @return
     */
    public static String html2Text(String inputString) {
        String htmlStr = inputString; // 含html标签的字符串
        String textStr = "";
        Pattern p_script;
        java.util.regex.Matcher m_script;
        Pattern p_style;
        java.util.regex.Matcher m_style;
        Pattern p_html;
        java.util.regex.Matcher m_html;
        Pattern p_html1;
        java.util.regex.Matcher m_html1;
        try {
            // 定义script的正则表达式{或<script[^>]*?>[//s//S]*?<///script>
            String regEx_script = "<[//s]*?script[^>]*?>[//s//S]*?<[//s]*?///[//s]*?script[//s]*?>";
            // 定义style的正则表达式{或<style[^>]*?>[//s//S]*?<///style>
            String regEx_style = "<[//s]*?style[^>]*?>[//s//S]*?<[//s]*?///[//s]*?style[//s]*?>";
            // 定义HTML标签的正则表达式
            String regEx_html = "<[^>]+>";
            String regEx_html1 = "<[^>]+";
            p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
            m_script = p_script.matcher(htmlStr);
            htmlStr = m_script.replaceAll(""); // 过滤script标签
            p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
            m_style = p_style.matcher(htmlStr);
            htmlStr = m_style.replaceAll(""); // 过滤style标签
            p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
            m_html = p_html.matcher(htmlStr);
            htmlStr = m_html.replaceAll(""); // 过滤html标签

            p_html1 = Pattern.compile(regEx_html1, Pattern.CASE_INSENSITIVE);
            m_html1 = p_html1.matcher(htmlStr);
            htmlStr = m_html1.replaceAll(""); // 过滤html标签
            textStr = htmlStr;
        } catch (Exception e) {
            System.err.println("Html2Text: " + e.getMessage());
        }
        return textStr;// 返回文本字符串
    }

    // 比较时间大小
    public static boolean startAndEndTime(String starttime, String endtime)
            throws Exception {
        if (StrUtil.isNotEmpty(starttime)
                && StrUtil.isNotEmpty(endtime)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(starttime);
            Date end = sdf.parse(endtime);
            if (start.after(end)) {
                return true;

            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    // 转换格式
    @SuppressWarnings("rawtypes")
    public static Serializable[] getSerializable(List<Long> list) {
        Object[] eleOrderStr = (Object[]) list.toArray();
        Serializable[] sb = new Serializable[eleOrderStr.length];
        for (int i = 0; i < sb.length; i++) {
            sb[i] = (Serializable) eleOrderStr[i];
        }
        return sb;
    }

    /**
     * 获得当天0点时间 时间戳
     */
    public static int getTimesmorning() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (int) (cal.getTimeInMillis() / 1000);
    }

    /**
     * 获得当天24点时间 时间戳
     */
    public static int getTimesnight() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 24);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (int) (cal.getTimeInMillis() / 1000);
    }

    /**
     * 获得昨天0点时间 时间戳
     */
    public static int getYesterdayMorning() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (int) (cal.getTimeInMillis() / 1000);
    }

    /**
     * 获得昨天24点时间 时间戳
     */
    public static int getYesterdayNight() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        cal.set(Calendar.HOUR_OF_DAY, 24);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (int) (cal.getTimeInMillis() / 1000);
    }

    /**
     * 获得昨天星期几
     */
    public static int getYesterdayWeek() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, -2);
        return (int) c.get(Calendar.DAY_OF_WEEK);
    }


}
