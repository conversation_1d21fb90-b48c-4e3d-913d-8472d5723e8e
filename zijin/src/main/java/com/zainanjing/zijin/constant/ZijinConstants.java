package com.zainanjing.zijin.constant;

public class ZijinConstants {

    public final static String YES = "1";
    public final static String NO = "0";
    //点击率相关常量 新闻 ,广播帖子 , 团购商品(这里用于拍卖) ,摇一摇
    public final static String ARTICLE = "ARTICLE";
    //点击率
    public final static String CLICKNUM = "CLICKNUM";
    //评论数
    public final static String COMMENTNUM = "COMMENTNUM";
    //点赞数
    public final static String PRAISENUM = "PRAISENUM";

    public static final String SHORT_VIDEO_STR = "SHORT_VIDEO";

    public final static String SQFORUM = "SQFORUM";
    //今日发帖总数和回复数
    public final static String TODAYPOSTNUM = "TODAYPOSTNUM";
    //主题数量
    public final static String SUBJECTNUM = "SUBJECTNUM";
    //回复数量
    public final static String REPLYNUM = "REPLYNUM";

    public final static String SQPOST = "SQPOST";

    public final static String SQMESSAGE_CODE_COMMENT = "71";
    public final static String SQMESSAGE_CODE_REWARD = "73";
    //71评论  73 打赏
    public final static String[] SQMESSAGE_CODE_LIST_POST = new String[]{SQMESSAGE_CODE_COMMENT, SQMESSAGE_CODE_REWARD};

    public final static String CONSTANTS_0 = "0";
    public final static String CONSTANTS_1 = "1";
    public final static String CONSTANTS_2 = "2";
    public final static String CONSTANTS_3 = "3";
    public final static String CONSTANTS_4 = "4";

    public final static String BC_COMMENT_NEED_AUDIT = "30";
    public final static String SMALL_PIC_SIZE = "@48W_48H_100Q.";

    /**
     * 关联类型:10--广播话题评论;11---广播话题回复;12---广播话题二级回复;20---广播提问;21---广播提问回复;22--广播提问二级回复;30--专辑评论;
     * 31---专辑回复;32----专辑二级回复;40---直播评论;50---主播动态;51---主播动态回复;
     */
    public final static String ANCHOR_NEWS_IMAGE_TYPE = "50";
    public final static String ANCHOR_NEWS_REPLY_IMAGE_TYPE = "51";

    public final static String TOPIC_IS_AUDIT_TYPE = "1";
    public final static String HUDONG_IS_AUDIT_TYPE = "2";
    public final static String ANCHOR_NEWS_REPLY_IS_AUDIT_TYPE = "3";
    public final static String JINGXUAN_COMMENT_IS_AUDIT_TYPE = "4";

    public final static Integer JINGXUAN_YINPIN = 33;
    public final static Integer JINGXUAN_SHIPIN = 35;

    public final static Integer JINGXUAN_YINPIN_133 = 133;
    public final static Integer JINGXUAN_SHIPIN_135 = 135;
    //打赏类型 1紫金FM互动区主题帖、2社区主题帖、3商品评价、 11 新版紫金
    //打赏类型 1紫金FM互动区主题帖、2社区主题帖、3商品评价、 11 新版紫金
    public final static String REWARD_TYPE_BCPOST = "1";
    public final static String REWARD_TYPE_ZJPOST = "11";//新版紫金 20190828 zj_topic_post表
    public final static String REWARD_TYPE_ZJPOST_HUDONG = "13";//新版紫金互动 20190925  都是zj_bc_question表
    public final static String REWARD_TYPE_SQPOST = "2";
    public final static String REWARD_TYPE_GOODS_COMMENT = "3";
    //广播帖子相关
    public final static String POST_TYPE_COMMON = "1";
    public final static String POST_TYPE_CPMPERE = "2";
    public final static String POST_TYPE_REPLAY = "3";
    public final static String POST_TYPE_TOPIC = "4";
    /**
     * 新紫金 互动话踢开关 key
     */
    public final static Integer INTERACT_TOPIC_KEY = 1; //话踢
    public final static Integer INTERACT_GENERAL_KEY = 2; //互动
    public final static Integer INTERACT_VIDEO_KEY = 5; //视频

    /**
     * 收藏类型APP
     */
    public final static String ELECTRIC_COLLECT = "0";
    public final static String GROUP_COLLECT = "1";
    public final static String NEWS_COLLECT = "2";
    public final static String RADIO_COLLECT = "3";
    public final static String QINGLING_COLLECT = "4";
    public final static String FORUM_COLLECT = "5";//广播讨论版
    public final static String POST_COLLECT = "51";//广播帖子
    public final static String SQ_POST_COLLECT = "6";//社区帖子
    public final static String SQ_FORUM_COLLECT = "7";//社区讨论版
    public final static String VIDEO_COLLECT = "8";//短视频
    public final static String DOCUMENTARY_COLLECT = "9";//纪录片
    //收藏类型APP数组
    public final static String[] MY_COLLECT_TYPE_LIST = new String[]{
            GROUP_COLLECT, NEWS_COLLECT, RADIO_COLLECT, QINGLING_COLLECT, FORUM_COLLECT, POST_COLLECT, SQ_POST_COLLECT
            , VIDEO_COLLECT, DOCUMENTARY_COLLECT
    };

    public final static Integer YES_COMMENT = 0;
    public final static Integer NO_COMMENT = 1;

    public final static String BCPOST = "BCPOST";
    //帖子在版块中的行数
    public final static String FORUMNUM = "FORUMNUM";

    public final static String TYPE_REPLAY_POST = "1";
    public final static String TYPE_REPLAY_COMMENT = "2";

    public final static String IMG_TYPE_POST = "1";  //这里理解为bcpost key=zjfm
    public final static String IMG_TYPE_COMMENT = "2";
    public final static String IMG_TYPE_ZJPOST = "11";//紫金帖子 20190828  【由于都是zj_topic_post表 都用图片都用11】 这里紫金帖子11 和互动 13 ，
    public final static String IMG_TYPE_ZJCOMMENT = "12";//紫金回帖  20190828
    public final static String IMAGE_TYPE_SQPOST = "3";//社区发帖
    public final static String IMAGE_TYPE_SQCOMMENT = "4";//社区回帖
    public final static String IMAGE_TYPE_AUDIO = "5";// 广播语音回帖

    public final static Integer CHECKED_PENDING = 30;//待审核
    public final static Integer CHECKED_YES = 0;//审核通过
    public final static Integer CHECKED_NO = 32;//审核不通过
}
