package com.zainanjing.zijin.constant.GdmmCatFlow;


public enum GdmmCatFlowType {

    BuyTicket(11, "购买"), //购买粮票
    SystemSendTicket(12, "获赠"), //系统送我粮票
    IGiveTicketToOther(13, "打赏"), //我打赏别人
    TicketExpiring(14, "过期"), //系统送我粮票过期
    CatFoodExchangeGoods(21, "兑换"),
    SomeoneGiveMeCatFood(22, "获赏"),
    CatFoodExpiring(23, "过期");//猫粮过期
    /**
     *
     */
    private Integer code;
    private String display = "";


    private GdmmCatFlowType(Integer code, String display) {
        this.code = code;
        this.display = display;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public static GdmmCatFlowType valueOf(Integer value) {
        for (GdmmCatFlowType qdc : GdmmCatFlowType.values()) {
            if (qdc.getCode().equals(value)) {
                return qdc;
            }
        }
        return null;
    }

}
