package com.zainanjing.zijin.dto;

import lombok.Data;

@Data
public class SiteCollectDTO {

    // 收藏夹ID
    private String siteCollectId;

    // 会员ID
    private String siteMemberId;

    // 收藏对象ID
    private String collectObjectId;

    // 收藏对象类型:0 便宜  1 优惠  2 新闻  3电台 4青柠电台
    private Integer collectObjectType;

    // 收藏时间
    private Long collectTime;

    private String url = "";

    private Integer productStatus;//收藏商品才有  0正常 1删除  2下架  3 抢完  4过期

    private String title = "";

    private String content = "";
    //电台名称
    private String tranName = "";
    //市场价格
    private String marketprice = "";
    //本店价格
    private String original = "";
    //促销价格
    private String price = "";
    //折扣
    private String discount = "";
    //描述
    private String description = "";

    private int start;

    private String goodsbrief;

    private int size;

    private Long startTime = 0l;

    private Long entTime = 0l;

    private Long ispromote = 0l;


    private Integer showMarketPrice;//我的收藏—本地生活 是否显示市场价

}
