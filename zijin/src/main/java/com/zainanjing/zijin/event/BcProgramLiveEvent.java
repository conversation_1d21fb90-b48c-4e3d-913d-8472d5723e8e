package com.zainanjing.zijin.event;

import com.ruoyi.common.dto.EventAction;
import com.zainanjing.zijin.domain.BcProgramLive;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class BcProgramLiveEvent extends ApplicationEvent {

    private List<Integer> ids;
    private BcProgramLive bcProgramLive;
    private EventAction action;

    public BcProgramLiveEvent(Object source, BcProgramLive bcProgramLive, EventAction action) {
        super(source);
        this.bcProgramLive = bcProgramLive;
        this.action = action;
    }

    public BcProgramLiveEvent(Object source, List<Integer> ids, EventAction action) {
        super(source);
        this.ids = ids;
        this.action = action;
    }

    public BcProgramLive getBcProgramLive() {
        return bcProgramLive;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public EventAction getAction() {
        return action;
    }
}