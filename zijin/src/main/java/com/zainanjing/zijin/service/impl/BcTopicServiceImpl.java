package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.domain.BcPost;
import com.zainanjing.zijin.domain.BcTopic;
import com.zainanjing.zijin.mapper.BcForumMapper;
import com.zainanjing.zijin.mapper.BcTopicMapper;
import com.zainanjing.zijin.service.IBcTopicService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 广播话题Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class BcTopicServiceImpl extends GenericCurdServiceImpl<BcTopicMapper, BcTopic>
        implements IBcTopicService {

    @Resource
    private BcForumMapper bcForumMapper;

    @Override
    public IPage<BcTopic> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        return baseMapper.selectBcTopicsPage(pagination, searchable.getFilterCdtns());
    }

    @Override
    public int saveMore(BcTopic bcTopic) {
        bcTopic.setCreateTime(new Date());
        bcTopic.setUpdateTime(new Date());
        BcForum forum = bcForumMapper.selectById(bcTopic.getForumId());
        if (forum != null) {
            bcTopic.setFmId(forum.getFmId());
            bcTopic.setProgramId(forum.getProgramId());
        }
        return baseMapper.insert(bcTopic);
    }

    @Override
    public void addTopicView(BcPost bcPost) {
        baseMapper.addTopicView(bcPost.getTopicId());
    }
}
