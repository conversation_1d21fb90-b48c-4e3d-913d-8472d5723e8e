package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.mapper.BcAnchorMapper;
import com.zainanjing.zijin.mapper.BcAnchorRelationMapper;
import com.zainanjing.zijin.mapper.BcProgramListMapper;
import com.zainanjing.zijin.mapper.BcProgramMapper;
import com.zainanjing.zijin.service.IBcAnchorService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 主播Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class BcAnchorServiceImpl extends GenericCurdServiceImpl<BcAnchorMapper, BcAnchor>
        implements IBcAnchorService {

    @Resource
    private BcProgramListMapper bcProgramListMapper;
    @Resource
    private BcAnchorRelationMapper bcAnchorRelationMapper;
    @Resource
    private BcProgramMapper bcProgramMapper;

    @Override
    public IPage<BcAnchor> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination;
        if (!searchable.getIsPage()) {
            pagination = new Page(pageable.getPageNumber(), -1);
        } else {
            pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        }
        IPage<BcAnchor> bcAnchorList = baseMapper.selectAnchorPage(pagination, searchable.getFilterCdtns());
        if (!CollectionUtils.isEmpty(bcAnchorList.getRecords())) {
            for (BcAnchor bcAnchor : bcAnchorList.getRecords()) {
                BcAnchor model = getSelectiveAnchor(bcAnchor.getId());
                bcAnchor.setProgramName(model.getProgramName());
            }
        }
        return bcAnchorList;
    }

    @Override
    public BcCommonModel queryByPhoneNumber(String phoneNumber) {
        return baseMapper.queryByPhoneNumber(phoneNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(BcAnchor bcAnchor) {
        // 插入主播表
        baseMapper.insert(bcAnchor);

        // 获取节目ID字符串
        String programId = bcAnchor.getProgramId();
        if (programId != null) {
            // 将节目ID字符串分割为列表
            List<String> idStrList = Arrays.asList(programId.split(","));
            // 查询关联的节目单
            List<BcProgramList> bcProgramLists = bcProgramListMapper.selectByFmIdAndProgramId(bcAnchor.getChannelId(), idStrList);
            if (!CollectionUtils.isEmpty(bcProgramLists)) {
                // 创建主播关联关系并插入数据库
                List<BcAnchorRelation> relations = bcProgramLists.stream()
                        .map(bcProgramList -> {
                            BcAnchorRelation relation = new BcAnchorRelation();
                            relation.setBcId(bcProgramList.getId());
                            relation.setAnchorId(bcAnchor.getUserId());
                            return relation;
                        })
                        .toList();
                // 批量插入关联关系
                relations.forEach(bcAnchorRelationMapper::insert);
            }
        }
        return 1; // 返回成功状态
    }

    @Override
    public BcAnchor getSelectiveAnchor(Long id) {
        // 查询主播信息
        BcAnchor bcAnchor = baseMapper.selectMoreById(id);
        if (bcAnchor == null) {
            return null;
        }
        // 查询主播关联的节目单
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("anchor_id", bcAnchor.getUserId());
        List<BcAnchorRelation> relationList = bcAnchorRelationMapper.selectByMap(queryMap);
        // 提取关联的节目单ID
        List<Long> bcIdList = relationList.stream()
                .map(BcAnchorRelation::getBcId)
                .collect(Collectors.toList());
        // 查询节目信息
        if (!CollectionUtils.isEmpty(bcIdList)) {
            List<BcProgramList> programLists = bcProgramListMapper.selectByIds(bcIdList);

            List<Integer> programIdList = programLists.stream()
                    .map(BcProgramList::getProgramId)
                    .toList();
            if (!CollectionUtils.isEmpty(programIdList)) {
                List<BcProgram> programList = bcProgramMapper.selectByIds(programIdList);
                if (!CollectionUtils.isEmpty(programList)) {
                    // 使用 Stream 操作拼接字符串
                    String bcProgramName = programList.stream()
                            .map(BcProgram::getName)
                            .collect(Collectors.joining(","));

                    String programId = programList.stream()
                            .map(program -> String.valueOf(program.getId()))
                            .collect(Collectors.joining(","));
                    // 设置节目ID和节目名称
                    bcAnchor.setProgramId(programId);
                    bcAnchor.setProgramName(bcProgramName);
                }
            }


        }
        return bcAnchor;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateSelective(BcAnchor bcAnchor) {
        baseMapper.updateById(bcAnchor);
        // 获取节目ID字符串
        String programId = bcAnchor.getProgramId();
        if (programId != null) {
            List<String> idStrList = Arrays.asList(programId.split(","));
            // 查询关联的节目单
            List<BcProgramList> bcProgramLists = bcProgramListMapper.selectByFmIdAndProgramId(bcAnchor.getChannelId(), idStrList);
            if (!CollectionUtils.isEmpty(bcProgramLists)) {
                //先删除 relations表中的数据
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("anchor_id", bcAnchor.getUserId());
                bcAnchorRelationMapper.deleteByMap(queryMap);
                //再插入新的关系
                List<BcAnchorRelation> relations = bcProgramLists.stream()
                        .map(bcProgramList -> {
                            BcAnchorRelation relation = new BcAnchorRelation();
                            relation.setBcId(bcProgramList.getId());
                            relation.setAnchorId(bcAnchor.getUserId());
                            return relation;
                        })
                        .toList();
                relations.forEach(bcAnchorRelationMapper::insert);
            }
        }
        return 1;
    }
}
