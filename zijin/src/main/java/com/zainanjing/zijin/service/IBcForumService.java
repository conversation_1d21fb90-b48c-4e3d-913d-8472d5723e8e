package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.dto.SiteCollectDTO;

import java.util.List;
import java.util.Map;

/**
 * 互动区数据Service接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IBcForumService extends GenericCurdService<BcForum> {

    IPage<BcForum> findAggregateQuery(Searchable searchable);

    IPage<BcForum> selectOptionalList(IPage page, Map<String, Object> paramMap);

    List<BcAnchor> findAnchorByForumId(Long id);

    IPage<SiteCollectDTO> searchSiteCollect(IPage page, Map<Object, Object> filterMap);

    BcForum thisDateHaveLive(long forumId);
}
