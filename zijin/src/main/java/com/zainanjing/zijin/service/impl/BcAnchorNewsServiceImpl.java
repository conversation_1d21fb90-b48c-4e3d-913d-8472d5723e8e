package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.mapper.*;
import com.zainanjing.zijin.service.IBcAnchorNewsReplyService;
import com.zainanjing.zijin.service.IBcAnchorNewsService;
import com.zainanjing.zijin.service.IBcCommentImageService;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 主播动态Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class BcAnchorNewsServiceImpl extends GenericCurdServiceImpl<BcAnchorNewsMapper, BcAnchorNews>
        implements IBcAnchorNewsService {
    @Resource
    private BcAnchorRelationMapper bcAnchorRelationMapper;
    @Resource
    private BcProgramListMapper bcProgramListMapper;
    @Resource
    private BcProgramMapper bcProgramMapper;
    @Resource
    private IBcCommentImageService bcCommentImageService;
    @Resource
    private IBcAnchorNewsReplyService bcAnchorNewsReplyService;
    @Resource
    private BcCommentImageMapper bcCommentImageMapper;
    @Resource
    private BcAnchorMapper bcAnchorMapper;
    @Resource
    private BcAnchorNewsReplyMapper bcAnchorNewsReplyMapper;

    @Override
    public IPage<BcAnchorNews> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        IPage<BcAnchorNews> newsIPage = baseMapper.selectAnchorNewsPage(pagination, searchable.getFilterCdtns());
        if (!newsIPage.getRecords().isEmpty()) {
            for (BcAnchorNews bcAnchorNews : newsIPage.getRecords()) {
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("anchor_id", bcAnchorNews.getAnchorId());
                List<BcAnchorRelation> bcAnchorRelations = bcAnchorRelationMapper.selectByMap(queryMap);
                List<Long> bcIdList = bcAnchorRelations.stream()
                        .map(BcAnchorRelation::getBcId)
                        .toList();
                if (!CollectionUtils.isEmpty(bcIdList)) {
                    List<BcProgramList> programLists = bcProgramListMapper.selectByBcListId(bcIdList);
                    StringBuilder nameBuilder = new StringBuilder();
                    for (BcProgramList programList : programLists) {
                        Map<String, Object> queryBcMap = new HashMap<>();
                        queryBcMap.put("id", programList.getProgramId());
                        // queryBcMap.put("is_show", ZijinConstants.CONSTANTS_1);
                        // queryBcMap.put("status", ZijinConstants.CONSTANTS_0);
                        List<BcProgram> bcProgram = bcProgramMapper.selectByMap(queryBcMap);
                        if (!CollectionUtils.isEmpty(bcProgram)) {
                            String programNames = bcProgram.stream()
                                    .map(BcProgram::getName)
                                    .collect(Collectors.joining(","));
                            if (!nameBuilder.isEmpty()) {
                                nameBuilder.append(",");
                            }
                            nameBuilder.append(programNames);
                        }
                    }
                    bcAnchorNews.setProgramName(nameBuilder.toString());
                }
            }
        }
        return newsIPage;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public int saveAggregateQuery(BcAnchorNews bcAnchorNews) {
        // 设置创建时间和更新时间
        bcAnchorNews.setCreateTime(new Date());
        bcAnchorNews.setUpdateTime(new Date());
        bcAnchorNews.setSoureType(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        baseMapper.insert(bcAnchorNews);
        Long generatedId = bcAnchorNews.getId();
        // 0代表视频，1代表图片
        if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(bcAnchorNews.getPublishType()))) {
            // 处理图片URL
            handleImageUrls(bcAnchorNews.getImgUrl(), generatedId);
        }
        return 1; // 返回成功状态
    }

    @Override
    public BcAnchorNews getMoreById(Long id) {
        BcAnchorNews bcAnchorNews = baseMapper.selectMoreById(id);
        if (bcAnchorNews.getFileUrl().isEmpty()) {
            bcAnchorNews.setPublishType(Integer.valueOf(ZijinConstants.CONSTANTS_1));
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("ref_id", bcAnchorNews.getId());
            queryMap.put("ref_type", ZijinConstants.ANCHOR_NEWS_IMAGE_TYPE);
            List<BcCommentImage> imageList = bcCommentImageService.listByMap(queryMap);
            if (!CollectionUtils.isEmpty(imageList)) {
                String imgUrls = imageList.stream()
                        .map(BcCommentImage::getImgUrl)
                        .collect(Collectors.joining(","));
                bcAnchorNews.setImgUrl(imgUrls);
            }
        } else {
            bcAnchorNews.setPublishType(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("anchor_id", bcAnchorNews.getAnchorId());
        List<BcAnchorRelation> bcAnchorRelations = bcAnchorRelationMapper.selectByMap(queryMap);
        List<Long> bcIdList = bcAnchorRelations.stream()
                .map(BcAnchorRelation::getBcId)
                .toList();
        if (!CollectionUtils.isEmpty(bcIdList)) {
            List<BcProgramList> programLists = bcProgramListMapper.selectByBcListId(bcIdList);
            StringBuilder nameBuilder = new StringBuilder();
            for (BcProgramList programList : programLists) {
                Map<String, Object> queryBcMap = new HashMap<>();
                queryBcMap.put("id", programList.getProgramId());
                List<BcProgram> bcProgram = bcProgramMapper.selectByMap(queryBcMap);
                if (!CollectionUtils.isEmpty(bcProgram)) {
                    String programNames = bcProgram.stream()
                            .map(BcProgram::getName)
                            .collect(Collectors.joining(","));
                    if (!nameBuilder.isEmpty()) {
                        nameBuilder.append(",");
                    }
                    nameBuilder.append(programNames);
                }
            }
            bcAnchorNews.setProgramName(nameBuilder.toString());
        }
        //查询该条动态下的回复以及图片
        Map<String, Object> queryReplyMap = new HashMap<>();
        queryReplyMap.put("news_id", id);
        queryReplyMap.put("is_show", ZijinConstants.CONSTANTS_1);
        queryReplyMap.put("is_audit", ZijinConstants.CONSTANTS_1);
        List<BcAnchorNewsReply> replyList = bcAnchorNewsReplyMapper.selectByMap(queryReplyMap);
        if (!CollectionUtils.isEmpty(replyList)) {
            for (BcAnchorNewsReply reply : replyList) {
                Map<String, Object> queryImageMap = new HashMap<>();
                queryImageMap.put("ref_id", reply.getId());
                queryImageMap.put("ref_type", ZijinConstants.ANCHOR_NEWS_REPLY_IMAGE_TYPE);
                List<BcCommentImage> imgList = bcCommentImageMapper.selectByMap(queryImageMap);
                List<String> urlList = imgList.stream()
                        .map(BcCommentImage::getImgUrl) // 提取 url 字段
                        .filter(url -> url != null && !url.isEmpty()) // 过滤掉空或空字符串
                        .toList();
                reply.setReplyPictures(urlList);
            }
            bcAnchorNews.setReplyList(replyList);
        }
        return bcAnchorNews;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public int updateMoreById(BcAnchorNews bcAnchorNews) {
        baseMapper.updateById(bcAnchorNews);
        //先清空再删除
        bcCommentImageMapper.delete(new QueryWrapper<BcCommentImage>()
                .eq("ref_id", bcAnchorNews.getId())
                .eq("ref_type", ZijinConstants.ANCHOR_NEWS_IMAGE_TYPE));
        handleImageUrls(bcAnchorNews.getImgUrl(), bcAnchorNews.getId());
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNews(Long id) {
        Date now = new Date();
        // 查询子回复
        List<Long> subReplyIds = bcAnchorNewsReplyService.lambdaQuery()
                .eq(BcAnchorNewsReply::getNewsId, id)
                .eq(BcAnchorNewsReply::getIsDel, 0)
                .select(BcAnchorNewsReply::getId)
                .list()
                .stream()
                .map(BcAnchorNewsReply::getId)
                .collect(Collectors.toList());

        // 删除动态
        this.lambdaUpdate()
                .eq(BcAnchorNews::getId, id)
                .set(BcAnchorNews::getIsDel, 1)
                .set(BcAnchorNews::getUpdateTime, now)
                .update();

        // 删除动态关联的图片
        bcCommentImageService.lambdaUpdate().eq(BcCommentImage::getRefId, id)
                .eq(BcCommentImage::getRefType, ZijinConstants.ANCHOR_NEWS_IMAGE_TYPE).remove();

        // 删除动态的子回复
        bcAnchorNewsReplyService.lambdaUpdate()
                .eq(BcAnchorNewsReply::getNewsId, id)
                .eq(BcAnchorNewsReply::getIsDel, 0)
                .set(BcAnchorNewsReply::getIsDel, 1)
                .set(BcAnchorNewsReply::getUpdateTime, now)
                .update();

        // 删除子回复关联的图片
        if (!subReplyIds.isEmpty()) {
            bcCommentImageService.lambdaUpdate()
                    .in(BcCommentImage::getRefId, subReplyIds)
                    .eq(BcCommentImage::getRefType, ZijinConstants.ANCHOR_NEWS_REPLY_IMAGE_TYPE)
                    .remove();
        }

        // 更新动态相关的点赞消息状态 TODO 点赞逻辑处理
//        myPraiseService.lambdaUpdate()
//                .eq(MyPraise::getPraiseId, id)
//                .eq(MyPraise::getType, MyPraise.TYPE_ANCHOR_NEW)
//                .set(MyPraise::getIsRead, MyPraise.IS_READ)
//                .update();

        return true;
    }

    private void handleImageUrls(String imgUrl, Long generatedId) {
        if (StrUtil.isNotBlank(imgUrl)) {
            String[] imgUrls = imgUrl.split(",");
            for (String imageUrl : imgUrls) {
                processSingleImage(imageUrl, generatedId);
            }
        }
    }

    private void processSingleImage(String imageUrl, Long generatedId) {
        try {
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);
            int width = image.getWidth();
            int height = image.getHeight();

            BcCommentImage bcCommentImage = new BcCommentImage();
            bcCommentImage.setRefId(generatedId);
            bcCommentImage.setRefType(ZijinConstants.ANCHOR_NEWS_IMAGE_TYPE);
            bcCommentImage.setImgUrl(imageUrl);
            bcCommentImage.setImgWidth((long) width);
            bcCommentImage.setImgHeight((long) height);
            bcCommentImage.setCreateTime(new Date());
            bcCommentImage.setUpdateTime(new Date());

            bcCommentImageService.save(bcCommentImage);
        } catch (IOException e) {
            log.error("读取图片失败:" + e.getMessage());
        }
    }
}
