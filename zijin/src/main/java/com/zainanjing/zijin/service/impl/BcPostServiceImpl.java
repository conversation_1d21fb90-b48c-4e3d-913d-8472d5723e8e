package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.zijin.constant.GdmmCatFlow.GdmmCatFlowType;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.mapper.*;
import com.zainanjing.zijin.service.IBcPostService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 帖子数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class BcPostServiceImpl extends GenericCurdServiceImpl<BcPostMapper, BcPost>
        implements IBcPostService {

    @Resource
    private BcCommentMapper bcCommentMapper;
    @Resource
    private BcZiJinImgMapper bcZiJinImgMapper;
    @Resource
    private BcForumMapper bcForumMapper;
    @Resource
    private BcPostMapper bcPostMapper;
    @Autowired
    private BcVerifyConfigMapper bcVerifyConfigMapper;

    @Override
    public IPage<BcPost> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        IPage<BcPost> bcPostIPage = baseMapper.selectPostPage(pagination, searchable.getFilterCdtns());
        for (BcPost post : bcPostIPage.getRecords()) {
            Long commentCount = bcCommentMapper.selectCount(new QueryWrapper<BcComment>()
                    .eq("post_id", post.getId()).eq("status", 0));
            post.setCommentNum(Math.toIntExact(commentCount));
        }
        return bcPostIPage;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public int recommend(List<Long> ids, Integer type) {
        try {
            for (Long id : ids) {
                if (type == 1) {
                    BcPost post = baseMapper.selectById(id);
                    if (null != post) {
                        if (post.getIsTop() == 1) {
                            //如果是置顶帖子
                            throw new Exception("所选数据包含已置顶帖子,不可再进行推荐操作");
                        }
                    }
                }
                BcPost post = new BcPost();
                post.setId(id);
                post.setIsRec(type);
                baseMapper.updateById(post);
            }
        } catch (Exception e) {
            log.error("帖子推荐报错:" + e.getMessage());
            return 0;
        }
        return 1;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public int saveMore(BcPost bcPost) {
        bcPost.setUid(SecurityUtils.getUserId());
        bcPost.setCreateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        bcPost.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        bcPost.setIsRec(Integer.valueOf(ZijinConstants.CONSTANTS_0));
        baseMapper.insert(bcPost);
        if (null != bcPost.getImageUrls() && !bcPost.getImageUrls().isEmpty()) {
            String[] urls = bcPost.getImageUrls().split(",");
            for (String imgUrl : urls) {
                URL url = new URL(imgUrl);
                BufferedImage image = ImageIO.read(url);
                BcZiJinImg bcZiJinImg = new BcZiJinImg();
                bcZiJinImg.setImgUrl(imgUrl);
                bcZiJinImg.setPostId(bcPost.getId());
                bcZiJinImg.setType(Long.valueOf(ZijinConstants.CONSTANTS_1));
                bcZiJinImg.setCreateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
                bcZiJinImg.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
                bcZiJinImg.setHeight(image.getHeight());
                bcZiJinImg.setWidth(image.getWidth());
                bcZiJinImgMapper.insert(bcZiJinImg);
            }
        }
        return 1;
    }

    @Override
    public BcPost getMoreById(Long id) {
        BcPost bcPost = baseMapper.selectMoreById(id);
        List<BcZiJinImg> imgPosts = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                .eq("post_id", id)
                .eq("type", 1));
        bcPost.setImageUrls(imgPosts.stream()
                .map(BcZiJinImg::getImgUrl)
                .filter(url -> url != null && !url.isEmpty())
                .collect(Collectors.joining(",")));
        List<BcComment> bcComments = bcCommentMapper.selectList(new QueryWrapper<BcComment>()
                .eq("post_id", id)
                .eq("status", 0)
                .eq("level", 1)
                .eq("type", 1));
        if (null != bcComments && !bcComments.isEmpty()) {
            for (BcComment bcComment : bcComments) {
                List<BcZiJinImg> imgList = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                        .eq("post_id", id)
                        .eq("comment_id", bcComment.getId())
                        .eq("type", 2));
                bcComment.setPicUrls(imgList.stream().map(BcZiJinImg::getImgUrl).collect(Collectors.toList()));
                String userName = baseMapper.selectUserNameById(bcComment.getUid());
                bcComment.setUserName(userName);
                //查询二级子回复
                List<BcComment> secondReply = bcCommentMapper.selectList(new QueryWrapper<BcComment>()
                        .eq("parent_id", bcComment.getId())
                        .eq("level", 2)
                        .eq("type", 2)
                        .eq("status", 0));
                if (null != secondReply && !secondReply.isEmpty()) {
                    for (BcComment secondReplyComment : secondReply) {
                        List<BcZiJinImg> replyImages = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                                .eq("post_id", id)
                                .eq("comment_id", secondReplyComment.getId())
                                .eq("type", 2));
                        secondReplyComment.setPicUrls(replyImages.stream().map(BcZiJinImg::getImgUrl).collect(Collectors.toList()));
                        String userNameTwo = baseMapper.selectUserNameById(secondReplyComment.getUid());
                        secondReplyComment.setUserName(userNameTwo);
                    }
                    bcComment.setSecondReplyList(secondReply);
                }
            }
        }
        bcPost.setCommentList(bcComments);
        return bcPost;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public int updateMore(BcPost bcPost) {
        bcPost.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        baseMapper.updateById(bcPost);
        if (null != bcPost.getImageUrls() && !bcPost.getImageUrls().isEmpty()) {
            //先清空再更新进去
            bcZiJinImgMapper.delete(new QueryWrapper<BcZiJinImg>()
                    .eq("post_id", bcPost.getId())
                    .eq("type", 1));
            String[] urls = bcPost.getImageUrls().split(",");
            for (String imgUrl : urls) {
                URL url = new URL(imgUrl);
                BufferedImage image = ImageIO.read(url);
                BcZiJinImg bcZiJinImg = new BcZiJinImg();
                bcZiJinImg.setImgUrl(imgUrl);
                bcZiJinImg.setPostId(bcPost.getId());
                bcZiJinImg.setType(Long.valueOf(ZijinConstants.CONSTANTS_1));
                bcZiJinImg.setCreateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
                bcZiJinImg.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
                bcZiJinImg.setHeight(image.getHeight());
                bcZiJinImg.setWidth(image.getWidth());
                bcZiJinImgMapper.insert(bcZiJinImg);
            }
        }
        return 1;
    }

    @Override
    public List<BcPost> searchIncludeImgAndComment(HashMap<String, Object> filterMap) {
        String uid = StrUtil.toStringOrNull(filterMap.get("uid"));
        String isHost = StrUtil.toStringOrNull(filterMap.get("isHost"));
        List<BcPost> bcPostList = null;
        List<BcPost> bcPostListAdd = null;
        if (ObjUtil.isNotNull(filterMap.get("forumId"))) {
            BcForum bf = bcForumMapper.selectOne(new QueryWrapper<BcForum>().eq("id", filterMap.get("forumId")));
            if (bf != null && bf.getFmId() != null) {
                filterMap.put("fmId", String.valueOf(bf.getFmId()));
            }
        }
        filterMap.remove("uid");
        //屏蔽列表
        Set<String> closeUids = CommonUtil.getCloseUids( uid);
        filterMap.put("closeUids", closeUids);

        if (ObjUtil.isNotNull(filterMap.get("latestId"))) {
            BcPost bcPost = bcPostMapper.selectById(StrUtil.toString(filterMap.get("latestId")));
            if (ZijinConstants.YES.equals(bcPost.getIsTop().toString())) {
                filterMap.put("isTop", ZijinConstants.YES);
                filterMap.put("topLatestId", bcPost.getId().toString());
                bcPostList = bcPostMapper.search(filterMap, 0, Integer.MAX_VALUE);
                if (bcPostList.size() < 10) {
                    filterMap.remove("latestId");
                }
            } else {
                //如果是非置顶帖
                Integer ForumNum = bcPost.getForumNum();
                int forumNumMin = ForumNum - 50;
                filterMap.put("forumNum", ForumNum.toString());
                filterMap.put("forumNumMin", Integer.toString(forumNumMin));
                filterMap.put("isTop", ZijinConstants.NO);
                bcPostList = bcPostMapper.search(filterMap, 0, Integer.MAX_VALUE);
            }
        } else {
            filterMap.put("isTop", ZijinConstants.YES);
            filterMap.put("thisUid", uid);
            bcPostList = bcPostMapper.search(filterMap, 0, Integer.MAX_VALUE);
        }
        //列表不足10条补齐
        if (bcPostList.size() < 10 && !bcPostList.isEmpty()) {
            if (ObjUtil.isNotNull(filterMap.get("latestId"))) {
                Integer ForumNum = bcPostList.get(bcPostList.size() - 1).getForumNum();
                filterMap.put("forumNum", ForumNum.toString());
            }
            filterMap.put("isTop", ZijinConstants.NO);
            filterMap.remove("topLatestId");
            bcPostListAdd = bcPostMapper.search(filterMap, 0, Integer.MAX_VALUE);
            bcPostList.addAll(bcPostListAdd);
        }
        //第一次进来没查到置顶帖
        if (bcPostList.isEmpty() && ObjUtil.isNotNull(filterMap.get("latestId"))) {
            //表示无置顶帖
            filterMap.put("isTop", ZijinConstants.NO);
            filterMap.remove("topLatestId");
            bcPostListAdd = bcPostMapper.search(filterMap, 0, Integer.MAX_VALUE);
            bcPostList.addAll(bcPostListAdd);
        }

        for (BcPost bcPost : bcPostList) {
            String imgUrl = bcPost.getImgUrl();
            Integer avaType = bcPost.getAvatarType();
            imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
            bcPost.setImgUrl(imgUrl);
            bcPost.setCoverThumb(OSSUtil.getImageURL(bcPost.getCoverThumb()));
            bcPost.setProgramLogo(OSSUtil.getImageURL(bcPost.getProgramLogo()));
        }
        if (StrUtil.isNotEmpty(uid) && !uid.equals("false")) {
            checkPostIsReward(bcPostList, uid);
        }
        if (!bcPostList.isEmpty()) {
            //帖子下的图片列表
            List<BcZiJinImg> bcImgList = searchBcImgList(bcPostList);
            addAliImgPath(bcImgList);
            //帖子列表加入图片
            postAddImg(bcPostList, bcImgList);
            //帖子下的评论列表
            List<BcComment> bcCommentList = searchCommentList(bcPostList, uid, isHost, StrUtil.toStringOrNull(filterMap.get("sessionUid")));
            //帖子列表加入评论
            postAddComment(bcPostList, bcCommentList);
        }
        return bcPostList;
    }

    @Override
    public int savePost(BcPost bcPost, String bcImgIds) {
        Long forumNum = CommonUtil.redisCommonAdd( ZijinConstants.BCPOST + "_" + ZijinConstants.FORUMNUM + "_" + bcPost.getForumId());
        bcPost.setCreateTime(CommonUtil.getTimestamp());
        bcPost.setForumNum((int) (long) forumNum);
        bcPostMapper.insert(bcPost);
        if (StrUtil.isNotEmpty(bcImgIds)) {
            int sort = 1;
            String[] pics = bcImgIds.split("_");
            for (String id : pics) {
                BcZiJinImg bcImg = new BcZiJinImg();
                bcImg.setPostId(bcPost.getId());
                bcImg.setId(Long.valueOf(id));
                bcImg.setType(Long.valueOf(ZijinConstants.CONSTANTS_1));
                bcImg.setSort(sort);
                bcZiJinImgMapper.insertOrUpdate(bcImg);
                sort++;
            }
        }
        return Math.toIntExact(bcPost.getId());
    }

    @Override
    public BcPost findIncludeImgs(String postId, String uid) {
        BcPost bcPost = bcPostMapper.findByParams(postId, uid);
        if (null != bcPost) {
            List<BcZiJinImg> bcPostImgList = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                    .eq("post_id", postId)
                    .eq("type", Integer.valueOf(ZijinConstants.CONSTANTS_1))
                    .eq("status", Integer.valueOf(ZijinConstants.CONSTANTS_0)));

            addAliImgPath(bcPostImgList);
            bcPost.setProgramLogo(OSSUtil.getImageURL(bcPost.getProgramLogo()));
            bcPost.setVideoImg(OSSUtil.getImageURL(bcPost.getVideoImg()));
            bcPost.setBcImgList(bcPostImgList);
            //头像
            String imgUrl = bcPost.getImgUrl();
            Integer avaType = bcPost.getAvatarType();
            imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
            bcPost.setImgUrl(imgUrl);

            BcVerifyConfig huDongConfig = bcVerifyConfigMapper.selectOne(
                    new QueryWrapper<BcVerifyConfig>().eq("verify_type", ZijinConstants.POST_TYPE_COMMON));
            BcVerifyConfig topicConfig = bcVerifyConfigMapper.selectOne(
                    new QueryWrapper<BcVerifyConfig>().eq("verify_type", ZijinConstants.CONSTANTS_2));
            //互动
            if (bcPost.getType().equals(Integer.parseInt(ZijinConstants.POST_TYPE_COMMON))) {
                if (huDongConfig.getIsComment().equals(ZijinConstants.YES_COMMENT)) {
                    bcPost.setIsComment(ZijinConstants.NO_COMMENT);
                } else {
                    bcPost.setIsComment(ZijinConstants.YES_COMMENT);
                }
            } else if (bcPost.getType().equals(Integer.parseInt(ZijinConstants.POST_TYPE_TOPIC))) {
                if (topicConfig.getIsComment().equals(ZijinConstants.YES_COMMENT)) {
                    bcPost.setIsComment(ZijinConstants.NO_COMMENT);
                } else {
                    bcPost.setIsComment(ZijinConstants.YES_COMMENT);
                }
            }
        }
        return bcPost;
    }

    @Override
    public List<BcPost> search(HashMap<String, Object> filterMap, Integer start, Integer pageSize) {
        return bcPostMapper.search(filterMap,start,pageSize);
    }

    /**
     * @param bcPostList
     * @param uid
     * @return
     * @throws SQLException List<BcPost>
     * @Title:checkPostIsReward
     * @Description 查看帖子是否打赏
     */
    private List<BcPost> checkPostIsReward(List<BcPost> bcPostList, String uid) {
        for (BcPost bcPost : bcPostList) {
            Map<String, Object> filterMap = new HashMap<String, Object>();
            filterMap.put("uid", uid);
            filterMap.put("code", GdmmCatFlowType.IGiveTicketToOther.getCode());
            filterMap.put("rewardType", ZijinConstants.REWARD_TYPE_BCPOST);
            filterMap.put("resourceId", bcPost.getId());
            Integer rewardNumber = bcPostMapper.countReward(filterMap);
            if (rewardNumber > 0) {
                bcPost.setIsReward(Integer.valueOf(ZijinConstants.CONSTANTS_1));
            } else {
                bcPost.setIsReward(Integer.valueOf(ZijinConstants.CONSTANTS_0));
            }
        }
        return bcPostList;
    }

    private List<BcZiJinImg> searchBcImgList(List<BcPost> bcPostList) {
        List<String> bcPostIdList = new ArrayList<String>();
        for (BcPost bcPost : bcPostList) {
            bcPostIdList.add(bcPost.getId().toString());
        }
        return bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                .eq("type", ZijinConstants.CONSTANTS_1)
                .eq("status", ZijinConstants.CONSTANTS_0)
                .in("post_id", bcPostIdList));
    }

    private List<BcZiJinImg> addAliImgPath(List<BcZiJinImg> bcImgList) {

        for (BcZiJinImg bcImg : bcImgList) {
            bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
        }
        return bcImgList;
    }

    private List<BcPost> postAddImg(List<BcPost> bcPostList,
                                    List<BcZiJinImg> bcImgList) {

        for (BcZiJinImg bcImg : bcImgList) {
            for (BcPost bcPost : bcPostList) {
                if (bcImg.getPostId().equals(bcPost.getId())) {
                    bcPost.getBcImgList().add(bcImg);
                }
            }
        }
        return bcPostList;
    }

    private List<BcComment> searchCommentList(List<BcPost> bcPostList, String uid, String isHost, String
            sessionUid) {
        Map<String, Object> filterMap = new HashMap<String, Object>();
        List<String> bcPostIdList = new ArrayList<String>();
        for (BcPost bcPost : bcPostList) {
            bcPostIdList.add(bcPost.getId().toString());
        }
        filterMap.put("bcPostIdList", bcPostIdList);
        filterMap.put("type", ZijinConstants.POST_TYPE_COMMON);
        filterMap.put("status", ZijinConstants.CONSTANTS_0);
        filterMap.put("orderBy", "idAsc");
        if (isHost.equals(ZijinConstants.NO)) {
            filterMap.put("uid", uid);
        }
        Set<String> closeUids = CommonUtil.getCloseUids( sessionUid);
        filterMap.put("closeUids", closeUids);
        filterMap.put("verifyUid", uid);

        List<BcComment> bcCommentList = bcCommentMapper.search(filterMap);
        if (!bcCommentList.isEmpty()) {
            for (BcComment bcComment : bcCommentList) {
                String imgUrl = bcComment.getImgUrl();
                Integer avaType = bcComment.getAvatarType();
                imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
                bcComment.setImgUrl(imgUrl);
                bcComment.setAudioUrl(OSSUtil.getImageURL(bcComment.getAudioUrl()));
            }
        }

        return bcCommentList;
    }

    private List<BcPost> postAddComment(List<BcPost> bcPostList,
                                        List<BcComment> bcCommentList) {

        for (BcPost bcPost : bcPostList) {
            for (BcComment bcComment : bcCommentList) {
                if (bcComment.getPostId().equals(bcPost.getId())) {
//					//评论数量大于1 或者 如果用户是版主  且  帖子回复为版主可见 且 该回复不是用户所发  不添加评论
                    bcPost.getCommentList().add(bcComment);
                }
            }

        }
        return bcPostList;
    }
}
