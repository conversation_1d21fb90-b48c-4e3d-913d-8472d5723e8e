package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcChoiceAlbum;
import com.zainanjing.zijin.mapper.BcChoiceAlbumMapper;
import com.zainanjing.zijin.service.IBcChoiceAlbumService;
import org.springframework.stereotype.Service;

/**
 * 精选内容专辑Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class BcChoiceAlbumServiceImpl extends GenericCurdServiceImpl<BcChoiceAlbumMapper, BcChoiceAlbum>
        implements IBcChoiceAlbumService {

    @Override
    public IPage<BcChoiceAlbum> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        return baseMapper.selectBcChoiceAlbumPage(pagination, searchable.getFilterCdtns());
    }
}
