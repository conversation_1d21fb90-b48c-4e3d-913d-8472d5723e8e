package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcVerifyConfig;
import com.zainanjing.zijin.mapper.BcVerifyConfigMapper;
import com.zainanjing.zijin.service.IBcAnchorService;
import com.zainanjing.zijin.service.IBcVerifyConfigService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 发言审核配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class BcVerifyConfigServiceImpl extends GenericCurdServiceImpl<BcVerifyConfigMapper, BcVerifyConfig>
        implements IBcVerifyConfigService {

    @Resource
    private IBcAnchorService bcAnchorService;

    @Override
    public boolean updateVerifyConfig(BcVerifyConfig bcVerifyConfig) {
        bcVerifyConfig.setUpdateTime(LocalDateTime.now());
        if (this.updateById(bcVerifyConfig)) {
            if (ObjUtil.equals(bcVerifyConfig.getVerifyType(), 3)) {
                if (bcVerifyConfig.getIsComment() != null) {
                    bcAnchorService.lambdaUpdate().set(BcAnchor::getIsTalk, bcVerifyConfig.getIsComment()).gt(BcAnchor::getId, 0).update();
                }
                if (bcVerifyConfig.getIsAudit() != null) {
                    bcAnchorService.lambdaUpdate().set(BcAnchor::getIsAudit, bcVerifyConfig.getIsAudit()).gt(BcAnchor::getId, 0).update();
                }
            }
            return true;
        }
        return false;
    }
}
