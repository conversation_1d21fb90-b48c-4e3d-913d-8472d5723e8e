package com.zainanjing.zijin.service;

import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcProgramLive;

import java.util.List;

/**
 * 视频节目Service接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IBcProgramLiveService extends GenericCurdService<BcProgramLive> {

    /**
     * 更新同时发送事件
     */
    boolean updateByIdAndEvent(BcProgramLive entity);

    /**
     * 删除同时发送事件
     */
    boolean removeByIdsAndEvent(List<Integer> list);

    /**
     * 保存同时发送事件
     */
    boolean saveAndEvent(BcProgramLive entity);

}
