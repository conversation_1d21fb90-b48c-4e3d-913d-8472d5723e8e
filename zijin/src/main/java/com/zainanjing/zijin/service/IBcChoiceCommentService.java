package com.zainanjing.zijin.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcChoiceComment;

/**
 * 精选内容评论Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IBcChoiceCommentService extends GenericCurdService<BcChoiceComment>
{

    IPage<BcChoiceComment> findAggregateQuery(Searchable searchable);

    BcChoiceComment getMoreById(Long id);

    int batchReview(String ids, Integer status);

    int saveMore(BcChoiceComment bcChoiceComment);

    boolean addChoiceComment(BcChoiceComment bcChoiceComment);
}
