package com.zainanjing.zijin.service;

import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcAnchorRelation;
import com.zainanjing.zijin.domain.BcProgram;

import java.util.List;
import java.util.Map;

/**
 * 节目主播关系Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IBcAnchorRelationService extends GenericCurdService<BcAnchorRelation> {

    /**
     * 获取主播关联的所有节目名称
     */
    Map<Long, List<BcProgram>> getAllBcName(Long anchorId);

}
