package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcProgram;
import com.zainanjing.zijin.domain.BcProgramList;
import com.zainanjing.zijin.mapper.BcProgramListMapper;
import com.zainanjing.zijin.mapper.BcProgramMapper;
import com.zainanjing.zijin.service.IBcProgramListService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 直播节目单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class BcProgramListServiceImpl extends GenericCurdServiceImpl<BcProgramListMapper, BcProgramList>
        implements IBcProgramListService {

    @Resource
    private BcProgramMapper bcProgramMapper;

    @Override
    public IPage<BcProgramList> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        return baseMapper.selectBcProgramListPage(pagination, searchable.getFilterCdtns());
    }

    @Override
    public List<BcProgramList> queryListByFmId(Integer fmId) {
        List<BcProgramList> bcProgramListList = baseMapper.selectList(new QueryWrapper<BcProgramList>()
                .eq("fm_id", fmId)
                .eq("status", 0));
        if (bcProgramListList != null && !bcProgramListList.isEmpty()) {
            BcProgram bcProgram = new BcProgram();
            for (BcProgramList bcProgramList : bcProgramListList) {
                bcProgram = bcProgramMapper.selectById(bcProgramList.getProgramId());
                if (null != bcProgram) {
                    bcProgramList.setProgramName(bcProgram.getName());
                }
            }
        }
        return bcProgramListList;
    }

    @Override
    public List<BcProgramList> selectOptionList(Map<String, Object> paramMap, int start, int maxValue) {
        return baseMapper.selectOptionList(paramMap, start, maxValue);
    }
}
