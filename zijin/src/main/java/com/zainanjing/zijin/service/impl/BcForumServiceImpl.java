package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.domain.BcProgramList;
import com.zainanjing.zijin.dto.SiteCollectDTO;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.mapper.BcForumMapper;
import com.zainanjing.zijin.service.IBcForumService;
import com.zainanjing.zijin.service.IBcProgramListService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 互动区数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class BcForumServiceImpl extends GenericCurdServiceImpl<BcForumMapper, BcForum>
        implements IBcForumService {
    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcProgramListService bcProgramListService;

    @Override
    public IPage<BcForum> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return baseMapper.selectBcForumPage(pagination, searchable.getFilterCdtns());
    }

    @Override
    public IPage<BcForum> selectOptionalList(IPage page, Map<String, Object> paramMap) {
        return baseMapper.selectOptionalList(page,paramMap);
    }

    @Override
    public List<BcAnchor> findAnchorByForumId(Long id) {
        return baseMapper.findAnchorByForumId(id);
    }

    @Override
    public IPage<SiteCollectDTO> searchSiteCollect(IPage page,Map<Object, Object> filterMap) {
        return bcExtMapper.searchSiteCollect(page,filterMap);
    }

    @Override
    public BcForum thisDateHaveLive(long forumId) {
        //获取当前系统时间戳  以及当前星期几
        Calendar calendar = Calendar.getInstance();
        //currDate   应该是19：00这种
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("currDate", sdf.format(date));
        int currWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (currWeek == 0) {
            currWeek = 7;
        }
        paramMap.put("currWeek", currWeek + "");
        paramMap.put("forumId", forumId);
        BcForum bf = null;
        bf = baseMapper.selectById(forumId);
        if (bf != null) {
            paramMap.put("fmId", bf.getFmId());
            paramMap.put("id", bf.getProgramId());
            List<BcProgramList> bcProgramList = bcProgramListService.selectOptionList(paramMap,0,Integer.MAX_VALUE);
            if (bcProgramList != null && !bcProgramList.isEmpty()) {
                BcProgramList bpl = bcProgramList.get(0);
                bf.setIsLive(1);
                bf.setStartTime(bpl.getStartTime());
                bf.setEndTime(bpl.getEndTime());
                return bf;
            }
        }
        return bf;
    }
}
