package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcAnchorNews;
import com.zainanjing.zijin.domain.BcAnchorNewsReply;

/**
 * 主播动态回复Service接口
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface IBcAnchorNewsReplyService extends GenericCurdService<BcAnchorNewsReply> {

    IPage<BcAnchorNewsReply> findAggregateQuery(Searchable searchable);

    BcAnchorNewsReply getMoreById(Long id);

    int batchReview(String ids, Integer status);

    /**
     * 评论主播动态
     */
    BcAnchorNewsReply commentAnchorNews(BcAnchorNewsReply bcAnchorNewsReply);

    /**
     * 删除帖子
     */
    boolean deleteNewsReply(Long id);


    int backSave(BcAnchorNewsReply bcAnchorNewsReply);
}
