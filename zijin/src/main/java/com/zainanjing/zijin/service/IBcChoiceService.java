package com.zainanjing.zijin.service;

import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcChoice;
import com.zainanjing.zijin.dto.BcChoiceRankDTO;

import java.util.List;
import java.util.Map;

/**
 * 精选内容分集Service接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IBcChoiceService extends GenericCurdService<BcChoice> {
    Map<Long, Long> getChoiceNumsByAlbumIds(List<Long> albumId);

    List<BcChoiceRankDTO> getRankList(Integer type, int startInt, Integer pageSize);
}
