package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcChoice;
import com.zainanjing.zijin.domain.BcChoiceAlbum;
import com.zainanjing.zijin.dto.BcChoiceRankDTO;
import com.zainanjing.zijin.mapper.BcChoiceAlbumMapper;
import com.zainanjing.zijin.mapper.BcChoiceMapper;
import com.zainanjing.zijin.service.IBcChoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 精选内容分集Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class BcChoiceServiceImpl extends GenericCurdServiceImpl<BcChoiceMapper, BcChoice>
        implements IBcChoiceService {

    @Autowired
    private BcChoiceAlbumMapper albumMapper;

    /**
     * 通过多个专辑id获取分集数量
     */
    public Map<Long, Long> getChoiceNumsByAlbumIds(List<Long> albumId) {
        QueryWrapper<BcChoice> query = Wrappers.query();
        query.select("album_id", "count(*) nums").eq("status", 1).eq("is_show", 1).in("album_id", albumId).groupBy("album_id");
        List<Map<String, Object>> maps = this.listMaps(query);
        return maps.stream().collect(Collectors.toMap(map -> (Long) map.get("album_id"), map -> (Long) map.get("nums")));
    }

    @Override
    public List<BcChoiceRankDTO> getRankList(Integer type, int startInt, Integer pageSize) {

        //获取“当前月份第一天 00:00:00”所对应的时间戳
        long unixMonthTime = LocalDate.now()
                .withDayOfMonth(1)
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant()
                .getEpochSecond();

        List<BcChoiceRankDTO> rankDTOList = baseMapper.selectRankList(type, unixMonthTime, startInt, pageSize);
        if (CollectionUtils.isNotEmpty(rankDTOList)) {
            for (BcChoiceRankDTO rankDTO : rankDTOList) {
                BcChoiceAlbum bcChoiceAlbum = albumMapper.selectById(rankDTO.getAlbum_id());
                if (null != bcChoiceAlbum) {
                    rankDTO.setAlbum_name(bcChoiceAlbum.getName());
                }
            }
        }
        return rankDTOList;
    }

}
