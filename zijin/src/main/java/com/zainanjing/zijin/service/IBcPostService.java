package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcPost;

import java.util.HashMap;
import java.util.List;

/**
 * 帖子数据Service接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IBcPostService extends GenericCurdService<BcPost> {

    IPage<BcPost> findAggregateQuery(Searchable searchable);

    int recommend(List<Long> ids, Integer type);

    int saveMore(BcPost bcPost);

    BcPost getMoreById(Long id);

    int updateMore(BcPost bcPost);

    List<BcPost> searchIncludeImgAndComment(HashMap<String, Object> filterMap);

    int savePost(BcPost bcPost, String bcImgIds);

    BcPost findIncludeImgs(String postId, String uid);

    List<BcPost> search(HashMap<String, Object> filterMap, Integer start, Integer pageSize);
}
