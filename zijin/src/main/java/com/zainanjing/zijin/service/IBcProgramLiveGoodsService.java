package com.zainanjing.zijin.service;

import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcProgramLiveGoods;

import java.util.List;

/**
 * 直播爆品管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IBcProgramLiveGoodsService extends GenericCurdService<BcProgramLiveGoods> {

    /**
     * 修改讲解
     */
    boolean updateExplain(BcProgramLiveGoods bcProgramLiveGoods);

    List<BcProgramLiveGoods> searchGoodsList(String id, String pageNo, String pageSize);
}
