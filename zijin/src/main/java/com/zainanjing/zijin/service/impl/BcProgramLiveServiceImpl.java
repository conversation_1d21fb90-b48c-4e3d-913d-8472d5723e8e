package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.utils.DateUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.http.HttpUtil;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.BcProgramLive;
import com.zainanjing.zijin.event.BcProgramLiveEvent;
import com.zainanjing.zijin.mapper.BcProgramLiveMapper;
import com.zainanjing.zijin.service.IBcProgramLiveService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 视频节目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class BcProgramLiveServiceImpl extends GenericCurdServiceImpl<BcProgramLiveMapper, BcProgramLive>
        implements IBcProgramLiveService {

    private static final String VMS_CREATE_PROGRAM_LIVE = "https://vms.zainanjing365.com/tcv/admin/tcv/app/genCreate";

    @Value("${city-enums.CITY_ADDRESS_NAME}")
    private String cityName;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public boolean updateByIdAndEvent(BcProgramLive entity) {
        entity.setUpdateTime(DateUtil.currentSeconds());
        if (super.updateById(entity)) {
            applicationEventPublisher.publishEvent(new BcProgramLiveEvent(this, entity, EventAction.builder().type(BusinessType.UPDATE).build()));
            return true;
        }
        return false;
    }

    @Override
    public boolean saveAndEvent(BcProgramLive entity) {
        if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(entity.getVideoType()))
                && StrUtil.isEmpty(entity.getTecentVideoUrl())) {

            Map<String, Object> request = new HashMap<>();
            request.put("liveName", entity.getName());
            request.put("cityName", cityName);
            request.put("startTime", entity.getStartTime());
            request.put("endTime", entity.getEndTime());
            request.put("liveCoverImg", OSSUtil.getImageURL(entity.getBanner()));

            JsonNode jsonNode = HttpUtil.postForm(VMS_CREATE_PROGRAM_LIVE, request);

            if (jsonNode.has("data")) {
                JsonNode data = jsonNode.get("data");
                if (data == null) {
                    log.error("create programLive--Response data is null");
                    return false;
                }
                // 提取streamName和urlForApp字段
                if (data.has("streamName") && data.has("urlForApp") && data.has("playAddress") && data.has("tcvAddress")) {
                    String streamName = data.get("streamName").asText();
                    String hlsUrl = data.get("urlForApp").asText();
                    String playAddress = data.get("playAddress").asText();
                    String pushAddress = data.get("tcvAddress").asText();
                    //截取rtmp地址
                    if (null != playAddress) {
                        entity.setTecentVideoRtmpUrl(playAddress.replace("https", "rtmp"));
                    }
                    entity.setStreamId(streamName);
                    entity.setTecentVideoUrl(hlsUrl);
                    entity.setPushUrl(pushAddress);
                } else {
                    log.error("create programLive--Response streamName,urlForApp is missing required fields");
                    return false;
                }
            } else {
                log.error("create programLive--Response data is missing required fields");
                return false;
            }
            //桂林 新增拉流转推生成回放地址,videoType为4
        } else if (ZijinConstants.CONSTANTS_4.equals(String.valueOf(entity.getVideoType()))) {
            //拉流转推的地址不能为空
            if (StrUtil.isEmpty(entity.getTecentVideoUrl())) {
                log.error("create programLive--TecentVideoUrl is null");
                return false;
            }
            // 按照'/'分割字符串
            String[] parts = entity.getTecentVideoUrl().split("/");
            // 取最后一个部分
            String lastPart = parts[parts.length - 1];
            // 按照'.'分割最后一个部分
            String[] lastPartSplit = lastPart.split("\\.");
            entity.setStreamId(lastPartSplit[0]);
        }
        entity.setCreateTime(DateUtil.currentSeconds());
        if (super.save(entity)) {
            applicationEventPublisher.publishEvent(new BcProgramLiveEvent(this, entity, EventAction.builder().type(BusinessType.INSERT).build()));
            return true;
        }
        return false;
    }

    public boolean removeByIdsAndEvent(List<Integer> ids) {
        if (super.removeByIds(ids)) {
            applicationEventPublisher.publishEvent(new BcProgramLiveEvent(this, ids, EventAction.builder().type(BusinessType.DELETE).build()));
            return true;
        }
        return false;
    }


}
