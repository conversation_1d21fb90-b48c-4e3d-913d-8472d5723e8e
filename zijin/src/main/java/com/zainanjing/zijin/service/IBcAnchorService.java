package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcCommonModel;

/**
 * 主播Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IBcAnchorService extends GenericCurdService<BcAnchor> {
    /**
     * 后台主播列表接口
     */
    IPage<BcAnchor> findAggregateQuery(Searchable searchable);

    /**
     * 根据手机号查询gdmm_users表
     */
    BcCommonModel queryByPhoneNumber(String phoneNumber);

    /**
     * 关联保存
     *
     * @return
     */
    int insertSelective(BcAnchor bcAnchor);

    /**
     * 关联查询
     */
    BcAnchor getSelectiveAnchor(Long id);

    /**
     * 关联保存
     */
    int updateSelective(BcAnchor bcAnchor);
}
