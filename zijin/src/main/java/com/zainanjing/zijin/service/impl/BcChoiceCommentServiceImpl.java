package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.utils.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.BcAnchorNewsReply;
import com.zainanjing.zijin.domain.BcChoiceComment;
import com.zainanjing.zijin.domain.BcChoiceCommentImg;
import com.zainanjing.zijin.domain.BcVerifyConfig;
import com.zainanjing.zijin.mapper.BcChoiceCommentImgMapper;
import com.zainanjing.zijin.mapper.BcChoiceCommentMapper;
import com.zainanjing.zijin.mapper.BcVerifyConfigMapper;
import com.zainanjing.zijin.service.IBcChoiceCommentService;
import com.zainanjing.zijin.service.IBcVerifyConfigService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 精选内容评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class BcChoiceCommentServiceImpl extends GenericCurdServiceImpl<BcChoiceCommentMapper, BcChoiceComment>
        implements IBcChoiceCommentService {

    @Resource
    private BcChoiceCommentImgMapper bcChoiceCommentImgMapper;
    @Resource
    private BcVerifyConfigMapper bcVerifyConfigMapper;

    @Override
    public IPage<BcChoiceComment> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        return baseMapper.selectChoiceCommentPage(pagination, searchable.getFilterCdtns());
    }

    @Override
    public BcChoiceComment getMoreById(Long id) {
        BcChoiceComment bcChoiceComment = baseMapper.getMoreById(id);
        Map<String, Object> queryMap = new HashMap<String, Object>();
        queryMap.put("comment_id", bcChoiceComment.getId());
        List<BcChoiceCommentImg> bcChoiceCommentImgList = bcChoiceCommentImgMapper.selectByMap(queryMap);
        String url = bcChoiceCommentImgList.stream()
                .map(BcChoiceCommentImg::getImgUrl)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        bcChoiceComment.setImgUrls(url);
        //查询该条评论下的回复
        Map<String, Object> queryMoreMap = new HashMap<String, Object>();
        queryMoreMap.put("parent_id", bcChoiceComment.getId());
        //审核通过了
        queryMoreMap.put("status", ZijinConstants.CONSTANTS_2);
        List<BcChoiceComment> bcChoiceCommentList = baseMapper.selectByMap(queryMoreMap);
        if (bcChoiceCommentList != null && !bcChoiceCommentList.isEmpty()) {
            for (BcChoiceComment bc : bcChoiceCommentList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("comment_id", bc.getId());
                List<BcChoiceCommentImg> picList = bcChoiceCommentImgMapper.selectByMap(map);
                bc.setPicUrls(picList.stream().map(BcChoiceCommentImg::getImgUrl).collect(Collectors.toList()));

                String userName = baseMapper.selectUserNameById(bc.getUserId());
                bc.setUserName(userName);
            }
        }
        bcChoiceComment.setChoiceComments(bcChoiceCommentList);
        return bcChoiceComment;
    }

    @Override
    public int batchReview(String ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return 0; // 如果ids为空，直接返回
        }
        List<String> idList = Arrays.asList(ids.split(","));
        // 遍历每个id，更新审核状态
        idList.forEach(id -> {
            BcChoiceComment choiceComment = baseMapper.selectById(Long.valueOf(id));
            if (choiceComment != null) {
                choiceComment.setStatus(status);
                choiceComment.setUpdateTime(new Date());
                baseMapper.updateById(choiceComment);
            }
        });
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveMore(BcChoiceComment bcChoiceComment) {
        //判断一下全局的审核开关
        LambdaQueryWrapper<BcVerifyConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BcVerifyConfig::getVerifyType, ZijinConstants.JINGXUAN_COMMENT_IS_AUDIT_TYPE);
        BcVerifyConfig bcVerifyConfig = bcVerifyConfigMapper.selectOne(wrapper);
        if (null != bcVerifyConfig) {
            //评论关闭直接返回
            if (ZijinConstants.CONSTANTS_0.equals(String.valueOf(bcVerifyConfig.getIsComment()))) {
                return 0;
            } else {
                //评论开启，且需要审核
                if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(bcVerifyConfig.getIsAudit()))) {
                    bcChoiceComment.setStatus(Integer.valueOf(ZijinConstants.CONSTANTS_1));
                } else {
                    //不需要审核
                    bcChoiceComment.setStatus(Integer.valueOf(ZijinConstants.CONSTANTS_2));
                }
            }
            bcChoiceComment.setCreateTime(new Date());
            bcChoiceComment.setUpdateTime(new Date());
            bcChoiceComment.setUserId(SecurityUtils.getUserId());
            baseMapper.insert(bcChoiceComment);
            Long generatedId = bcChoiceComment.getId();
            String imgUrls = bcChoiceComment.getImgUrls();
            if (StrUtil.isNotEmpty(imgUrls)) {
                String[] imgUrlList = imgUrls.split(",");
                for (String imgUrl : imgUrlList) {
                    BcChoiceCommentImg bcChoiceCommentImg = new BcChoiceCommentImg();
                    URL url = null;
                    try {
                        url = new URL(imgUrl);
                        BufferedImage image = ImageIO.read(url);
                        bcChoiceCommentImg.setWidth(image.getWidth());
                        bcChoiceCommentImg.setHeight(image.getHeight());
                    } catch (IOException e) {
                        log.error("读取精选内容评论图片失败:" + e.getMessage());
                    }
                    bcChoiceCommentImg.setImgUrl(imgUrl);
                    bcChoiceCommentImg.setCommentId(generatedId);
                    bcChoiceCommentImg.setCreateTime(new Date());
                    bcChoiceCommentImg.setUpdateTime(new Date());
                    bcChoiceCommentImgMapper.insert(bcChoiceCommentImg);
                }
            }
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addChoiceComment(BcChoiceComment bcChoiceComment) {
        Date now = new Date();
        bcChoiceComment.setCreateTime(now);
        if (this.save(bcChoiceComment)) {
            if (StrUtil.isNotEmpty(bcChoiceComment.getImgUrls())) {
                String[] imgUrls = bcChoiceComment.getImgUrls().split(",");
                for (String imgUrl : imgUrls) {
                    BcChoiceCommentImg bcChoiceCommentImg = new BcChoiceCommentImg();
                    bcChoiceCommentImg.setCommentId(bcChoiceComment.getId());
                    bcChoiceCommentImg.setImgUrl(imgUrl);
                    bcChoiceCommentImg.setCreateTime(now);
                    bcChoiceCommentImg.setUpdateTime(now);
                    bcChoiceCommentImgMapper.insert(bcChoiceCommentImg);
                }
            }
            return true;
        }
        return false;
    }
}
