package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcGoods;
import com.zainanjing.zijin.dto.BcGoodsDTO;

import java.util.Map;

/**
 * 爆品管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IBcGoodsService extends GenericCurdService<BcGoods> {

    /**
     * 分页关联查询
     */
    IPage<BcGoodsDTO> findBcGoodsList(Searchable searchable);

    IPage<BcGoodsDTO> findBcGoodsList(IPage page, Map<String, Object> params);

}
