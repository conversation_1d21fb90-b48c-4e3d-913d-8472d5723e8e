package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcProgramLiveGoods;
import com.zainanjing.zijin.mapper.BcProgramLiveGoodsMapper;
import com.zainanjing.zijin.service.IBcProgramLiveGoodsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 直播爆品管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class BcProgramLiveGoodsServiceImpl extends GenericCurdServiceImpl<BcProgramLiveGoodsMapper, BcProgramLiveGoods>
        implements IBcProgramLiveGoodsService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExplain(BcProgramLiveGoods bcProgramLiveGoods) {
        BcProgramLiveGoods old = this.getById(bcProgramLiveGoods.getId());
        if (old == null) {
            return false;
        }
        if (ObjUtil.equals(bcProgramLiveGoods.getIsExplain(), 1)) {
            //设置其他值是0
            this.lambdaUpdate().set(BcProgramLiveGoods::getIsExplain, 0)
                    .eq(BcProgramLiveGoods::getProgramLiveId, old.getProgramLiveId()).update();
        }
        if (this.lambdaUpdate().set(BcProgramLiveGoods::getIsExplain, bcProgramLiveGoods.getIsExplain())
                .eq(BcProgramLiveGoods::getId, bcProgramLiveGoods.getId()).update()) {
            return true;
        }
        return false;
    }

    @Override
    public List<BcProgramLiveGoods> searchGoodsList(String id, String pageNo, String pageSize) {
        int pageSizeInt = Integer.parseInt(pageSize);
        int startInt = (Integer.parseInt(pageNo) - 1) * pageSizeInt;
        return baseMapper.searchGoodsList(id, startInt, pageSizeInt);
    }
}
