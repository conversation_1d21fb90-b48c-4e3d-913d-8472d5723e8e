package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcProgramTvLive;
import com.zainanjing.zijin.mapper.BcProgramTvLiveMapper;
import com.zainanjing.zijin.service.IBcProgramTvLiveService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 电视视频节目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class BcProgramTvLiveServiceImpl extends GenericCurdServiceImpl<BcProgramTvLiveMapper, BcProgramTvLive>
        implements IBcProgramTvLiveService {

    @Resource
    private BcProgramTvLiveMapper bcProgramTvLiveMapper;

    @Override
    public BcProgramTvLive findById(String id) {
        return bcProgramTvLiveMapper.findById(id);
    }
}
