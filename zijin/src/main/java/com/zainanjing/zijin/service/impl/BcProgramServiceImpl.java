package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcProgram;
import com.zainanjing.zijin.mapper.BcProgramListMapper;
import com.zainanjing.zijin.mapper.BcProgramMapper;
import com.zainanjing.zijin.service.IBcProgramService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 节目名称Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class BcProgramServiceImpl extends GenericCurdServiceImpl<BcProgramMapper, BcProgram>
        implements IBcProgramService {

    @Resource
    private BcProgramListMapper bcProgramListMapper;

    @Override
    public List<BcProgram> queryByFmId(Integer fmId) {
        return Optional.ofNullable(fmId)
                .map(bcProgramListMapper::selectByFmId)
                .filter(programIds -> !CollectionUtils.isEmpty(programIds))
                .map(baseMapper::selectByIds)
                .orElse(null);

    }
}
