package com.zainanjing.zijin.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcPost;
import com.zainanjing.zijin.domain.BcTopic;

/**
 * 广播话题Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IBcTopicService extends GenericCurdService<BcTopic>
{

    IPage<BcTopic> findAggregateQuery(Searchable searchable);

    int saveMore(BcTopic bcTopic);

    void addTopicView(BcPost bcPost);
}
