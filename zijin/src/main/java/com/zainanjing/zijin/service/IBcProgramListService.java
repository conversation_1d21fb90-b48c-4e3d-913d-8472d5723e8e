package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcProgramList;

import java.util.List;
import java.util.Map;

/**
 * 直播节目单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IBcProgramListService extends GenericCurdService<BcProgramList> {

    /**
     * 后台节目单列表聚合查询
     */
    IPage<BcProgramList> findAggregateQuery(Searchable searchable);

    List<BcProgramList> queryListByFmId(Integer fmId);

    List<BcProgramList> selectOptionList(Map<String, Object> paramMap, int start, int maxValue);
}
