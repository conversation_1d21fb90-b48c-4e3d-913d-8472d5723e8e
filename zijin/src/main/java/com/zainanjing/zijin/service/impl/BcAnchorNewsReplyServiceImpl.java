package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.dto.GdmmUsersDTO;
import com.zainanjing.zijin.mapper.BcAnchorNewsMapper;
import com.zainanjing.zijin.domain.BcAnchorNewsReply;
import com.zainanjing.zijin.domain.BcCommentImage;
import com.zainanjing.zijin.domain.BcVerifyConfig;
import com.zainanjing.zijin.mapper.BcAnchorNewsReplyMapper;
import com.zainanjing.zijin.mapper.BcCommentImageMapper;
import com.zainanjing.zijin.mapper.BcExtMapper;
import com.zainanjing.zijin.service.*;
import com.zainanjing.zijin.mapper.BcVerifyConfigMapper;
import com.zainanjing.zijin.service.IBcAnchorNewsReplyService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 主播动态回复Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class BcAnchorNewsReplyServiceImpl extends GenericCurdServiceImpl<BcAnchorNewsReplyMapper, BcAnchorNewsReply>
        implements IBcAnchorNewsReplyService {

    @Resource
    private BcCommentImageMapper bcCommentImageMapper;
    @Resource
    private IBcVerifyConfigService verifyConfigService;

    @Resource
    private BcAnchorNewsMapper bcAnchorNewsMapper;

    @Resource
    private IBcAnchorService anchorService;

    @Resource
    private BcExtMapper bcExtMapper;

    @Resource
    private IBcCommentImageService bcCommentImageService;
    @Resource
    private BcVerifyConfigMapper bcVerifyConfigMapper;

    @Override
    public IPage<BcAnchorNewsReply> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        return baseMapper.selectAnchorNewsReplyPage(pagination, searchable.getFilterCdtns());
    }

    @Override
    public BcAnchorNewsReply getMoreById(Long id) {
        BcAnchorNewsReply bcAnchorNewsReply = baseMapper.selectMoreById(id);
        //查询评论图片
        Map<String, Object> map = new HashMap<>();
        map.put("ref_id", id);
        map.put("ref_type", ZijinConstants.ANCHOR_NEWS_REPLY_IMAGE_TYPE);
        List<BcCommentImage> imageList = bcCommentImageMapper.selectByMap(map);
        if (imageList != null && !imageList.isEmpty()) {
            // 使用Stream API拼接imgUrl字段
            String imgUrls = imageList.stream()
                    .map(BcCommentImage::getImgUrl) // 提取imgUrl字段
                    .filter(url -> url != null && !url.isEmpty()) // 过滤掉空或空字符串
                    .collect(Collectors.joining(",")); // 使用逗号拼接
            bcAnchorNewsReply.setImgUrls(imgUrls);
        }
        return bcAnchorNewsReply;
    }

    @Override
    public int batchReview(String ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return 0; // 如果ids为空，直接返回
        }
        List<String> idList = Arrays.asList(ids.split(","));
        // 遍历每个id，更新审核状态
        idList.forEach(id -> {
            BcAnchorNewsReply reply = baseMapper.selectById(Long.valueOf(id));
            if (reply != null) {
                reply.setIsAudit(status);
                reply.setAuditTime(new Date());
                baseMapper.updateById(reply);
            }
        });
        return 1;
    }

    @Override
    public int backSave(BcAnchorNewsReply bcAnchorNewsReply) {
        //判断一下全局的审核开关
        LambdaQueryWrapper<BcVerifyConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BcVerifyConfig::getVerifyType, ZijinConstants.ANCHOR_NEWS_REPLY_IS_AUDIT_TYPE);
        BcVerifyConfig bcVerifyConfig = bcVerifyConfigMapper.selectOne(wrapper);
        if (null != bcVerifyConfig) {
            //评论关闭直接返回
            if (ZijinConstants.CONSTANTS_0.equals(String.valueOf(bcVerifyConfig.getIsComment()))) {
                return 0;
            } else {
                BcAnchorNewsReply reply = new BcAnchorNewsReply();
                //评论开启，且需要审核
                if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(bcVerifyConfig.getIsAudit()))) {
                    reply.setIsAudit(Integer.valueOf(ZijinConstants.CONSTANTS_0));
                } else {
                    //不需要审核
                    reply.setIsAudit(Integer.valueOf(ZijinConstants.CONSTANTS_1));
                }
                reply.setNewsId(bcAnchorNewsReply.getId());
                reply.setCreateTime(new Date());
                reply.setUpdateTime(new Date());
                reply.setReplyTime(new Date());
                reply.setUserId(SecurityUtils.getUserId());
                reply.setFromUserName(SecurityUtils.getUsername());
                reply.setToUserId(Long.valueOf(bcAnchorNewsReply.getAnchorId()));
                String toUserName = baseMapper.selectNameByUserId(bcAnchorNewsReply.getAnchorId());
                reply.setToUserName(toUserName);
                reply.setReplyContent(bcAnchorNewsReply.getReplyContent());
                baseMapper.insert(reply);
                Long generatedId = reply.getId();
                if (null != bcAnchorNewsReply.getImgUrls()) {
                    List<String> imgUrls = Arrays.stream(bcAnchorNewsReply.getImgUrls().split(",")).toList();
                    for (String imgUrl : imgUrls) {
                        BcCommentImage bcCommentImage = new BcCommentImage();
                        URL url = null;
                        try {
                            url = new URL(imgUrl);
                            BufferedImage image = ImageIO.read(url);
                            bcCommentImage.setRefId(generatedId);
                            bcCommentImage.setRefType(ZijinConstants.ANCHOR_NEWS_REPLY_IMAGE_TYPE);
                            bcCommentImage.setImgWidth((long) image.getWidth());
                            bcCommentImage.setImgHeight((long) image.getHeight());
                            bcCommentImage.setImgUrl(imgUrl);
                            bcCommentImage.setCreateTime(new Date());
                            bcCommentImage.setUpdateTime(new Date());
                            bcCommentImageMapper.insert(bcCommentImage);
                        } catch (IOException e) {
                            log.error("获取图片信息失败:" + imgUrl);
                        }
                    }
                }
            }
        }
        return 1;
    }

    /**
     * 评论主播动态
     */
    @Override
    public BcAnchorNewsReply commentAnchorNews(BcAnchorNewsReply bcAnchorNewsReply) {
        Optional<BcVerifyConfig> bcVerifyConfigOpt = verifyConfigService.lambdaQuery().eq(BcVerifyConfig::getVerifyType, 3).oneOpt();

        if (bcVerifyConfigOpt.isPresent()) {
            BcVerifyConfig bcVerifyConfig = bcVerifyConfigOpt.get();
            if (bcVerifyConfig.getIsComment() == 0) {
                throw new ServiceException("评论功能已关闭");
            }
        }
        //TODO: 实现敏感词过滤 IGdmmSpecialWordService 底层依赖处理， 重新考虑是还是这个表的敏感词，还是用订阅号自己的

        //业务逻辑处理
        // 获取当前时间
        Date now = new Date();

        // 获取评论审核开关配置
        BcVerifyConfig verifyConfig = bcVerifyConfigOpt.get();

        // 查询动态信息
        BcAnchorNews dynamicInfo = bcAnchorNewsMapper.selectById(bcAnchorNewsReply.getNewsId());
        if (dynamicInfo == null || dynamicInfo.getIsDel() == 1) {
            throw new ServiceException("动态不存在");
        }

        if (ObjUtil.isNull(bcAnchorNewsReply.getToUserId())) {
            bcAnchorNewsReply.setToUserId(dynamicInfo.getAnchorId());
        }

        // 查询主播信息
        BcAnchor anchorInfo = anchorService.lambdaQuery().eq(BcAnchor::getUserId, dynamicInfo.getAnchorId()).eq(BcAnchor::getIsDel, 0).one();
        if (anchorInfo == null) {
            throw new ServiceException("主播不存在");
        }

        // 判断评论审核状态
        Integer audit = 1; // 默认通过审核
        if (verifyConfig.getIsAudit() == 1 && anchorInfo.getIsAudit() == 1) {
            // 全局开启且主播开启，需要审核
            audit = 0;
        } else if (verifyConfig.getIsAudit() == 1 && anchorInfo.getIsAudit() == 0) {
            // 全局开启主播关闭，需要审核
            audit = 0;
        } else if (verifyConfig.getIsAudit() == 0 && anchorInfo.getIsAudit() == 0) {
            // 全局关闭主播关闭，不需审核
            audit = 1;
        } else if (verifyConfig.getIsAudit() == 0 && anchorInfo.getIsAudit() == 1) {
            // 全局关闭主播开启，需要审核
            audit = 0;
        }

        // 判断回复类型
        Integer replyType = ObjUtil.isNotNull(bcAnchorNewsReply.getParentEntityId()) && bcAnchorNewsReply.getParentEntityId() > 0 ? 2 : 1;

        // 获取评论用户信息
        List<Long> queryUids = bcAnchorNewsReply.getToUserId() != null ? List.of(bcAnchorNewsReply.getUserId(), bcAnchorNewsReply.getToUserId()) : List.of(bcAnchorNewsReply.getUserId());
        List<GdmmUsersDTO> gdmmUsersDTOS = bcExtMapper.selectUsers(Map.of("uids", queryUids));
        String fromUserName = gdmmUsersDTOS.stream().filter(x -> x.getUserId().equals(bcAnchorNewsReply.getUserId())).findFirst().get().getUserName();
        String toUserName = gdmmUsersDTOS.stream().filter(x -> x.getUserId().equals(bcAnchorNewsReply.getToUserId())).findFirst().get().getUserName();

        // 判断是否是主播
        List<BcAnchor> isBcAnchors = anchorService.lambdaQuery().in(BcAnchor::getUserId, queryUids).eq(BcAnchor::getIsDel, 0).list();

        for (BcAnchor bcAnchor : isBcAnchors) {
            if (bcAnchor.getUserId().equals(bcAnchorNewsReply.getUserId())) {
                fromUserName = bcAnchor.getAnchorName();
            }
            if (bcAnchor.getUserId().equals(bcAnchorNewsReply.getToUserId())) {
                toUserName = bcAnchor.getAnchorName();
            }
        }

        bcAnchorNewsReply.setFromUserName(fromUserName);
        bcAnchorNewsReply.setToUserName(toUserName);
        bcAnchorNewsReply.setIsAudit(audit);
        bcAnchorNewsReply.setReplyType(replyType);
        bcAnchorNewsReply.setIsDel(0);
        bcAnchorNewsReply.setIsShow(1);

        // 保存评论
        boolean success = this.save(bcAnchorNewsReply);

        if (success && StrUtil.isNotBlank(bcAnchorNewsReply.getImgUrls())) {
            Arrays.stream(bcAnchorNewsReply.getImgUrls().split(",")).forEach(x -> {
                BcCommentImage image = new BcCommentImage();
                image.setRefId(bcAnchorNewsReply.getId());
                image.setRefType("51");
                image.setImgUrl(x);
                image.setCreateTime(now);
                image.setUpdateTime(now);
                long[] imageWidthAndHeight = OSSUtil.getImageWidthAndHeight(x);
                image.setImgWidth(imageWidthAndHeight[0]);
                image.setImgHeight(imageWidthAndHeight[1]);
                bcCommentImageService.save(image);
            });
        }

        // 发送消息通知 TODO 管理端这个逻辑也漏了 存Redis 消息的逻辑
//        if (toUserId != null) {
//            bcMessageService.sendReplyMessage(uid, toUserId, reply.getId());
//        }

        return bcAnchorNewsReply;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNewsReply(Long id) {
        Date now = new Date();
        // 查询子回复
        List<Long> subReplyIds = this.lambdaQuery()
                .eq(BcAnchorNewsReply::getParentEntityId, id)
                .eq(BcAnchorNewsReply::getIsDel, 0)
                .select(BcAnchorNewsReply::getId)
                .list().stream().map(BcAnchorNewsReply::getId).collect(Collectors.toList());
        subReplyIds.add(id);
        if (this.lambdaUpdate().in(BcAnchorNewsReply::getId, subReplyIds).set(BcAnchorNewsReply::getIsDel, 1).set(BcAnchorNewsReply::getUpdateTime, now).update()) {
            bcCommentImageService.lambdaUpdate().in(BcCommentImage::getRefId, subReplyIds).eq(BcCommentImage::getRefType, "51").remove();
            return true;
        }
        return false;
    }
}

