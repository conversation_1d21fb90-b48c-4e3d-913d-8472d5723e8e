package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcChoiceAlbum;

/**
 * 精选内容专辑Service接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IBcChoiceAlbumService extends GenericCurdService<BcChoiceAlbum> {

    IPage<BcChoiceAlbum> findAggregateQuery(Searchable searchable);
}
