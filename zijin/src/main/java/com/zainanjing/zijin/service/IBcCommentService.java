package com.zainanjing.zijin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcComment;
import com.zainanjing.zijin.domain.BcPost;
import com.zainanjing.zijin.dto.EcsAvatar;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评论Service接口
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
public interface IBcCommentService extends GenericCurdService<BcComment> {

    IPage<BcComment> findAggregateQuery(Searchable searchable);

    BcComment getMoreById(Long id);

    int saveMore(BcComment bcComment);

    List<BcComment> search(Map<String, Object> filterMap);

    List<BcComment> searchByConditions(HashMap<String, Object> filterMap, String type, String commentId, Integer currentPage, Integer pageSize);

    Map<String, String> findAudioUrlByBcImgId(String bcImgIds);

    void saveOnAudit(BcComment bcComment, BcPost bcPost);

    Long saveCommentAndImgs(BcComment bcComment, String bcImgIds);

    void searchImgUrlsAndUserHead(Long id, Long uid, BcComment bcComment);


    void updateHasNewComment(String type, String postId, String commentId, Integer hasNewComment);

    Integer selectMedalLevelByUid(Long uid);

    String selectUserNameByUid(Long uid);

    EcsAvatar findEcsAvatarByUid(Long uid);
}
