package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcFm;
import com.zainanjing.zijin.mapper.BcFmMapper;
import com.zainanjing.zijin.service.IBcFmService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 频率信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class BcFmServiceImpl extends GenericCurdServiceImpl<BcFmMapper, BcFm>
        implements IBcFmService {
    @Resource
    private BcFmMapper bcFmMapper;

    @Override
    public List<BcFm> findFmListByMap(Map<String, Object> paramMap) {
        return bcFmMapper.findFmListByMap(Page.of(1, -1, false), paramMap).getRecords();
    }
}
