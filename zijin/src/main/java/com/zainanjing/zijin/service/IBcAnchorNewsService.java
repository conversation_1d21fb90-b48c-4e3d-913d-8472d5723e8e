package com.zainanjing.zijin.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.zijin.domain.BcAnchorNews;

/**
 * 主播动态Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IBcAnchorNewsService extends GenericCurdService<BcAnchorNews>
{

    IPage<BcAnchorNews> findAggregateQuery(Searchable searchable);

    int saveAggregateQuery(BcAnchorNews bcAnchorNews);

    BcAnchorNews getMoreById(Long id);

    /**
     * 删除动态
     */
    boolean deleteNews(Long id);

    int updateMoreById(BcAnchorNews bcAnchorNews);
}
