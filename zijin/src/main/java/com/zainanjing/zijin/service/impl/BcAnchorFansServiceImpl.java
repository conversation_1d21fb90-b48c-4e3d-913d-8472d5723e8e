package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcAnchorFans;
import com.zainanjing.zijin.mapper.BcAnchorFansMapper;
import com.zainanjing.zijin.service.IBcAnchorFansService;
import org.springframework.stereotype.Service;

/**
 * 主播粉丝Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class BcAnchorFansServiceImpl extends GenericCurdServiceImpl<BcAnchorFansMapper, BcAnchorFans>
        implements IBcAnchorFansService {

    @Override
    public IPage<BcAnchor> anchorListByFansIdPage(IPage<BcAnchor> page, Long fansId) {
        return this.baseMapper.selectAnchorListByFansIdPage(page, fansId);
    }
}
