package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcAdv;
import com.zainanjing.zijin.mapper.BcAdvMapper;
import com.zainanjing.zijin.service.IBcAdvService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
public class BcAdvServiceImpl extends GenericCurdServiceImpl<BcAdvMapper, BcAdv>
        implements IBcAdvService {
    @Autowired
    private BcAdvMapper bcAdvMapper;

    @Override
    public List<BcAdv> selectOptionalList(String type, String area) {
        return bcAdvMapper.selectOptionalList(type,area);
    }
}
