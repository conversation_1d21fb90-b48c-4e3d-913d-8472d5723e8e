package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcGoods;
import com.zainanjing.zijin.dto.BcGoodsDTO;
import com.zainanjing.zijin.mapper.BcGoodsMapper;
import com.zainanjing.zijin.service.IBcGoodsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 爆品管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class BcGoodsServiceImpl extends GenericCurdServiceImpl<BcGoodsMapper, BcGoods>
        implements IBcGoodsService {

    @Resource
    private BcGoodsMapper bcGoodsMapper;

    @Override
    public IPage<BcGoodsDTO> findBcGoodsList(Searchable searchable) {
        return bcGoodsMapper.selectBcGoodsList(PageDTO.of(searchable.getPage().getPageNumber(), searchable.getPage().getPageSize()),
                searchable.getFilterCdtns());
    }

    public IPage<BcGoodsDTO> findBcGoodsList(IPage page, Map<String, Object> params) {
        return bcGoodsMapper.selectBcGoodsList(page,
                params);
    }
}
