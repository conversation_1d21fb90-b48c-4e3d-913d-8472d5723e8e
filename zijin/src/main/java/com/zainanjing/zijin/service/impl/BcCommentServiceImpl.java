package com.zainanjing.zijin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.*;
import com.zainanjing.zijin.dto.EcsAvatar;
import com.zainanjing.zijin.mapper.*;
import com.zainanjing.zijin.service.IBcCommentService;
import com.zainanjing.zijin.service.IBcIndexManagerUserService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.*;

/**
 * 评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Service
public class BcCommentServiceImpl extends GenericCurdServiceImpl<BcCommentMapper, BcComment>
        implements IBcCommentService {

    @Resource
    private BcPostMapper bcPostMapper;
    @Resource
    private BcTopicMapper bcTopicMapper;
    @Resource
    private BcCommentMapper bcCommentMapper;
    @Resource
    private BcVerifyConfigMapper bcVerifyConfigMapper;
    @Resource
    private BcZiJinImgMapper bcZiJinImgMapper;
    @Resource
    private IBcIndexManagerUserService managerUserService;

    @Override
    public IPage<BcComment> findAggregateQuery(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), pageable.getPageSize());
        IPage<BcComment> commentIPage = baseMapper.selectCommentPage(pagination, searchable.getFilterCdtns());
        BcPost post;
        for (BcComment bcComment : commentIPage.getRecords()) {
            post = bcPostMapper.selectMoreById(bcComment.getPostId());
            if (null != post) {
                bcComment.setUserNameTwo(post.getUserName());
            }
        }
        return commentIPage;
    }

    @Override
    public BcComment getMoreById(Long id) {
        BcComment bcComment = baseMapper.getMoreById(id);
        if (bcComment != null) {
            BcPost bcPost = bcPostMapper.selectById(bcComment.getPostId());
            if (bcPost != null) {
                BcTopic bcTopic = bcTopicMapper.selectById(bcPost.getTopicId());
                if (bcTopic != null) {
                    bcComment.setTopicName(bcTopic.getName());
                }
                List<String> imgList = baseMapper.selectImgByPostIdAndCommentId(bcComment.getId(), bcPost.getId());
                String result = String.join(",", imgList);
                bcComment.setImgArray(result);
            }
        }
        return bcComment;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public int saveMore(BcComment bcComment) {
        BcComment comment = new BcComment();
        comment.setPostId(bcComment.getId());

        Long uid = SecurityUtils.getUserId();
        BcIndexManagerUser managerUser = managerUserService.getById(uid);
        if (managerUser != null) {
            comment.setUid(managerUser.getUid());
        }
        comment.setContent(bcComment.getPostContent());
        comment.setCreateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        comment.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        comment.setForumId(bcComment.getForumId());
        if (null != bcComment.getSecondReplyId()) {
            comment.setType(Integer.valueOf(ZijinConstants.CONSTANTS_2));
            comment.setLevel(Integer.valueOf(ZijinConstants.CONSTANTS_2));
            comment.setParentId(Long.valueOf(bcComment.getSecondReplyId()));
            comment.setFloor(0L);
        } else {
            comment.setType(Integer.valueOf(ZijinConstants.CONSTANTS_1));
            comment.setLevel(Integer.valueOf(ZijinConstants.CONSTANTS_1));
            //计算几楼
            Integer floor = baseMapper.selectFloor(comment.getPostId());
            if (null == floor) {
                floor = 0;
            }
            comment.setFloor((long) (floor + 1));
        }
        //查一下该帖子是互动还是话题
        BcPost post = bcPostMapper.selectById(comment.getPostId());
        if (post != null) {
            LambdaQueryWrapper<BcVerifyConfig> wrapper = new LambdaQueryWrapper<>();
            //互动
            if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(post.getType()))) {
                wrapper.eq(BcVerifyConfig::getVerifyType, ZijinConstants.HUDONG_IS_AUDIT_TYPE);
                BcVerifyConfig bcVerifyConfig = bcVerifyConfigMapper.selectOne(wrapper);
                if (null != bcVerifyConfig) {
                    if (ZijinConstants.CONSTANTS_0.equals(String.valueOf(bcVerifyConfig.getIsComment()))) {
                        //评论关闭
                        return 0;
                    } else {
                        if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(bcVerifyConfig.getIsAudit()))) {
                            //需要审核
                            comment.setStatus(Integer.valueOf(ZijinConstants.BC_COMMENT_NEED_AUDIT));
                        } else {
                            //不需要审核
                            comment.setStatus(Integer.valueOf(ZijinConstants.CONSTANTS_0));
                        }
                    }
                }
            } else {
                //话题
                wrapper.eq(BcVerifyConfig::getVerifyType, ZijinConstants.TOPIC_IS_AUDIT_TYPE);
                BcVerifyConfig bcVerifyConfig = bcVerifyConfigMapper.selectOne(wrapper);

                if (null != bcVerifyConfig) {
                    if (ZijinConstants.CONSTANTS_0.equals(String.valueOf(bcVerifyConfig.getIsComment()))) {
                        //评论关闭
                        return 0;
                    } else {
                        if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(bcVerifyConfig.getIsAudit()))) {
                            //需要审核
                            comment.setStatus(Integer.valueOf(ZijinConstants.BC_COMMENT_NEED_AUDIT));
                        } else {
                            //不需要审核
                            comment.setStatus(Integer.valueOf(ZijinConstants.CONSTANTS_0));
                        }
                    }
                }
            }
        }
        bcCommentMapper.insert(comment);
        if (null != bcComment.getImgArray()) {
            List<String> imgUrls = Arrays.stream(bcComment.getImgArray().split(","))
                    .map(String::trim)
                    .toList();
            for (String imgUrl : imgUrls) {
                URL url = new URL(imgUrl);
                BufferedImage image = ImageIO.read(url);
                BcZiJinImg bcZiJinImg = new BcZiJinImg();
                bcZiJinImg.setPostId(comment.getPostId());
                bcZiJinImg.setCommentId(comment.getId());
                bcZiJinImg.setType(Long.valueOf(ZijinConstants.CONSTANTS_2));
                bcZiJinImg.setImgUrl(imgUrl);
                bcZiJinImg.setCreateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
                bcZiJinImg.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
                bcZiJinImg.setHeight(image.getHeight());
                bcZiJinImg.setWidth(image.getWidth());
                bcZiJinImgMapper.insert(bcZiJinImg);
            }
        }
        return 1;
    }

    @Override
    public List<BcComment> search(Map<String, Object> filterMap) {
        String postId = (String) filterMap.get("postId");
        //评论图片
        List<BcZiJinImg> bcImgList = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                .eq("post_id", postId).eq("status", ZijinConstants.CONSTANTS_0).eq("type", ZijinConstants.CONSTANTS_2));
        addAliImgPath(bcImgList);

        Set<String> closeUids = CommonUtil.getCloseUids( filterMap.get("sessionUid"));
        filterMap.put("closeUids", closeUids);
        filterMap.put("type", ZijinConstants.TYPE_REPLAY_POST);
        List<BcComment> bcCommentList = bcCommentMapper.search(filterMap);
        commentAddHeadImage(bcCommentList);

        commentAddImg(bcCommentList, bcImgList);
        //语音阿里云地址
        for (BcComment bcComment : bcCommentList) {
            Integer isAudio = bcComment.getIsAudio();
            if (Integer.valueOf(ZijinConstants.YES).equals(isAudio)) {
                bcComment.setAudioUrl(OSSUtil.getImageURL(bcComment.getAudioUrl()));
            }
        }
        //查询子评论列表
        filterMap.put("type", ZijinConstants.TYPE_REPLAY_COMMENT);
        filterMap.remove("uid");
//        filterMap.remove("limitType");
        filterMap.put("orderBy", "idAsc");
        List<BcComment> commentToCommentList = bcCommentMapper.search(filterMap);
        commentAddHeadImage(commentToCommentList);
        commentAddImg(commentToCommentList, bcImgList);
        commentAddSonComment(bcCommentList, commentToCommentList);
        return bcCommentList;
    }

    @Override
    public List<BcComment> searchByConditions(HashMap<String, Object> filterMap, String type, String commentId, Integer currentPage, Integer pageSize) {

        Integer start = null;
        if (currentPage != null && pageSize != null) {
            start = (currentPage - 1) * pageSize;
        }

        String postId = (String) filterMap.get("postId");
        List<BcComment> bcCommentList = null;
        if (ZijinConstants.CONSTANTS_2.equals(type)) {
            List<BcZiJinImg> bcImgList = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                    .eq("post_id", postId).eq("status", ZijinConstants.CONSTANTS_0).eq("type", ZijinConstants.CONSTANTS_2));
            addAliImgPath(bcImgList);

            Set<String> closeUids = CommonUtil.getCloseUids( filterMap.get("sessionUid"));
            if (CollUtil.isNotEmpty(closeUids)) {
                filterMap.put("closeUids", closeUids);
            }
            filterMap.put("type", ZijinConstants.TYPE_REPLAY_POST);

            filterMap.entrySet().removeIf(entry -> "null".equals(entry.getValue()));
            //评论列表
            bcCommentList = bcCommentMapper.searchPages(filterMap, start, pageSize);
            commentAddHeadImage(bcCommentList);
            commentAddImg(bcCommentList, bcImgList);
            //语音阿里云地址
            for (BcComment bcComment : bcCommentList) {
                Integer isAudio = bcComment.getIsAudio();
                if (Integer.valueOf(ZijinConstants.YES).equals(isAudio)) {
                    bcComment.setAudioUrl(OSSUtil.getImageURL(bcComment.getAudioUrl()));
                }
            }
            filterMap.put("type", ZijinConstants.TYPE_REPLAY_COMMENT);
            filterMap.remove("uid");
            filterMap.put("orderBy", "idAsc");
            //子评论列表
            List<BcComment> commentToCommentList = bcCommentMapper.search(filterMap);
            commentAddHeadImage(commentToCommentList);
            commentAddImg(commentToCommentList, bcImgList);
            commentAddSonComment(bcCommentList, commentToCommentList);
        } else if (ZijinConstants.CONSTANTS_3.equals(type)) {
            //只看子评论
            List<BcZiJinImg> bcImgList = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                    .eq("post_id", postId).eq("status", ZijinConstants.CONSTANTS_0).eq("type", ZijinConstants.CONSTANTS_2));
            addAliImgPath(bcImgList);
            filterMap.put("parentId", commentId);
            filterMap.put("type", ZijinConstants.TYPE_REPLAY_COMMENT);
            bcCommentList = bcCommentMapper.searchPages(filterMap, start, pageSize);
            commentAddHeadImage(bcCommentList);
            commentAddImg(bcCommentList, bcImgList);
        }
        return bcCommentList;
    }


    /**
     * 根据图片id 判断是否是语音，实际这里是查type=5的 图片地址 （即语音）
     */
    @Override
    public Map<String, String> findAudioUrlByBcImgId(String bcImgIds) {
        Map<String, String> map = new HashMap<String, String>();
        if (StrUtil.isNotEmpty(bcImgIds)) {
            String[] bcImgIdArray = bcImgIds.split("_");
            //语音的话 肯定是只有1个地址
            if (bcImgIdArray.length == 1) {
                String audioUrl = "";
                String id = bcImgIdArray[0];
                List<BcZiJinImg> bcImgList = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                        .eq("id", Long.parseLong(id)).eq("type", ZijinConstants.IMAGE_TYPE_AUDIO));
                if (!bcImgList.isEmpty()) {
                    BcZiJinImg bcImg = bcImgList.get(0);
                    audioUrl = bcImg.getImgUrl();
                    map.put("audioUrl", audioUrl);
                    map.put("audioId", id);
                }
            }
        }
        return map;
    }

    @Override
    public void saveOnAudit(BcComment bcComment, BcPost bcPost) {
        BcVerifyConfig bcVerifyConfig = new BcVerifyConfig();
        if (bcPost.getType().equals(Integer.parseInt(ZijinConstants.POST_TYPE_COMMON))) {
            //2 为互动
            bcVerifyConfig = bcVerifyConfigMapper.selectOne(new QueryWrapper<BcVerifyConfig>()
                    .eq("verify_type", ZijinConstants.INTERACT_GENERAL_KEY));

        } else if (bcPost.getType().equals(Integer.parseInt(ZijinConstants.POST_TYPE_TOPIC))) {
            //1 为话题
            bcVerifyConfig = bcVerifyConfigMapper.selectOne(new QueryWrapper<BcVerifyConfig>()
                    .eq("verify_type", ZijinConstants.INTERACT_TOPIC_KEY));
        }
        //全局是否需要审核
        Integer isAudit = bcVerifyConfig.getIsAudit();
        //全局是否开启评论
        Integer isComment = bcVerifyConfig.getIsComment();
        //全局可以评论
        if (Integer.valueOf(ZijinConstants.YES).equals(isComment)) {
            //需要审核
            if (Integer.valueOf(ZijinConstants.YES).equals(isAudit)) {
                bcComment.setStatus(ZijinConstants.CHECKED_PENDING);
            } else {
                //不需要审核
                bcComment.setStatus(ZijinConstants.CHECKED_YES);
            }
        } else {
            bcComment.setStatus(ZijinConstants.CHECKED_NO);
        }
    }

    @Override
    public Long saveCommentAndImgs(BcComment bcComment, String bcImgIds) {
        //根据语音地址判断是否是语音
        Map<String, String> audioMap = findAudioUrlByBcImgId(bcImgIds);
        String audioUrl = audioMap.get("audioUrl");
        if (StrUtil.isNotEmpty(audioUrl)) {
            bcComment.setIsAudio(Integer.valueOf(ZijinConstants.CONSTANTS_1));
            bcComment.setAudioUrl(audioUrl);
            String audioId = audioMap.get("audioId");
            bcComment.setAudioId(Long.valueOf(audioId));
        } else {
            bcComment.setIsAudio(Integer.valueOf(ZijinConstants.CONSTANTS_0));
            bcComment.setAudioUrl("");
            bcComment.setAudioId(0L);//默认值
        }
        bcCommentMapper.insert(bcComment);
        Long commentId = bcComment.getId();
        //如果是对帖子的评论
        if (bcComment.getType().equals(1)) {
            //计算楼层
            HashMap<String, Object> map = new HashMap<String, Object>();
            map.put("postId", bcComment.getPostId());
            map.put("type", "1");
            map.put("idForFloor", commentId);
            Integer floor = bcCommentMapper.count(map);
            //更新楼层
            map = new HashMap<String, Object>();
            map.put("floor", floor);
            map.put("commentId", commentId);
            bcCommentMapper.updateByMap(map);
            map = new HashMap<String, Object>();
            map.put("postId", bcComment.getPostId());
            map.put("commentNum", "add");
            //修改评论数
            CommonUtil.redisCommonAdd( ZijinConstants.BCPOST + "_" + ZijinConstants.COMMENTNUM + "_" + bcComment.getPostId());
        }

        if (StrUtil.isNotEmpty(bcImgIds) && StrUtil.isEmpty(audioUrl)) {
            int sort = 1;
            String[] pics = bcImgIds.split("_");
            for (String id : pics) {
                BcZiJinImg bcImg = new BcZiJinImg();
                bcImg.setType(Long.valueOf(ZijinConstants.IMG_TYPE_COMMENT));
                bcImg.setCommentId(commentId);
                bcImg.setId(Long.valueOf(id));
                bcImg.setPostId(bcComment.getPostId());
                bcImg.setSort(sort);
                bcZiJinImgMapper.insertOrUpdate(bcImg);
                sort++;
            }
        }
        return commentId;
    }

    @Override
    public void searchImgUrlsAndUserHead(Long id, Long uid, BcComment bcComment) {
        List<BcZiJinImg> bcImglist = bcZiJinImgMapper.selectList(new QueryWrapper<BcZiJinImg>()
                .eq("comment_id", id).eq("status", ZijinConstants.CONSTANTS_0).eq("type", ZijinConstants.IMG_TYPE_COMMENT));

        for (BcZiJinImg bcImg : bcImglist) {
            bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
        }
        BcComment bc = bcCommentMapper.selectById(id);
        String imgUrl = bc.getImgUrl();
        Integer avaType = bc.getAvatarType();
        imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
        bcComment.setImgUrl(imgUrl);
        bcComment.setBcImgList(bcImglist);
    }

    @Override
    public void updateHasNewComment(String type, String postId, String commentId, Integer hasNewComment) {
        if (type.equals(ZijinConstants.CONSTANTS_1)) {
            bcPostMapper.update(new UpdateWrapper<BcPost>().eq("id", postId).set("has_new_comment", hasNewComment));
            //这里用于当有新回帖时 让我的主题里  的“回复我的” 出现红点
        } else if (type.equals(ZijinConstants.CONSTANTS_2)) {
            bcCommentMapper.update(new UpdateWrapper<BcComment>().eq("id", commentId).set("has_new_comment", hasNewComment));
        }
    }

    @Override
    public Integer selectMedalLevelByUid(Long uid) {
        return bcCommentMapper.selectMedalLevelByUid(uid);
    }

    @Override
    public String selectUserNameByUid(Long uid) {
        return bcCommentMapper.selectUserNameByUid(uid);
    }

    @Override
    public EcsAvatar findEcsAvatarByUid(Long uid) {
        return bcCommentMapper.findEcsAvatarByUid(uid);
    }

    private void addAliImgPath(List<BcZiJinImg> bcImgList) {
        for (BcZiJinImg bcImg : bcImgList) {
            bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
        }
    }

    private void commentAddHeadImage(List<BcComment> bcCommentList) {
        if (bcCommentList != null && !bcCommentList.isEmpty()) {
            for (BcComment bcComment : bcCommentList) {
                String imgUrl = bcComment.getImgUrl();
                Integer avaType = bcComment.getAvatarType();
                imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
                bcComment.setImgUrl(imgUrl);
            }
        }
    }

    public void commentAddImg(List<BcComment> bcCommentList,
                              List<BcZiJinImg> bcImgList) {
        for (BcZiJinImg bcImg : bcImgList) {
            for (BcComment bcComment : bcCommentList) {
                if (bcComment.getId().equals(bcImg.getCommentId())) {
                    bcComment.getBcImgList().add(bcImg);
                }
            }
        }
    }

    private void commentAddSonComment(
            List<BcComment> bcCommentList, List<BcComment> commentToCommentList) {

        for (BcComment bcComment : bcCommentList) {
            for (BcComment bcCommentSon : commentToCommentList) {
                if (bcComment.getId().equals(bcCommentSon.getParentId())) {
                    bcComment.setSonCommentNum(bcComment.getSonCommentNum() + 1);
                    bcComment.getBcCommentList().add(bcCommentSon);

                }
            }
        }
    }
}
