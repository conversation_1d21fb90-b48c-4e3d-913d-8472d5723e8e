package com.zainanjing.zijin.service.impl;

import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.zijin.domain.BcAnchorRelation;
import com.zainanjing.zijin.domain.BcProgram;
import com.zainanjing.zijin.mapper.BcAnchorRelationMapper;
import com.zainanjing.zijin.service.IBcAnchorRelationService;
import com.zainanjing.zijin.service.IBcProgramService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 节目主播关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class BcAnchorRelationServiceImpl extends GenericCurdServiceImpl<BcAnchorRelationMapper, BcAnchorRelation>
        implements IBcAnchorRelationService {

    @Resource
    private IBcProgramService programService;

    @Override
    public Map<Long, List<BcProgram>> getAllBcName(Long anchorId) {
        List<BcAnchorRelation> bcAnchorRelations = this.lambdaQuery().eq(ObjUtil.isNotNull(anchorId), BcAnchorRelation::getAnchorId, anchorId).orderByDesc(BcAnchorRelation::getId).list();
        List<BcProgram> bcPrograms = programService.lambdaQuery().in(BcProgram::getId, bcAnchorRelations.stream().map(BcAnchorRelation::getBcId).collect(Collectors.toList())).list();
        Map<Long, List<BcProgram>> result = new HashMap<>();
        for (BcProgram bcProgram : bcPrograms) {
            for (BcAnchorRelation bcAnchorRelation : bcAnchorRelations) {
                if (bcProgram.getId().equals(bcAnchorRelation.getBcId())) {
                    if (result.containsKey(bcAnchorRelation.getAnchorId())) {
                        result.get(bcAnchorRelation.getAnchorId()).add(bcProgram);
                    } else {
                        List<BcProgram> bcProgramList = new ArrayList<>();
                        bcProgramList.add(bcProgram);
                        result.put(bcAnchorRelation.getAnchorId(), bcProgramList);
                    }
                }
            }
        }
        return result;
    }
}
