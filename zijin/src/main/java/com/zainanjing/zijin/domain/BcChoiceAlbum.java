package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 精选内容专辑对象 bc_choice_album
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@TableName(value = "bc_choice_album")
public class BcChoiceAlbum implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 所属分类id
     */
    private Long categoryId;

    /**
     * 专辑名称
     */
    private String name;

    /**
     * 专辑封面图片
     */
    private String logo;

    /**
     * 主播
     */
    private String anchor;

    /**
     * 出品
     */
    private String produce;

    /**
     * 描述
     */
    private String description;

    /**
     * 专辑简介图片url
     */
    private String descImgUrl;

    /**
     * 是否关联主播 0 不关联 1 关联
     */
    private Integer isRefAnchor;

    /**
     * 主播uid
     */
    private Long anchorUid;

    /**
     * 播放次数
     */
    private Long view;

    /**
     * 虚拟播放次数
     */
    private Long virtualView;

    /**
     * 是否推荐到热门新品 0 否 1 是
     */
    private Integer isHot;

    /**
     * 是否推荐到精选 0 否 1 是
     */
    private Integer isJingxuan;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 分享设置-分享图标
     */
    private String shareIcon;

    /**
     * 分享设置-主标题
     */
    private String shareTitle;

    /**
     * 分享设置-描述语
     */
    private String shareDesc;

    /**
     * 1音频 2视频
     */
    private Integer type;

    /**
     * 状态 1正常 2删除
     */
    private Integer status;

    /**
     * 栏目id
     */
    private Integer lanMuId;

    /**
     * 专辑名称
     */
    @TableField(exist = false)
    private String choiceCategoryName;

    /**
     * 主播名称
     */
    @TableField(exist = false)
    private Integer anchorId;

}
