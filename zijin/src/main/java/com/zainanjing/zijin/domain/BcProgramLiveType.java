package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 直播分类对象 bc_program_live_type
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@TableName(value = "bc_program_live_type")
public class BcProgramLiveType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * $column.columnComment
     */
    private Date createTime;

    /**
     * $column.columnComment
     */
    private Date updateTime;

    /**
     * 直播分类全称
     */
    private String name;

    /**
     * 直播分类简称
     */
    private String shortName;

    /**
     * 直播分类频率号
     */
    private String channel;

    /**
     * 直播分类介绍
     */
    private String description;

    /**
     * 直播分类LOGO
     */
    private String logo;

    /**
     * 是否推荐
     */
    private Integer isRec;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 1删除 0正常
     */
    @TableLogic(value = "0", delval = "1")
    private Integer status;

}
