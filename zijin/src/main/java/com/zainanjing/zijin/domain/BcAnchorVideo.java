package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 主播视频对象 bc_anchor_video
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "bc_anchor_video")
public class BcAnchorVideo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否删除 1是 0否
     */
    private Integer isDel;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 视频名称
     */
    private String videoName;

    /**
     * 视频简介
     */
    private String videoDesc;

    /**
     * 视频时长,单位:秒
     */
    private Long videoDuration;

    /**
     * 视频文件URL
     */
    private String playUrl;

    /**
     * 视频封面URL
     */
    private String coverUrl;

    /**
     * 视频分享图URL
     */
    private String shareImgUrl;

    /**
     * 是否显示 1是 0否
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Long sortNo;

}
