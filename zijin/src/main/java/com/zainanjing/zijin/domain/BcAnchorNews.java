package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 主播动态对象 bc_anchor_news
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName(value = "bc_anchor_news")
public class BcAnchorNews implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 1是 0否
     */
    private Integer isDel;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 动态内容
     */
    private String newsContent;

    /**
     * 封面图url
     */
    private String coverFileUrl;

    /**
     * 播放url
     */
    private String fileUrl;

    /**
     * 播放时间(S)
     */
    private Long fileDuration;

    /**
     * 可见值 0 私密 1 公开
     */
    private Integer visibleType;

    /**
     * 发布来源 0 PC端  1手机端
     */
    private Integer soureType;

    /**
     * 点赞数量
     */
    private Long compNum;

    /**
     * 是否显示 1 是 0否
     */
    private Integer isShow;

    @TableField(exist = false)
    private String conmmentNumb;
    @TableField(exist = false)
    private String anchorName;
    @TableField(exist = false)
    private String anchorImgUrl;
    @TableField(exist = false)
    private String fmName;
    @TableField(exist = false)
    private String programName;
    @TableField(exist = false)
    private Integer publishType;
    /**
     * 动态图片
     */
    @TableField(exist = false)
    private String imgUrl;

    /**
     * 动态回复
     */
    @TableField(exist = false)
    private List<BcAnchorNewsReply> replyList;
}
