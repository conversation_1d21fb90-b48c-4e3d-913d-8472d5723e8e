package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 爆品管理对象 bc_goods
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName(value = "bc_goods")
public class BcGoods implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 爆品链接类型:0:内链;1:外链
     */
    private String linkType;

    /**
     * 链接跳转模块:0---商品详情页;1---列表页
     */
    private String linkModule;

    /**
     * 链接到:0--分类列表;1--资源详情页
     */
    private String linkTo;

    /**
     * 商品id商品资源id
     */
    private String goodsResId;

    /**
     * 外链图片URL
     */
    private String imgUrl;

    /**
     * 外链地址
     */
    private String imgLink;

    /** 爆品展示区域:0---广播节目爆品;1---主播圈爆品
     */
    private String showPosition;

    /**
     * 互动区id
     */
    private Long forumId;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 是否推荐到主播圈首页:Y---是;N---否
     */
    private String isRecommandAnchor;

    /**
     * 是否显示:Y---是;N---否
     */
    private String isShow;

    /**
     * 是否讲解  0 否  1 是
     */
    private Integer isExplain;

    /**
     * 排序号
     */
    private Long sortNo;

    /**
     * 是否被删除，0=否 1=是
     */
    private Integer isDel;

}
