package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.zainanjing.zijin.constant.ZijinConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 帖子数据对象 bc_post
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@TableName(value = "bc_post")
public class BcPost implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 帖子ID
     */
    @TableId
    private Long id;

    /**
     * 版块ID
     */
    private Long forumId;

    /**
     * 节目ID
     */
    private Long programId;

    /**
     * 所属话题id bc_topic.id
     */
    private Long topicId;

    /**
     * 标题
     */
    private String subject;

    /**
     * 内容
     */
    private String content;

    /**
     * 20240118 评论记录IP地址
     */
    private String ip;

    /**
     * 20240118 评论记录地区
     */
    private String region;

    /**
     * 标题颜色
     */
    private String color;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 浏览量
     */
    private Integer clickNum;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 点赞数
     */
    private Integer praiseNum;

    /**
     * 1普通 2主持人 3回听 4话题
     */
    private Integer type;

    /**
     * 地址
     */
    private String url;

    /**
     * 是否匿名 1是 0否
     */
    private Integer isAnonymous;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否推荐 1是0否
     */
    private Integer isRec;

    /**
     * 是否置顶 1是0否
     */
    private Integer isTop;

    /**
     * 分类 预留
     */
    private Integer category;

    /**
     * 发帖时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 回听贴开始时间
     */
    private String startTime;

    /**
     * 回听贴结束时间
     */
    private String endTime;

    /**
     * 查看等级 1版主可见 0都可见
     */
    private Integer seeLevel;

    /**
     * 状态 1删除 0 正常 30 待审核 32 审核不通过
     */
    private Integer status;

    /**
     * 关联点播表主键
     */
    private Long replayId;

    /**
     * 回听贴日期
     */
    private String startDate;

    /**
     * 是否有最新评论 1是 0否
     */
    private Integer hasNewComment;

    /**
     * 帖子所属频率id
     */
    private Long fmId;

    /**
     * 视频图片
     */
    private String videoImg;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 置顶类型,本互动区1  本频率2  全局3    默认1
     */
    private Integer topType;

    /**
     * 板块内排序
     */
    private Integer forumNum;

    /**
     * 是否开启评论 默认0 能评论 1不能评论
     */
    private Integer isComment;

    /**
     * 是否是语音帖(语音帖子可以语音回复)
     */
    private Integer isVoice;

    /**
     * 审核人uid
     */
    private Long auditUid;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date auditTime;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 话题封面图
     */
    private String coverThumb;

    /**
     * 作者
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 互动区名称
     */
    @TableField(exist = false)
    private String forumName;

    /**
     * 话题名称
     */
    @TableField(exist = false)
    private String topicName;

    /**
     * 帖子下的评论汇总
     */
    @TableField(exist = false)
    private List<BcComment> commentList = new ArrayList<>();

    /**
     * 帖子图片
     */
    @TableField(exist = false)
    private String imageUrls;

    /**
     * 频率名称
     */
    @TableField(exist = false)
    private String fmName;

    /**
     * 频率名称简称
     */
    @TableField(exist = false)
    private String fmShortName;

    /**
     * 用户头像
     */
    @TableField(exist = false)
    private String imgUrl;

    /**
     * 头像来源
     */
    @TableField(exist = false)
    private Integer avatarType;

    @TableField(exist = false)
    private String programLogo;

    @TableField(exist = false)
    private Integer isReward;

    @TableField(exist = false)
    private List<BcZiJinImg> bcImgList = new ArrayList<BcZiJinImg>();

    /**
     * 当前用户是否点过赞?	0未赞 1已赞
     */
    @TableField(exist = false)
    private Integer isPraise;

    /**
     * 评论列表 app用
     */
    @TableField(exist = false)
    private List<BcComment> bcCommentList = new ArrayList<BcComment>();

    /**
     * 是否收藏	0否 1是
     */
    @TableField(exist = false)
    private Integer isCollect;

    /**
     * 收藏id
     */
    @TableField(exist = false)
    private String collectedid;

    /**
     * 是否在直播
     */
    @TableField(exist = false)
    private Integer isLive = Integer.valueOf(ZijinConstants.NO);
}
