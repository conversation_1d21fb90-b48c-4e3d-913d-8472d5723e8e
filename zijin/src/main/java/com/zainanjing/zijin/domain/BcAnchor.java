package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 主播对象 bc_anchor
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "bc_anchor")
public class BcAnchor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除 1是 0否
     */
    private Integer isDel;

    /**
     * $column.columnComment
     */
    private Long channelId;

    /**
     * 用户id,对应gdmm_users表的主键id
     */
    private Long userId;

    /**
     * 广播节目id
     */
    private Long bcId;

    /**
     * 主播名称
     */
    private String anchorName;

    /**
     * 是否评论 1 开启 0 关闭
     */
    private Integer isTalk;

    /**
     * 是否审核 1 审核 0 不审
     */
    private Integer isAudit;

    /**
     * 主播简介
     */
    private String anchorDesc;

    /**
     * 主播图片URL
     */
    private String anchorImgUrl;

    /**
     * 分享图URL
     */
    private String shareImgUrl;

    /**
     * 动态时长（s)
     */
    private Long fileDuration;

    /**
     * 是否显示:1 是 0 否
     */
    private Integer isShow;

    /**
     * 列表页排序
     */
    private Long listSortNo;

    /**
     * 是否推荐到栏目首页; 1是 0否
     */
    private Integer isRecMain;

    /**
     * 首页排序
     */
    private Long mainSortNo;

    /**
     * 排行榜排序方式:0---系统排序;1:人工手动排序
     */
    private Integer topSortType;

    /**
     * 排行榜手动设置排序
     */
    private Long topSortNo;

    /**
     * 粉丝数
     */
    private Integer fansNum;

    /**
     * 视频数量
     */
    @TableField(exist = false)
    private Integer videoNum;

    /**
     * 主播相册数量
     */
    @TableField(exist = false)
    private Integer imgNum;

    @TableField(exist = false)
    private String userName;

    /**
     * 节目id
     */
    @TableField(exist = false)
    private String programId;

    @TableField(exist = false)
    private String programName;

    /**
     * 主播手机号
     */
    @TableField(exist = false)
    private String phone;

}
