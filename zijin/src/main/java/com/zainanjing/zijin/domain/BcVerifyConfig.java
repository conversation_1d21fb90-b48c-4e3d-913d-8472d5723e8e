package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发言审核配置对象 bc_verify_config
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName(value = "bc_verify_config")
public class BcVerifyConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId
    private Long id;

    /**
     * $column.columnComment
     */
    private LocalDateTime createTime;

    /**
     * $column.columnComment
     */
    private LocalDateTime updateTime;

    /**
     * 1 话题  2 互动 3 主播 4精选  5视频
     */
    private Integer verifyType;

    /**
     * 是否开启讨论 0 关闭  1开启
     */
    private Integer isComment;

    /**
     * 核查状态    是否审核 1 审核 0 不审
     */
    private Integer isAudit;

    /**
     * 操作人
     */
    private Long userId;

    /**
     * 备注
     */
    private String remark;

}
