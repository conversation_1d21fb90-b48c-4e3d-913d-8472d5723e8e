package com.zainanjing.zijin.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 评论回复图片对象 bc_comment_image
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@TableName(value ="bc_comment_image")
public class BcCommentImage  implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 关联id:话题表的主键，回复表的主键，根据类型来关联 */
    private Long refId;

    /** 关联类型:
            10--广播话题评论;11---广播话题回复;12---广播话题二级回复;20---广播提问;21---广播提问回复;22--广播提问二级回复;30--专辑评论;31---专辑回复;32----专辑二级回复;40---直播评论;50---主播动态;51---主播动态回复; */
    private String refType;

    /** $column.columnComment */
    private String imgUrl;

    /** 图片宽度 */
    private Long imgWidth;

    /** 图片高度 */
    private Long imgHeight;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

}
