package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 导航配置对象 bc_navigation_config
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName(value = "bc_navigation_config")
public class BcNavigationConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId
    private Long id;

    /**
     * $column.columnComment
     */
    private Date createTime;

    /**
     * $column.columnComment
     */
    private Date updateTime;

    /**
     * 是否显示 1是 0否
     */
    private Integer isShow;

    /**
     * 是否删除 1是 0否
     */
    private Integer isDel;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 类型 1导航 2特殊
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer sort;

}
