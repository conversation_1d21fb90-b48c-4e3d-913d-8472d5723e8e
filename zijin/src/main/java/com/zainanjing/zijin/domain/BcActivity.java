package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动对象 bc_activity
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName(value = "bc_activity")
public class BcActivity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * $column.columnComment
     */
    private Date createTime;

    /**
     * $column.columnComment
     */
    private Date updateTime;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动图片URL
     */
    private String activityImgUrl;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 链接类型:1--内链;2--外链
     */
    private String linkType;

    /**
     * 链接跳转模块:0--商品详情页;1--列表页
     */
    private String linkJumpTo;

    /**
     * 链接到:1--分类列表页;2--资源详情页
     */
    private String linkTo;

    /**
     * 商品资源id
     */
    private Long goodsId;

    /**
     * 外链地址
     */
    private String linkUrl;

    /**
     * 参与人数
     */
    private Long userNum;

    /**
     * 排序
     */
    private Long sortNo;

    /**
     * 是否被删除，0=否 1=是
     */
    private Integer isDel;

}
