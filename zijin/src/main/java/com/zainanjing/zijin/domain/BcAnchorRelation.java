package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 节目主播关系对象 bc_anchor_relation
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName(value = "bc_anchor_relation")
public class BcAnchorRelation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 节目id
     */
    private Long bcId;

    /**
     * 主播id
     */
    private Long anchorId;

}
