package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 互动区数据对象 bc_forum
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName(value = "bc_forum")
public class BcForum implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 互动区ID
     */
    @TableId
    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 频率id
     */
    private Long fmId;

    /**
     * 节目单bc_proram_list的id
     */
    private Long programId;

    /**
     * 点播节目代号
     */
    private String code;

    /**
     * 互动区名称
     */
    private String name;

    /**
     * 互动区LOGO
     */
    private String logo;

    /**
     * 是否推荐 1是0否
     */
    private Integer isRec;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否开启 1正常0关闭
     */
    private Integer isShow;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    /**
     * 是否开启评论 默认0 能评论 1不能评论
     */
    private Integer isComment;

    /**
     * 主持人
     */
    private String userName;

    /**
     * 主持人中文名
     */
    private String chineseName;

    /**
     * 频率名称
     */
    @TableField(exist = false)
    private String fmName;

    /**
     * 节目单名称
     */
    @TableField(exist = false)
    private String programListName;

    /**
     * 主键用于返回给客户端
     */
    @TableField(exist = false)
    private Long forumId;

    /**
     * 节目名称
     */
    @TableField(exist = false)
    private String programName;

    /**
     * 节目logo
     */
    @TableField(exist = false)
    private String programLogo;

    /**
     * 是否直播
     */
    @TableField(exist = false)
    private Integer isLive;

    /**
     * 播放地址
     */
    @TableField(exist = false)
    private String url;

    /**
     * 频率简称
     */
    @TableField(exist = false)
    private String fmShortName;

    /**
     * 回听列表数量
     */
    @TableField(exist = false)
    private Long bcReplayListSize;

    /**
     * 开始时间  13:00
     */
    @TableField(exist = false)
    private String startTime;

    /**
     * 结束时间 14:00
     */
    @TableField(exist = false)
    private String endTime;

    /**
     * 主播列表
     */
    @TableField(exist = false)
    private List<BcAnchor> bcAnchorList;

    /**
     * 是否是当前帖子的主播 1是 0不是
     */
    @TableField(exist = false)
    private Integer isThisAnchor;

    @TableField(exist = false)
    private Integer currDate;

    /**
     * 是否收藏	0否 1是
     */
    @TableField(exist = false)
    private Integer isCollect;

    /**
     * //收藏id
     */
    @TableField(exist = false)
    private String collectedid;
}
