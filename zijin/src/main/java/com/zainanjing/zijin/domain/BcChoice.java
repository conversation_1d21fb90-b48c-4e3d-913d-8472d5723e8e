package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 精选内容分集对象 bc_choice
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@TableName(value = "bc_choice")
public class BcChoice implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 所属专辑id
     */
    private Long albumId;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 分集图片
     */
    private String logo;

    /**
     * 分集地址
     */
    private String url;

    /**
     * 音视频时长 格式"00:00:15"
     */
    private String duration;

    /**
     * 音视频时长 秒
     */
    private Integer seconds;

    /**
     * 浏览数
     */
    private Integer view;

    /**
     * 虚拟浏览量
     */
    private Integer virtualView;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分享设置-分享图标
     */
    private String shareIcon;

    /**
     * 分享设置-主标题
     */
    private String shareTitle;

    /**
     * 分享设置-描述语
     */
    private String shareDesc;

    /**
     * 1音频 2视频
     */
    private Integer type;

    /**
     * 状态 1正常 2删除
     */
    private Integer status;

    /**
     * 第三方接入ID
     */
    private String idForThird;

    /**
     * 是否推荐 0 否 1 是
     */
    private Integer isRecommend;

    /**
     * 往客户端推荐的cardid存储
     */
    private Long cardid;

//    /**
//     * 添加人uid
//     */
//    @Excel(name = "添加人uid")
//    private Integer createUid;
//
//    /**
//     * 审批状态(当前审批角色) 根据配置获取的当前审批状态
//     */
//    @Excel(name = "审批状态(当前审批角色) 根据配置获取的当前审批状态")
//    private Integer approveStatus;
//
//    /**
//     * 审核状态 1=待审核 2=通过 3=不通过 4=系统自动审核
//     */
//    @Excel(name = "审核状态 1=待审核 2=通过 3=不通过 4=系统自动审核")
//    private Integer checkStatus;
//
//    /**
//     * 是否发布 0=否 1=是
//     */
//    @Excel(name = "是否发布 0=否 1=是")
//    private Integer isPublish;
//
//    /**
//     * 冗余配置的审核人员
//     */
//    @Excel(name = "冗余配置的审核人员")
//    private String approveMember;
//
//    /**
//     * 冗余审核记录
//     */
//    @Excel(name = "冗余审核记录")
//    private String checkWorkFlow;

}
