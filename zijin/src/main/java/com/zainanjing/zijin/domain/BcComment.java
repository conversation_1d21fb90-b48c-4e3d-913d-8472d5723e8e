package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 评论对象 bc_comment
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Data
@TableName(value = "bc_comment")
public class BcComment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评论自增ID
     */
    @TableId
    private Long id;

    /**
     * 帖子ID
     */
    private Long postId;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 20240118 评论记录IP地址
     */
    private String ip;

    /**
     * 20240118 评论记录地区
     */
    private String region;

    /**
     * 是否是语音 0否 1是
     */
    private Integer isAudio;

    /**
     * 语音唯一标识,这里用bc_img主键
     */
    private Long audioId;

    /**
     * 语音秒数
     */
    private Integer audioSecond;

    /**
     * 语音 存的是文件地址
     */
    private String audioUrl;

    /**
     * 父评论ID
     */
    private Long parentId;

    /**
     * 1对帖子的评论 2对评论的评论
     */
    private Integer type;

    /**
     * 1对帖子的评论 2对评论的评论
     */
    private Integer level;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 状态 1用户删除 0 正常，审核通过 2.主持人删除 30 待审核 32 审核不通过
     */
    private Integer status;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date auditTime;

    /**
     * 审核人id
     */
    private Long auditUid;

    /**
     * 驳回理由
     */
    private String remark;

    /**
     * 是否匿名 1是 0否
     */
    private Integer isAnonymous;

    /**
     * 板块id
     */
    private Long forumId;

    /**
     * 是否含有新评论 含1 0不含
     */
    private Integer hasNewComment;

    /**
     * 楼层
     */
    private Long floor;

    /**
     * 帖子内容
     */
    @TableField(exist = false)
    private String postContent;

    /**
     * 互动区名称
     */
    @TableField(exist = false)
    private String forumName;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 用户手机号
     */
    @TableField(exist = false)
    private String phone;

    /**
     * 作者
     */
    @TableField(exist = false)
    private String userNameTwo;

    /**
     * 话题名称
     */
    @TableField(exist = false)
    private String topicName;

    /**
     * 评论带的图片
     */
    @TableField(exist = false)
    private String imgArray;

    /**
     * 评论图片
     */
    @TableField(exist = false)
    private List<String> picUrls;

    @TableField(exist = false)
    private String secondReplyId;

    /**
     * 评论图片
     */
    @TableField(exist = false)
    private List<BcComment> secondReplyList;

    /**
     * 评论用户头像
     */
    @TableField(exist = false)
    private String imgUrl;

    /**
     * 0表示php那边的图片   1表示
     */
    @TableField(exist = false)
    private Integer avatarType;

    /**
     * 评论图片列表	数组
     */
    @TableField(exist = false)
    private List<BcZiJinImg> bcImgList = new ArrayList<BcZiJinImg>();

    /**
     * 子评论数量 客户端用
     */
    @TableField(exist = false)
    private Integer sonCommentNum = 0;

    /**
     * 子评论列表
     */
    @TableField(exist = false)
    private List<BcComment> bcCommentList = new ArrayList<BcComment>();

    /**
     * 主键用于返回给客户端
     */
    @TableField(exist = false)
    private Long commentId;

    /**
     * 用户勋章等级
     */
    @TableField(exist = false)
    private Integer medalLevel;
}
