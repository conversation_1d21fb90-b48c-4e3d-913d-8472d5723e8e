package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 主播粉丝对象 bc_anchor_fans
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@NoArgsConstructor
@SuperBuilder
@TableName(value = "bc_anchor_fans")
public class BcAnchorFans implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 粉丝id,即用户id
     */
    private Long fansId;
}
