package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广播话题对象 bc_topic
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@TableName(value = "bc_topic")
public class BcTopic implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键自增id
     */
    @TableId
    private Long id;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 话题名称
     */
    private String name;

    /**
     * 话题描述
     */
    private String description;

    /**
     * 话题内容
     */
    private String content;

    /**
     * 封面图
     */
    private String coverThumb;

    /**
     * 浏览量
     */
    private Integer view;

    /**
     * 状态 1=正常 2=删除
     */
    private Integer status;

    /**
     * 版块ID bc_forum.id
     */
    private Long forumId;

    /**
     * 频率id bc_fm.id
     */
    private Long fmId;

    /**
     * 节目id bc_program_list.program_id
     */
    private Long programId;

    /**
     * 排序 越大越靠前
     */
    private Integer sort;

    /**
     * 话题日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date topicDate;

    /**
     * 话题回复数(帖子数)
     */
    private Integer replyNum;

    /**
     * 是否开启评论 默认1 能评论 0不能评论
     */
    private Integer isComment;

    /**
     * 互动区名称
     */
    @TableField(exist = false)
    private String forumName;
}



