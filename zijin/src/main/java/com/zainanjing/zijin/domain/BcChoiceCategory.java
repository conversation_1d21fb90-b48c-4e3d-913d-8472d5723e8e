package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 精选内容分类对象 bc_choice_category
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "bc_choice_category")
public class BcChoiceCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类介绍
     */
    private String description;

    /**
     * 分类LOGO
     */
    private String logo;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 预览地址
     */
    private String url;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分享设置-分享图标
     */
    private String shareIcon;

    /**
     * 分享设置-主标题
     */
    private String shareTitle;

    /**
     * 分享设置-描述语
     */
    private String shareDesc;

    /**
     * 状态 1正常 2删除
     */
    private Integer status;

    /**
     * 1音频 2视频
     */
    private Integer type;

}
