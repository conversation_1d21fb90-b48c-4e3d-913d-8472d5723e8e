package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 热门搜索对象 bc_hot_search
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName(value = "bc_hot_search")
public class BcHotSearch implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 热搜时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate searchTime;

    /**
     * 热搜内容
     */
    private String searchContent;

    /**
     * 搜索数量
     */
    private Long searchNums;

    /**
     * 是否系统生成:Y--是;N---否
     */
    @TableField(value = "isAuto")
    private String isAuto;

}
