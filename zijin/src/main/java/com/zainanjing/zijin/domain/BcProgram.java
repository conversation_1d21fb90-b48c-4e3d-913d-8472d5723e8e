package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 节目名称对象 bc_program
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName(value = "bc_program")
public class BcProgram implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节目自增ID
     */
    @TableId
    private Long id;

    /**
     * $column.columnComment
     */
    private Date createTime;

    /**
     * $column.columnComment
     */
    private Date updateTime;

    /**
     * 节目名称
     */
    private String name;

    /**
     * 是否显示 1是0否
     */
    private Integer isShow;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

}
