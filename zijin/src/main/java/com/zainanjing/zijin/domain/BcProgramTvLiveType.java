package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 电视直播分类对象 bc_program_tv_live_type
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@TableName(value = "bc_program_tv_live_type")
public class BcProgramTvLiveType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 直播分类全称
     */
    private String name;

    /**
     * 直播分类简称
     */
    private String shortName;

    /**
     * 直播分类频率号
     */
    private String channel;

    /**
     * 直播分类介绍
     */
    private String description;

    /**
     * 直播分类LOGO
     */
    private String logo;

    /**
     * 是否推荐
     */
    private Integer isRec;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 1删除 0正常
     */
    @TableLogic(value = "0", delval = "1")
    private Integer status;

    /**
     * 链接类型 0无链接 1内链 2外链
     */
    private Integer linkType;

    /**
     * 跳转模块
     */
    private Integer linkModule;

    /**
     * 资源ID
     */
    private Integer resourceId;

    /**
     * 链接到,1分类列表页 2商品内容页
     */
    private Integer linkTo;

    /**
     * 外链URL
     */
    private String linkUrl;

}
