package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 视频节目对象 bc_program_live
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@TableName(value = "bc_program_live")
public class BcProgramLive implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 视频节目自增id
     */
    @TableId
    private Long id;

    /**
     * 直播所属分类id
     */
    private Long typeId;

    /**
     * 视频直播频道ID
     */
    private Long channelId;

    /**
     * 腾讯云流名称
     */
    private String streamId;

    /**
     * 视频开始时间
     */
    private Integer startTime;

    /**
     * 视频结束时间
     */
    private Integer endTime;

    /**
     * 频率ID
     */
    private Long fmId;

    /**
     * 视频时间类型 0单次 (预留)
     */
    private Integer type;

    /**
     * 视频节目名称
     */
    private String name;

    /**
     * 视频节目封面图片
     */
    private String logo;

    /**
     * 播放类型 1 红云 2 广电云 3 其他
     */
    private Integer videoType;

    /**
     * 视频地址flv,m3u8
     */
    private String url;

    /**
     * 腾讯云视频播放地址
     */
    private String tecentVideoUrl;

    /**
     * 腾讯云视频rtmp播放地址
     */
    private String tecentVideoRtmpUrl;

    /**
     * 添加经纬度
     */
    private String location;

    /**
     * 描述
     */
    private String description;

    /**
     * 参与人数(预留)
     */
    private Integer view;

    /**
     * 互动号码(暂时8位数字)
     */
    private String number;

    /**
     * 支持互动 0不支持 1直呼模式 2消息回呼模式
     */
    private Integer interact;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 微信分享图片32k 300*200
     */
    private String shareImg;

    /**
     * 是否直播 1是0否
     */
    private Integer isLive;

    /**
     * 状态 0正常 1删除
     */
    @TableLogic(value = "0", delval = "1")
    private Integer status;

    /**
     * 排序
     */
    private Integer liveOrder;

    /**
     * 调用云接口返回唯一id
     */
    private String topicid;

    /**
     * 内容提要
     */
    private String title;

    /**
     * 视频通栏图
     */
    private String banner;

    /**
     * 是否置顶 0 正常 1 置顶
     */
    private Integer isTop;

    /**
     * 是否推荐 0 否 1 是
     */
    private Integer isRecommend;

    /**
     * 往客户端推荐的cardid存储
     */
    private Long cardid;

    /**
     * 是否开启评论 默认0 能评论 1不能评论
     */
    private Integer isComment;

    /**
     * 观看人数倍数
     */
    private Long viewTimes;

    /**
     * 最大观看人数
     */
    private Long maxView;

    /**
     * 实际观看人数
     */
    private Long actualViewTimes;

    /**
     * 腾讯云推流地址
     */
    private String pushUrl;

    @TableField(exist = false)
    private Long showView;

    @TableField(exist = false)
    private Integer isShowGoodsBtn;

    @TableField(exist = false)
    private String typeName;

    @TableField(exist = false)
    private String typeShortName;


}
