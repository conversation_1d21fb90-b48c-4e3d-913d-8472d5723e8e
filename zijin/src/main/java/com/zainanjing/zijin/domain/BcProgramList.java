package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 直播节目单对象 bc_program_list
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName(value = "bc_program_list")
public class BcProgramList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节目单自增ID
     */
    @TableId
    private Long id;

    /**
     * 节目id
     */
    private Integer programId;

    /**
     * 频率id
     */
    private Integer fmId;

    /**
     * 栏目id
     */
    private Integer columnId;

    /**
     * 备用节目点播代号
     */
    private String code;

    /**
     * 周几
     */
    private String week;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 是否开启 1是 0否
     */
    private Integer isOpen;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    @TableField(exist = false)
    private String programName;

    @TableField(exist = false)
    private String fmName;

    @TableField(exist = false)
    private String fmUrl;

    @TableField(exist = false)
    private Integer isShow;

    @TableField(exist = false)
    private Integer forumId;

    @TableField(exist = false)
    private String logo;

    @TableField(exist = false)
    private String description;

    @TableField(exist = false)
    private String startDate;


}
