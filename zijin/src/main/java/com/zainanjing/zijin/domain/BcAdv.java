package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告数据对象 bc_adv
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@TableName(value = "bc_adv")
public class BcAdv implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 广告ID
     */
    @TableId
    private Integer id;

    /**
     * 广告名称
     */
    private String name;

    /**
     * 广告标题
     */
    private String title;

    /**
     * 1电台 2直播 3电视 4 精选 5 主播圈
     */
    private Integer area;

    /**
     * 1全局互动区 2频率互动区 3版块(互动区) 4通栏滚动 5精选 6电视节目(新版目前只有 4 通栏滚动)
     */
    private Long type;

    /**
     * 上传图片
     */
    private String imgurl;

    /**
     * 链接类型 1内链 0外链
     */
    private Integer linkType;

    /**
     * 跳转模块
     */
    private Integer linkModule;

    /**
     * 资源ID
     */
    private Integer resourceId;

    /**
     * 链接到,1分类列表页 2商品内容页
     */
    private Integer linkTo;

    /**
     * 外链URL
     */
    private String linkUrl;

    /**
     * 是否开启 1是 0否
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    /**
     * 备用参数字段
     */
    private String resourceParamIds;

    /**
     * 主键用于返回给客户端
     */
    @TableField(exist = false)
    private Long advId;

}
