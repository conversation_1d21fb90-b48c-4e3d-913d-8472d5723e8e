package com.zainanjing.zijin.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 精选内容评论图片对象 bc_choice_comment_img
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName(value ="bc_choice_comment_img")
public class BcChoiceCommentImg  implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    @TableId
    private Long id;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 评论id */
    private Long commentId;

    /** 图片地址 */
    private String imgUrl;

    /** 宽度 */
    private Integer width;

    /** 高度 */
    private Integer height;

    /** 排序 */
    private Integer sort;

    /** 状态 1正常 2删除  */
    private Integer status;

}
