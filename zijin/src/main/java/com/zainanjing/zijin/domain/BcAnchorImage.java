package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 主播相册对象 bc_anchor_image
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "bc_anchor_image")
public class BcAnchorImage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 1是 0否
     */
    private Integer isDel;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 照片名称
     */
    private String imgName;

    /**
     * 照片URL
     */
    private String imgUrl;

    /**
     * 是否显示 1是 0否
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Long sortNo;

}
