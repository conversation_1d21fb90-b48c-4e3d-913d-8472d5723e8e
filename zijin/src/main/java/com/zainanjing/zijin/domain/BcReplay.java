package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 点播对象 bc_replay
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@TableName(value = "bc_replay")
public class BcReplay implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 节目名
     */
    private String name;

    /**
     * 频道号
     */
    private String channel;

    /**
     * 节目代号
     */
    private String code;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 点播地址
     */
    private String url;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 节目id
     */
    private Long programId;

    /**
     * 频道id
     */
    private Long fmId;

    /**
     * 讨论版id
     */
    private Integer forumId;

    /**
     * 该点播是否生成回听贴
     */
    private Integer isGeneratePost;

}
