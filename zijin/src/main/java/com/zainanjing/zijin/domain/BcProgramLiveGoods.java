package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 直播爆品管理对象 bc_program_live_goods
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@TableName(value = "bc_program_live_goods")
public class BcProgramLiveGoods implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 视频节目表id
     */
    private Long programLiveId;

    /**
     * 爆品链接类型:0:内链;1:外链
     */
    private String linkType;

    /**
     * 链接跳转模块:0---商品详情页;1---列表页
     */
    private String linkModule;

    /**
     * 链接到:0--分类列表;1--资源详情页
     */
    private String linkTo;

    /**
     * 商品id商品资源id
     */
    private String goodsResId;

    /**
     * 外链图片URL
     */
    private String imgUrl;

    /**
     * 外链地址
     */
    private String imgLink;

    /**
     * 排序号
     */
    private Long sortNo;

    /**
     * 是否讲解  0 否  1 是
     */
    private Integer isExplain;

    /**
     * 是否显示:Y---是;N---否
     */
    private String isShow;

    /**
     * 是否被删除，0=否 1=是
     */
    private Integer isDel;

    @TableField(exist = false)
    private String goodsName;

    @TableField(exist = false)
    private String goodsImgUrl;

    @TableField(exist = false)
    private String goodsPrice;

    @TableField(exist = false)
    private String goodsTitle;
}
