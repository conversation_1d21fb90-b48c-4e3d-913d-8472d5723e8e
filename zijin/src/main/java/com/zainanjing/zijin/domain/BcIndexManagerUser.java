package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 管理用户和会员对应关系对象 sq_index_manager_user
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@TableName(value ="sq_index_manager_user")
public class BcIndexManagerUser implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 角色id */
    @TableId(type = IdType.INPUT)
    private Long managerId;

    /** 用户ID */
    private Long uid;

}
