package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 精选内容评论对象 bc_choice_comment
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName(value = "bc_choice_comment")
public class BcChoiceComment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 上级ID
     */
    private Long parentId;

    /**
     * 专辑ID
     */
    private Long albumId;

    /**
     * 分集ID
     */
    private Long choiceId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 评论内容
     */
    private String content;

    /* 评论ip
    private String ip;*/

    /* 评论归属地 如:江苏
    private String region;*/

    /**
     * 是否匿名 1是 0否
     */
    private Integer isAnonymous;

    /**
     * 是否显示 1是 0否
     */
    private Integer isShow;

    /**
     * 状态 1待审核 2审核通过 3审核未通过
     */
    private Integer status;

    /**
     * 回复数
     */
    private Long replyNums;

    /**
     * 备注
     */
    private String remark;


    /**
     * 分集名称
     */
    @TableField(exist = false)
    private String choiceName;

    /**
     * 专辑名称
     */
    @TableField(exist = false)
    private String albumName;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 评论图片
     */
    @TableField(exist = false)
    private String imgUrls;

    /**
     * 评论图片
     */
    @TableField(exist = false)
    private List<String> picUrls;

    @TableField(exist = false)
    private List<BcChoiceComment> choiceComments;
}
