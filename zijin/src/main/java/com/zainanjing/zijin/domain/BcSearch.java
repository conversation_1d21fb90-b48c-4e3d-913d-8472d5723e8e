package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 搜索记录对象 bc_search
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName(value = "bc_search")
public class BcSearch implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 搜索内容
     */
    private String searchContent;

    /**
     * 创建时间
     */
    private Date createTime;

}
