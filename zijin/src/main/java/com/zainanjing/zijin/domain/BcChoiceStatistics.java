package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 精选音视频播放统计对象 bc_choice_statistics
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@TableName(value = "bc_choice_statistics")
public class BcChoiceStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId
    private Integer id;

    /**
     * 添加时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * bc_choice.id
     */
    private Integer choiceId;

    /**
     * 1音频 2视频
     */
    private Integer type;

    /**
     * 真实播放量
     */
    private Integer view;

    /**
     * 真实+虚拟播放量
     */
    private Integer totalView;

    /**
     * 存放年月日的时间戳(生成当前记录的年月日)
     */
    private Integer unixTime;

    /**
     * 存放年月的时间戳(生成当前记录的年月)
     */
    private Integer unixMonthTime;

}
