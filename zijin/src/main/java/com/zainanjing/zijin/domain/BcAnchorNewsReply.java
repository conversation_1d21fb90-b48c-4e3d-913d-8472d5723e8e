package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 主播动态回复对象 bc_anchor_news_reply
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@TableName(value = "bc_anchor_news_reply")
public class BcAnchorNewsReply implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 1是0否
     */
    private Integer isDel;

    /**
     * 父回复ID
     */
    private Long parentEntityId;

    /**
     * 主播动态id
     */
    private Long newsId;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 回复时间
     */
    private Date replyTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * From用户/主播
     */
    private String fromUserName;

    /**
     * To用户编号
     */
    private Long toUserId;

    /**
     * To用户/主播
     */
    private String toUserName;

    /**
     * 是否匿名 1是 0否
     */
    private Integer isAnonymous;

    /**
     * 是否显示 1是 0否
     */
    private Integer isShow;

    /**
     * 是否审核 0是待审核 1是审核通过 2是未通过
     */
    private Integer isAudit;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 1 一级回复 2 二级回复
     */
    private Integer replyType;

    /**
     * 备注
     */
    private String remark;

    /**
     * to用户是否匿名 1是 0否
     */
    private Integer isAnonymousByTo;

    /**
     * 作为消息判断，是否已读 1是0否
     */
    private Integer isRead;

    @TableField(exist = false)
    private String newsContent;
    @TableField(exist = false)
    private String imgUrls;
    /**
     * 主播userid
     */
    @TableField(exist = false)
    private String anchorId;

    /**
     * 回复图片相关
     */
    @TableField(exist = false)
    private List<String> replyPictures;
}
