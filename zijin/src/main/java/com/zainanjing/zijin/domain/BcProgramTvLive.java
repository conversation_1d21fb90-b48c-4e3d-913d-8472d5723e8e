package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 电视视频节目对象 bc_program_tv_live
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@TableName(value = "bc_program_tv_live")
public class BcProgramTvLive implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 视频节目自增id
     */
    @TableId
    private Integer id;

    /**
     * 直播所属分类id
     */
    private Integer typeId;

    /**
     * 视频直播频道ID
     */
    private Integer channelId;

    /**
     * 视频开始时间
     */
    private Integer startTime;

    /**
     * 视频结束时间
     */
    private Integer endTime;

    /**
     * 频率ID(预留)
     */
    private Integer fmId;

    /**
     * 视频时间类型 0单次 (预留)
     */
    private Integer type;

    /**
     * 视频节目名称
     */
    private String name;

    /**
     * 视频节目封面图片
     */
    private String logo;

    /**
     * 打开方式 0 内容 1 外链
     */
    private Integer openType;

    /**
     * 外链url
     */
    private String linkUrl;

    /**
     * 视频地址flv,m3u8
     */
    private String url;

    /**
     * 描述
     */
    private String description;

    /**
     * 参与人数(预留)
     */
    private Integer view;

    /**
     * 互动号码(暂时8位数字)
     */
    private String number;

    /**
     * 支持互动 0不支持 1直呼模式 2消息回呼模式
     */
    private Integer interact;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 微信分享图片32k 300*200
     */
    private String shareImg;

    /**
     * 是否直播 1是0否
     */
    private Integer isLive;

    /**
     * 状态 0正常 1删除
     */
    @TableLogic(value = "0", delval = "1")
    private Integer status;

    /**
     * 排序
     */
    private Integer liveOrder;

    /**
     * 调用云接口返回唯一id
     */
    private String topicid;

    /**
     * 内容提要
     */
    private String title;

    /**
     * 是否开启评论 默认0 能评论 1不能评论
     */
    private Integer isComment;

    /**
     * 观看人数倍数
     */
    private Long viewTimes;

    /**
     * 最大观看人数
     */
    private Long maxView;

    /**
     * 实际观看人数
     */
    private Long actualViewTimes;


    @TableField(exist = false)
    private Integer videoType;

    @TableField(exist = false)
    private Integer liveStatus;
}
