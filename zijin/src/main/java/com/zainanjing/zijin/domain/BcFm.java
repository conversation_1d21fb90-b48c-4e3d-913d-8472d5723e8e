package com.zainanjing.zijin.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 频率信息对象 bc_fm
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@TableName(value = "bc_fm")
public class BcFm implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 频率自增ID
     */
    @TableId
    private Long id;

    /**
     * $column.columnComment
     */
    private Date createTime;

    /**
     * $column.columnComment
     */
    private Date updateTime;

    /**
     * 频率全称
     */
    private String name;

    /**
     * 频率简称
     */
    private String shortName;

    /**
     * 频率号
     */
    private String channel;

    /**
     * 备用直播URL
     */
    private String m3u8;

    /**
     * mms流地址
     */
    private String mms;

    /**
     * 描述
     */
    private String description;

    /**
     * 直播URL
     */
    private String url;

    /**
     * 频率LOGO
     */
    private String logo;

    /**
     * 打开方式 0内容 1外链
     */
    private Integer openType;

    /**
     * 外链地址
     */
    private String linkUrl;

    /**
     * 是否显示 1是 0否
     */
    private Integer isShow;

    /**
     * 是否推荐 1是 0否
     */
    private Integer isRec;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备用外码
     */
    private Long channelId;

    /**
     * 是否可直播 1是 0否
     */
    private Integer isLive;

    /**
     * 是否可点播 1是 0否
     */
    private Integer isUnicast;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    @TableField(exist = false)
    private Long fmId;

    @TableField(exist = false)
    private String programName;
}
