package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zainanjing.zijin.dto.GdmmGoodsDTO;
import com.zainanjing.zijin.dto.GdmmUsersDTO;
import com.zainanjing.zijin.dto.SiteCollectDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户查询非关联表用户呈现扩展数据，不做关联查询，解藕操作
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface BcExtMapper {

    /**
     * 根据goodIds 查询商品信息
     */
    List<GdmmGoodsDTO> selectGoodsByGoodIds(@Param("goodsIds") List<Long> goodsIds);

    /**
     * 查询用户表信息
     */
    List<GdmmUsersDTO> selectUsers(@Param("p") Map<String, Object> p);

    /**
     * 查询用户收藏夹
     */
    IPage<SiteCollectDTO> searchSiteCollect(IPage page, @Param("filterMap") Map<Object, Object> filterMap);

    /**
     * 获取敏感词列表
     */
    List<String> getSensitiveWords();
}
