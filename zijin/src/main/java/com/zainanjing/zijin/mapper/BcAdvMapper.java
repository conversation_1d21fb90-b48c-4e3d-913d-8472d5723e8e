package com.zainanjing.zijin.mapper;

import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcAdv;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface BcAdvMapper extends GenericMapper<BcAdv> {

    List<BcAdv> selectOptionalList(@Param("type") String type, @Param("area") String area);
}
