package com.zainanjing.zijin.mapper;

import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcChoice;
import com.zainanjing.zijin.dto.BcChoiceRankDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 精选内容分集Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface BcChoiceMapper extends GenericMapper<BcChoice> {

    List<BcChoiceRankDTO> selectRankList(@Param("type") Integer type, @Param("unixMonthTime") long unixMonthTime,
                                         @Param("start") int startInt, @Param("pageSize") Integer pageSize);
}
