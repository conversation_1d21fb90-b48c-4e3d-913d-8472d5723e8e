package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcChoiceAlbum;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 精选内容专辑Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface BcChoiceAlbumMapper extends GenericMapper<BcChoiceAlbum> {

    IPage<BcChoiceAlbum> selectBcChoiceAlbumPage(Page pagination, @Param("param") Map<String, Object> param);
}
