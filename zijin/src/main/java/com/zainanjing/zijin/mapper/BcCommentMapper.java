package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcComment;
import com.zainanjing.zijin.dto.EcsAvatar;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评论Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
public interface BcCommentMapper extends GenericMapper<BcComment> {

    IPage<BcComment> selectCommentPage(Page pagination, @Param("param") Map<String, Object> param);

    BcComment getMoreById(Long id);

    List<String> selectImgByPostIdAndCommentId(@Param("commentId") Long commentId, @Param("postId") Long postId);

    Integer selectFloor(Long postId);

    List<BcComment> search(@Param("param") Map<String, Object> param);

    List<BcComment> searchPages(@Param("param") HashMap<String, Object> param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    Integer count(@Param("param") HashMap<String, Object> param);

    void updateByMap(@Param("param") HashMap<String, Object> param);

    Integer selectMedalLevelByUid(Long uid);

    String selectUserNameByUid(Long uid);

    EcsAvatar findEcsAvatarByUid(Long uid);
}
