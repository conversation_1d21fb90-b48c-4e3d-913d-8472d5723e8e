package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcProgramLive;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 视频节目Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface BcProgramLiveMapper extends GenericMapper<BcProgramLive> {

    IPage<BcProgramLive> search(IPage<BcProgramLive> page, @Param("p") Map<String, Object> params);

}
