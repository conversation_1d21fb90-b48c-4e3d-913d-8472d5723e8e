package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcPost;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 帖子数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface BcPostMapper extends GenericMapper<BcPost> {

    IPage<BcPost> selectPostPage(Page pagination, @Param("param") Map<String, Object> param);

    BcPost selectMoreById(Long postId);

    String selectUserNameById(Long uid);

    List<BcPost> search(@Param("param") HashMap<String, Object> param, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    Integer countReward(@Param("param") Map<String, Object> param);

    BcPost findByParams(@Param("postId") String postId, @Param("uid") String uid);
}
