package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcForum;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 互动区数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface BcForumMapper extends GenericMapper<BcForum> {

    IPage<BcForum> selectBcForumPage(Page pagination, @Param("param") Map<String, Object> param);

    IPage<BcForum> selectOptionalList(IPage page,@Param("paramMap") Map<String, Object> paramMap);

    List<BcAnchor> findAnchorByForumId(Long id);
}
