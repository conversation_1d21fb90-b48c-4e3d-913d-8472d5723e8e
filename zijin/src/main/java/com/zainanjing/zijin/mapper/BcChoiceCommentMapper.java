package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcChoiceComment;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 精选内容评论Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface BcChoiceCommentMapper extends GenericMapper<BcChoiceComment> {

    IPage<BcChoiceComment> selectChoiceCommentPage(Page pagination, @Param("param") Map<String, Object> param);

    BcChoiceComment getMoreById(Long id);

    String selectUserNameById(Long userId);
}
