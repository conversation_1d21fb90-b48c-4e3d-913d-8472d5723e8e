package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcProgramTvLive;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 电视视频节目Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface BcProgramTvLiveMapper extends GenericMapper<BcProgramTvLive> {

    IPage<BcProgramTvLive> searchProgramTvLive(IPage page, @Param("p") Map<String, Object> params);

    BcProgramTvLive findById(String id);
}
