package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcAnchorNews;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 主播动态Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface BcAnchorNewsMapper extends GenericMapper<BcAnchorNews> {

    IPage<BcAnchorNews> selectAnchorNewsPage(Page pagination, @Param("param") Map<String, Object> param);

    BcAnchorNews selectMoreById(Long id);
}
