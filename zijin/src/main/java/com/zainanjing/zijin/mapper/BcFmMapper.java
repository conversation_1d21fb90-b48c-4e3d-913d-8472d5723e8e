package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcFm;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 频率信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface BcFmMapper extends GenericMapper<BcFm> {

    IPage<BcFm> findFmListByMap(IPage page,@Param("paramMap") Map<String, Object> paramMap);
}
