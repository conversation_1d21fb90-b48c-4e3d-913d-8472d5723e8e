package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcAnchorNewsReply;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 主播动态回复Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface BcAnchorNewsReplyMapper extends GenericMapper<BcAnchorNewsReply> {

    IPage<BcAnchorNewsReply> selectAnchorNewsReplyPage(Page pagination, @Param("param") Map<String, Object> param);

    BcAnchorNewsReply selectMoreById(Long id);

    String selectNameByUserId(String anchorId);
}
