package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcGoods;
import com.zainanjing.zijin.dto.BcGoodsDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 爆品管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface BcGoodsMapper extends GenericMapper<BcGoods> {

    IPage<BcGoodsDTO> selectBcGoodsList(IPage<BcGoodsDTO> page, @Param("p") Map<String, Object> params);

}
