package com.zainanjing.zijin.mapper;

import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcProgramLiveGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 直播爆品管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface BcProgramLiveGoodsMapper extends GenericMapper<BcProgramLiveGoods> {

    List<BcProgramLiveGoods> searchGoodsList(@Param("id") String id, @Param("startInt") int startInt, @Param("pageSizeInt") int pageSizeInt);
}
