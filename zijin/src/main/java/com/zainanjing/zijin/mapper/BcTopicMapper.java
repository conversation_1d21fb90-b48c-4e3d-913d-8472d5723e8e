package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcTopic;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 广播话题Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface BcTopicMapper extends GenericMapper<BcTopic> {

    IPage<BcTopic> selectBcTopicsPage(Page pagination, @Param("param") Map<String, Object> param);

    void addTopicView(Long topicId);
}
