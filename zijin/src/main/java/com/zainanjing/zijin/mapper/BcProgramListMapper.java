package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcProgramList;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 直播节目单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface BcProgramListMapper extends GenericMapper<BcProgramList> {

    List<Integer> selectByFmId(@Param("fmId") Integer fmId);

    IPage<BcProgramList> selectBcProgramListPage(Page pagination, @Param("param") Map<String, Object> param);

    List<BcProgramList> selectByFmIdAndProgramId(@Param("channelId") Long channelId, @Param("idStrList") List<String> idStrList);

    List<BcProgramList> selectByBcListId(@Param("bcIdList") List<Long> bcIdList);

    List<BcProgramList> selectProgramLiveList(List<Long> ids);

    List<BcProgramList> selectProgramReplayList(@Param("start") Integer start, @Param("limit") Integer limit, @Param("codes") Set<String> codes);

    List<BcProgramList> selectOptionList(@Param("paramMap") Map<String, Object> paramMap, @Param("start") int start, @Param("pageSize") int maxValue);
}
