package com.zainanjing.zijin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcCommonModel;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.Map;

/**
 * 主播Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface BcAnchorMapper extends GenericMapper<BcAnchor> {

    IPage<BcAnchor> selectAnchorPage(Page pagination, @Param("param") Map<String, Object> param);

    BcCommonModel queryByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    BcAnchor selectMoreById(Long id);
}
