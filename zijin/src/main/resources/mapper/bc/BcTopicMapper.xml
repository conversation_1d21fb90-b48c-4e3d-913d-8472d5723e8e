<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcTopicMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcTopic" id="BcTopicResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="content" column="content"/>
        <result property="coverThumb" column="cover_thumb"/>
        <result property="view" column="view"/>
        <result property="status" column="status"/>
        <result property="forumId" column="forum_id"/>
        <result property="fmId" column="fm_id"/>
        <result property="programId" column="program_id"/>
        <result property="sort" column="sort"/>
        <result property="topicDate" column="topic_date"/>
        <result property="replyNum" column="reply_num"/>
        <result property="isComment" column="is_comment"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  name,  description,  content,  cover_thumb,  view,  status,  forum_id,  fm_id,  program_id,  sort,  topic_date,  reply_num,  is_comment     </sql>

    <update id="addTopicView">
        update bc_topic set view = view + 1 where id = #{topicId}
    </update>

    <select id="selectBcTopicsPage" parameterType="java.util.Map" resultMap="BcTopicResult">
        SELECT bt.*,
        bf.name AS forumName
        FROM bc_topic bt
        LEFT JOIN bc_forum bf ON bt.forum_id = bf.id
        <where>
            <if test="param.name != null and param.name != ''">
                AND bt.name like CONCAT('%',#{param.name},"%")
            </if>
            <if test="param.forumId != null  and param.forumId != ''">
                AND bf.id=#{param.forumId}
            </if>
            AND bt.status = 1
        </where>
        ORDER BY bt.id DESC
    </select>

</mapper>