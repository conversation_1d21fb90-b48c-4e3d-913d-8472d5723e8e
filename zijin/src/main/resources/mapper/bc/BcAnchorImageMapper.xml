<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcAnchorImageMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcAnchorImage" id="BcAnchorImageResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
        <result property="anchorId" column="anchor_id"/>
        <result property="imgName" column="img_name"/>
        <result property="imgUrl" column="img_url"/>
        <result property="isShow" column="is_show"/>
        <result property="sortNo" column="sort_no"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  is_del,  anchor_id,  img_name,  img_url,  is_show,  sort_no     </sql>

</mapper>