<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcCommentMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcComment" id="BcCommentResult">
        <result property="id" column="id"/>
        <result property="postId" column="post_id"/>
        <result property="uid" column="uid"/>
        <result property="content" column="content"/>
        <result property="ip" column="ip"/>
        <result property="region" column="region"/>
        <result property="isAudio" column="is_audio"/>
        <result property="audioId" column="audio_id"/>
        <result property="audioSecond" column="audio_second"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="parentId" column="parent_id"/>
        <result property="type" column="type"/>
        <result property="level" column="level"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditUid" column="audit_uid"/>
        <result property="remark" column="remark"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="forumId" column="forum_id"/>
        <result property="hasNewComment" column="has_new_comment"/>
        <result property="floor" column="floor"/>
        <result property="imgUrl" column="headImageAddress"/>
        <result property="avatarType" column="avatarType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  post_id,  uid,  content,  ip,  region,  is_audio,  audio_id,  audio_second,  audio_url,  parent_id,  type,  level,  create_time,  update_time,  status,  audit_time,  audit_uid,  remark,  is_anonymous,  forum_id,  has_new_comment,  floor     </sql>
    <update id="updateByMap">
        UPDATE bc_comment
        SET update_time = unix_timestamp(now())
        <if test="param.status != null and param.status != ''">
            , status = #{param.status}
        </if>
        <if test="param.floor != null and param.floor != ''">
            , floor = #{param.floor}
        </if>
        <if test="param.hasNewComment != null">
            , has_new_comment = #{param.hasNewComment}
        </if>
        <where>
            <if test="param.id != null">
                (id = #{param.id}
                <if test="param.parentId != null">
                    OR parent_id = #{param.parentId}
                </if>)
            </if>
            <if test="param.commentId != null">
                AND id = #{param.commentId}
            </if>
            <if test="param.uid != null">
                AND uid = #{param.uid}
            </if>
            <if test="param.postId != null">
                AND post_id = #{param.postId}
            </if>
        </where>
    </update>
    <select id="selectCommentPage" parameterType="java.util.Map" resultMap="BcCommentResult">
        SELECT bc.*,
        bp.content postContent,
        gu.user_name userName
        FROM bc_comment bc
        LEFT JOIN bc_post bp ON bc.post_id = bp.id
        LEFT JOIN gdmm_users gu ON gu.user_id = bc.uid
        <where>
            <if test="param.postContent != null and param.postContent != ''">
                AND bp.content like CONCAT('%',#{param.postContent},"%")
            </if>
            <if test="param.content != null and param.content != ''">
                AND bc.content like CONCAT('%',#{param.content},"%")
            </if>
            <if test="param.status != null and param.status !='' ">
                AND bc.status = #{param.status}
            </if>
            <if test="param.parentId != null and param.parentId !='' ">
                AND bc.parent_id = #{param.parentId}
            </if>
            <if test="param.type != null and param.type !='' ">
                AND bc.type = #{param.type}
            </if>
            <if test="param.level != null and param.level !='' ">
                AND bc.level = #{param.level}
            </if>
        </where>
        ORDER BY bc.id DESC
    </select>
    <select id="getMoreById" resultMap="BcCommentResult">
        SELECT bc.*,
               bp.content      postContent,
               gu.user_name    userName,
               gu.mobile_phone phone,
               bf.name         forumName
        FROM bc_comment bc
                 LEFT JOIN bc_post bp ON bc.post_id = bp.id
                 LEFT JOIN bc_forum bf ON bc.forum_id = bf.id
                 LEFT JOIN gdmm_users gu ON gu.user_id = bc.uid
        where bc.id = #{id}
    </select>
    <select id="selectImgByPostIdAndCommentId" resultType="java.lang.String">
        SELECT img_url
        FROM bc_img
        WHERE post_id = #{postId}
          AND comment_id = #{commentId}
    </select>
    <select id="selectFloor" resultType="java.lang.Integer">
        SELECT max(floor)
        FROM bc_comment
        WHERE post_id = #{postId}
          AND type = 1
          AND level = 1
    </select>

    <select id="search" resultType="com.zainanjing.zijin.domain.BcComment">
        SELECT
        (@rowno := @rowno + 1) AS rowno,
        bcr.*
        FROM
        (
        SELECT
        bc.*,
        eu.user_name,
        eu.medal_level,
        IF(ISNULL(bfu.forum_id), 0, 1) AS isHost,
        ea.address AS headImageAddress,
        ea.type AS avatarType
        FROM
        bc_comment bc
        LEFT JOIN gdmm_users eu ON bc.uid = eu.user_id
        LEFT JOIN bc_index_forum_user bfu ON bc.uid = bfu.uid
        LEFT JOIN gdmm_avatar ea ON bc.uid = ea.uid
        <where>
            <if test="param.postId != null and param.postId != ''">
                AND bc.post_id = #{param.postId}
            </if>
            <if test="param.type != null and param.type != ''">
                AND bc.type = #{param.type}
            </if>
            <if test="param.closeUids != null and param.closeUids.size() > 0">
                and bc.uid not in
                <foreach item="closeUid" collection="param.closeUids" open="(" separator="," close=")">
                    #{closeUid}
                </foreach>
            </if>
            <if test="param.verifyUid != null and param.verifyUid != ''">
                AND (bc.status = 0 OR (bc.status = 30 AND bc.uid = #{param.verifyUid}))
            </if>
            <if test="param.verifyUid == null or param.verifyUid == ''">
                <if test="param.uid != null and param.uid != ''">
                    AND bc.uid = #{param.uid}
                </if>
                <if test="param.status != null and param.status != ''">
                    AND bc.status = #{param.status}
                </if>
            </if>
            <if test="param.bcPostIdList != null and param.bcPostIdList != ''">
                AND bc.post_id IN
                <foreach collection="param.bcPostIdList" open="(" close=")" separator="," item="postId">
                    #{postId}
                </foreach>
            </if>
            <if test="param.orderBy != null and param.orderBy != ''">
                <choose>
                    <when test="param.orderBy == 'idDesc'">
                        ORDER BY id DESC
                    </when>
                    <when test="param.orderBy == 'idAsc'">
                        ORDER BY id ASC
                    </when>
                </choose>
            </if>
        </where>
        ) AS bcr,
        (SELECT (@rowno := 0)) AS b
    </select>

    <select id="searchPages" resultType="com.zainanjing.zijin.domain.BcComment">
        SELECT
        (@rowno := @rowno + 1) AS rowno,
        bcr.*
        FROM
        (
        SELECT
        bc.*,
        eu.user_name,
        eu.medal_level,
        IF(ISNULL(bfu.forum_id), 0, 1) AS isHost,
        ea.address AS headImageAddress,
        ea.type AS avatarType
        FROM
        bc_comment bc
        LEFT JOIN gdmm_users eu ON bc.uid = eu.user_id
        LEFT JOIN bc_index_forum_user bfu ON bc.uid = bfu.uid
        LEFT JOIN gdmm_avatar ea ON bc.uid = ea.uid
        <where>
            <if test="param.postId != null and param.postId != ''">
                AND bc.post_id = #{param.postId}
            </if>
            <if test="param.type != null and param.type != ''">
                AND bc.type = #{param.type}
            </if>
            <if test="param.closeUids != null and param.closeUids.size() > 0">
                and bc.uid not in
                <foreach item="closeUid" collection="param.closeUids" open="(" separator="," close=")">
                    #{closeUid}
                </foreach>
            </if>
            <if test="param.verifyUid != null and param.verifyUid != ''">
                AND (bc.status = 0 OR (bc.status = 30 AND bc.uid = #{param.verifyUid}))
            </if>
            <if test="param.verifyUid == null or param.verifyUid == ''">
                <if test="param.uid != null and param.uid != ''">
                    AND bc.uid = #{param.uid}
                </if>
                <if test="param.status != null and param.status != ''">
                    AND bc.status = #{param.status}
                </if>
            </if>
            <if test="param.bcPostIdList != null and param.bcPostIdList != ''">
                AND bc.post_id IN
                <foreach collection="param.bcPostIdList" open="(" close=")" separator="," item="postId">
                    #{postId}
                </foreach>
            </if>
            ORDER BY bc.id DESC
            <if test="start != null and pageSize != null">
                LIMIT #{start}, #{pageSize}
            </if>
        </where>
        ) AS bcr,
        (SELECT (@rowno := 0)) AS b
    </select>

    <select id="count" resultType="java.lang.Integer">
        select count(c.id)
        from bc_comment c
        left join bc_comment parent on c.parent_id = parent.id
        inner join bc_post p on c.post_id = p.id
        <where>
            <if test="param.uid != null and param.uid != ''">
                AND c.uid = #{param.uid}
            </if>
            <if test="param.status != null and param.status != ''">
                AND c.status = #{param.status}
            </if>
            <if test="param.postId != null and param.postId != ''">
                AND c.post_id = #{param.postId}
            </if>
            <if test="param.type != null and param.type != ''">
                AND c.type = #{param.type}
            </if>
            <if test="param.idForFloor != null and param.idForFloor != ''">
                <![CDATA[ AND c.id < #{param.idForFloor}]]>
            </if>
            <!-- 这里计算 所有回复我的帖子/评论 的评论 -->
            <if test="param.parentUid != null and param.parentUid != ''">
                AND ( p.uid = #{param.parentUid} or parent.uid= #{param.parentUid} )
            </if>
            <if test="param.hasNewComment != null and param.hasNewComment != ''">
                AND c.has_new_comment = #{param.hasNewComment}
            </if>
        </where>
    </select>

    <select id="selectMedalLevelByUid" resultType="java.lang.Integer">
        SELECT medal_level
        FROM gdmm_users
        WHERE user_id = #{uid}
    </select>
    <select id="selectUserNameByUid" resultType="java.lang.String">
        SELECT user_name
        FROM gdmm_users
        WHERE user_id = #{uid}
    </select>

    <select id="findEcsAvatarByUid" resultType="com.zainanjing.zijin.dto.EcsAvatar">
        select *
        from gdmm_avatar
        where uid = #{uid}
    </select>

</mapper>