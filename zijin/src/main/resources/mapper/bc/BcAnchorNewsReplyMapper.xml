<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcAnchorNewsReplyMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcAnchorNewsReply" id="BcAnchorNewsReplyResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
        <result property="parentEntityId" column="parent_entity_id"/>
        <result property="newsId" column="news_id"/>
        <result property="replyContent" column="reply_content"/>
        <result property="replyTime" column="reply_time"/>
        <result property="userId" column="user_id"/>
        <result property="fromUserName" column="from_user_name"/>
        <result property="toUserId" column="to_user_id"/>
        <result property="toUserName" column="to_user_name"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="isShow" column="is_show"/>
        <result property="isAudit" column="is_audit"/>
        <result property="auditTime" column="audit_time"/>
        <result property="replyType" column="reply_type"/>
        <result property="remark" column="remark"/>
        <result property="isAnonymousByTo" column="is_anonymous_by_to"/>
        <result property="isRead" column="is_read"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  is_del,  parent_entity_id,  news_id,  reply_content,  reply_time,  user_id,  from_user_name,  to_user_id,  to_user_name,  is_anonymous,  is_show,  is_audit,  audit_time,  reply_type,  remark,  is_anonymous_by_to,  is_read     </sql>
    <select id="selectAnchorNewsReplyPage" parameterType="java.util.Map" resultMap="BcAnchorNewsReplyResult" >
        SELECT anr.*,
        an.news_content
        FROM bc_anchor_news_reply anr
        LEFT JOIN bc_anchor_news an ON anr.news_id = an.id
        <where>
            <if test="param.newsContent != null and param.newsContent != ''">
                AND an.news_content like CONCAT('%',#{param.newsContent},"%")
            </if>
            <if test="param.replyContent != null and param.replyContent != ''">
                AND anr.reply_content like CONCAT('%',#{param.replyContent},"%")
            </if>
            <if test="param.anchorName != null and param.anchorName !='' ">
                AND anr.to_user_name like CONCAT('%',#{param.anchorName},"%")
            </if>
            <if test="param.userName != null and param.userName !='' ">
                AND anr.from_user_name like CONCAT('%',#{param.userName},"%")
            </if>
            <if test="param.isAudit != null and param.isAudit !='' ">
                AND anr.is_audit = #{param.isAudit}
            </if>

            AND anr.is_del = 0
        </where>
        ORDER BY anr.id DESC
    </select>
    <select id="selectMoreById" resultMap="BcAnchorNewsReplyResult">
        SELECT anr.*,
               an.news_content
        FROM bc_anchor_news_reply anr
                 LEFT JOIN bc_anchor_news an ON anr.news_id = an.id
        WHERE anr.id = #{id}
    </select>
    <select id="selectNameByUserId" resultType="java.lang.String">
        SELECT user_name
        FROM gdmm_users
        WHERE user_id = #{anchorId}
    </select>

</mapper>