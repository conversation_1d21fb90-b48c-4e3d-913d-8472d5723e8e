<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcAnchorFansMapper">

    <select id="selectAnchorListByFansIdPage" resultType="com.zainanjing.zijin.domain.BcAnchor">
        SELECT a.*
        FROM bc_anchor_fans af
                 INNER JOIN bc_anchor a ON af.anchor_id = a.user_id
        WHERE af.fans_id = #{fansId}
          AND a.is_del = 0
        ORDER BY af.id DESC
    </select>

</mapper>