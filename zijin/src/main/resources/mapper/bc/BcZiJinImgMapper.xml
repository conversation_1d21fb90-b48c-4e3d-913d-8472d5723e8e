<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcZiJinImgMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcZiJinImg" id="BcImgResult">
        <result property="id" column="id"/>
        <result property="postId" column="post_id"/>
        <result property="commentId" column="comment_id"/>
        <result property="type" column="type"/>
        <result property="imgUrl" column="img_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  post_id,  comment_id,  type,  img_url,  create_time,  update_time,  status,  width,  height,  sort
    </sql>

</mapper>