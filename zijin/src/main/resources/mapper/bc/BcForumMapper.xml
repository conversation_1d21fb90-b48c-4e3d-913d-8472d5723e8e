<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcForumMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcForum" id="BcForumResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="fmId" column="fm_id"/>
        <result property="programId" column="program_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="isRec" column="is_rec"/>
        <result property="description" column="description"/>
        <result property="isShow" column="is_show"/>
        <result property="status" column="status"/>
        <result property="isComment" column="is_comment"/>
        <result property="userName" column="user_name"/>
        <result property="chineseName" column="chinese_name"/>
    </resultMap>

    <resultMap id="bfForum2" extends="BcForumResult" type="com.zainanjing.zijin.domain.BcForum">
        <result property="forumId" column="forumId"/>
        <result property="programName" column="programName"/>
        <result property="fmName" column="fmName"/>
        <result property="programLogo" column="programLogo"/>
        <result property="isLive" column="isLive"/>
        <result property="url" column="url"/>
        <result property="isComment" column="is_comment"/>
        <result property="fmName" column="fmName"/>
        <result property="fmShortName" column="fmShortName"/>
        <result property="bcReplayListSize" column="bcReplayListSize"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  fm_id,  program_id,  code,  name,  logo,  is_rec,  description,  is_show,  status,  is_comment,  user_name,  chinese_name     </sql>
    <select id="selectBcForumPage" resultType="com.zainanjing.zijin.domain.BcForum">
        SELECT
        bf.*,
        fm.NAME AS fmName,
        bg.NAME AS programListName
        FROM
        bc_forum bf
        LEFT JOIN bc_fm fm ON bf.fm_id = fm.id
        LEFT JOIN bc_program_list bpl ON bf.program_id = bpl.id
        LEFT JOIN bc_program bg ON bg.id = bpl.program_id
        <where>
            <if test="param.name != null and param.name != ''">
                AND bf.name LIKE CONCAT('%', #{param.name}, '%')
            </if>
            <if test="param.fmName != null and param.fmName != ''">
                AND fm.name LIKE CONCAT('%', #{param.fmName}, '%')
            </if>
            <if test="param.isShow != null and param.isShow !='' ">
                AND bf.is_show = #{param.isShow}
            </if>
            AND bf.`status` = 0
        </where>
        ORDER BY bf.id DESC
    </select>

    <select id="selectOptionalList" resultType="com.zainanjing.zijin.domain.BcForum">
        select
        DISTINCT
        (
        case
        when (select bvc.is_comment from bc_verify_config bvc where bvc.verify_type = 2) = 0 then 1
        when (select bvc.is_comment from bc_verify_config bvc where bvc.verify_type = 2) = 1 then m.is_comment
        end
        ) as is_comment,m.*,
        m.id as forumId,
        p.name as programName,
        m.logo as programLogo,
        f.name as fmName,
        f.short_name as fmShortName,
        (select count(*) from bc_post where type=3 and status=0 and forum_id = m.id) as bcReplayListSize,
        (select start_time from bc_program_list where `status`=0 and is_open = 1 and fm_id = m.fm_id and id =
        m.program_id) as start_time,
        (select end_time from bc_program_list where `status`=0 and is_open = 1 and fm_id = m.fm_id and id =
        m.program_id) as end_time,
        case when(select count(*)
        from bc_program_list pl
        where #{paramMap.currDate} >= pl.start_time and pl.end_time > #{paramMap.currDate} and
        find_in_set(#{paramMap.currWeek},pl.week) and pl.id =
        m.program_id and pl.fm_id=m.fm_id and pl.status=0 and pl.is_open=1) > 0 then 1
        else 0 end as isLive,
        '' as url
        from
        bc_forum m
        left join bc_fm f on m.fm_id = f.id and m.is_show = 1 and m.status = 0 and f.status = 0
        left join bc_program_list bpl on ( bpl.id = m.program_id and bpl.fm_id=m.fm_id and bpl.status=0 and
        bpl.is_open=1 )
        left join bc_program p on bpl.program_id = p.id
        where
        m.is_show=1
        and m.status=0 and p.status=0 and p.is_show=1 and bpl.status=0 and bpl.is_open=1
        <if test="paramMap.isRec != null and paramMap.isRec != ''">
            and m.is_rec = #{paramMap.isRec}
        </if>
        <if test="paramMap.fmId != null and paramMap.fmId != ''">
            and m.fm_id = #{paramMap.isRec}
        </if>
        <if test="paramMap.keyword != null and paramMap.keyword != ''">
            and m.name like CONCAT('%',#{paramMap.keyword},'%')
        </if>
        and (LENGTH(m.code)>0 or (LENGTH(m.code)=0 and (select count(*)
        from bc_program_list pl
        where #{paramMap.currDate} >= pl.start_time and pl.end_time > #{paramMap.currDate} and
        find_in_set(#{paramMap.currWeek},pl.week) and
        pl.program_id = m.program_id and pl.fm_id=m.fm_id and pl.status=0 and pl.is_open=1) > 0))
        order by bpl.start_time
    </select>

    <select id="findAnchorByForumId" resultType="com.zainanjing.zijin.domain.BcAnchor">
        select an.id,
               an.anchor_name,
               an.user_id,
               an.anchor_img_url AS heardImage
        from bc_forum bf
                 left join bc_program_list bpl on bf.program_id = bpl.id
                 left join bc_anchor_relation bar on bpl.id = bar.bc_id
                 left join bc_anchor an on bar.anchor_id = an.user_id and is_del = 0
                 left join gdmm_avatar av on av.uid = an.user_id
        where bf.id = #{id}
    </select>

</mapper>