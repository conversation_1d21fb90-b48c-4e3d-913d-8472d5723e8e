<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcChoiceCommentImgMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcChoiceCommentImg" id="BcChoiceCommentImgResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="commentId" column="comment_id"/>
        <result property="imgUrl" column="img_url"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  comment_id,  img_url,  width,  height,  sort,  status     </sql>

</mapper>