<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcProgramLiveMapper">

    <select id="search" resultType="com.zainanjing.zijin.domain.BcProgramLive">
        <![CDATA[
        select a.live_status as status,
        (
            case
            when (select bvc.is_comment from bc_verify_config bvc where bvc.verify_type = 5) = 0 then 1
            when (select bvc.is_comment from bc_verify_config bvc where bvc.verify_type = 5) = 1 then a.is_comment
            end
        ) as is_comment,
        a.*,(a.actual_view_times*a.view_times) as showView,litype.name as typeName,
        litype.short_name as typeShortName,
        if( IFNULL(co.`go_count`,0) != 0 and a.`live_status` = 1, 1, 0 ) AS `isShowGoodsBtn`
        from ( select * ,case
            when is_show = 1 and is_live =1 and (start_time < #{p.now} and end_time > #{p.now} ) then 1
            when is_show = 1 and is_live =1 and start_time > #{p.now} then 2
            when is_show = 1 and is_live =1 and end_Time < #{p.now} then 3
            when is_show = 1 and is_live = 0 then 4
            end as live_status
        from bc_program_live
        where status=0)a
        left join bc_program_live_type litype on a.type_id = litype.id
        left join (
            SELECT
                lg.`program_live_id`,
                count( 1 ) as `go_count`
            FROM
                bc_program_live_goods lg
            where
                lg.`is_del` = 0
                AND lg.`is_show` = 'Y'
            GROUP BY
                lg.`program_live_id`
        ) co on co.`program_live_id` = a.`id`
    ]]>
        <where>
            <if test="p.liveStatus != null and p.liveStatus.length > 0">
                AND a.live_status in
                <foreach collection="liveStatus" item="status" open="(" close=")" separator=",">
                    #{p.status}
                </foreach>
            </if>
            <if test="p.fmId != null">
                AND a.fm_id = #{p.fmId}
            </if>
            <if test="p.id != null">
                AND a.id = #{p.id}
            </if>
            <if test="p.typeId != null">
                AND a.type_id = #{p.typeId}
            </if>
            <if test="p.typeChannel != null">
                AND litype.channel = #{p.typeChannel}
            </if>
            <if test="p.keyword != null">
                AND a.name like CONCAT('%',#{p.keyword},'%')
            </if>
            <if test="p.isShow != null">
                AND a.is_show = #{p.isShow}
            </if>
            <if test="p.endTime != null">
                <![CDATA[ AND a.end_time > #{p.endTime} ]]>
            </if>
            <if test="p.isLive != null">
                <![CDATA[ AND a.is_live = #{p.isLive} ]]>
            </if>
            <if test="p.flag != null and p.flag == '1'">
                <![CDATA[ AND (a.end_time >= unix_timestamp() and a.start_time <= unix_timestamp() and a.is_live=1) ]]>
            </if>
        </where>
        order by a.is_top desc, a.live_order desc, a.id desc
    </select>

</mapper>