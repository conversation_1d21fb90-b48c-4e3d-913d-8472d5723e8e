<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcExtMapper">

    <select id="selectGoodsByGoodIds" resultType="com.zainanjing.zijin.dto.GdmmGoodsDTO" parameterType="java.util.List">
        SELECT
        id, name
        FROM
        gdmm_goods
        WHERE
        id IN
        <foreach collection="goodsIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectUsers" resultType="com.zainanjing.zijin.dto.GdmmUsersDTO">
        SELECT
        gu.user_id, gu.user_name, gu.medal_level,ga.address as avatar,gu.headImgUrl,ga.type as avatarType
        FROM
        gdmm_users gu left join gdmm_avatar ga on gu.user_id = ga.uid
        WHERE
        1 = 1
        <if test="p.uid != null">
          AND user_id = #{p.uid}
        </if>
        <if test="p.uids != null and p.uids.size() > 0">
            AND user_id IN
            <foreach item="id" collection="p.uids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="searchSiteCollect" resultType="com.zainanjing.zijin.dto.SiteCollectDTO">
        SELECT
        collect_id as siteCollectId,
        uid as siteMemberId,
        ctime as collectTime,
        '' as tranName,
        '' as ispromote,
        itemid as collectObjectId,
        collect_type as collectObjectType,
        '' as goodsbrief,
        '' as url,
        '' as startTime,
        '' as entTime,
        '' as price,
        '' as original,
        '' as marketprice,
        '' as discount,
        '' as description,
        itemname as title,
        '' as content
        FROM
        gdmm_collect
        WHERE
        is_del = 0
        <if test="filterMap.ctype != null and filterMap.ctype != ''">
            AND collect_type = #{filterMap.ctype}
        </if>
        <if test="filterMap.siteMemberId != null and filterMap.siteMemberId != ''">
            AND uid = #{filterMap.siteMemberId}
        </if>
        <if test="filterMap.collectObjectId != null and filterMap.collectObjectId != ''">
            AND itemid = #{filterMap.collectObjectId}
        </if>
        ORDER BY
        collect_id DESC
    </select>
    <select id="getSensitiveWords" resultType="java.lang.String">
        select name from gdmm_special_word
    </select>

</mapper>