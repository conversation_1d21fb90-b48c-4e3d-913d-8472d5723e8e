<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcAdvMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcAdv" id="BcAdvResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="title" column="title"/>
        <result property="area" column="area"/>
        <result property="type" column="type"/>
        <result property="imgUrl" column="imgUrl"/>
        <result property="linkType" column="link_type"/>
        <result property="linkModule" column="link_module"/>
        <result property="resourceId" column="resource_id"/>
        <result property="linkTo" column="link_to"/>
        <result property="linkUrl" column="link_url"/>
        <result property="isShow" column="is_show"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="resourceParamIds" column="resource_param_ids"/>
    </resultMap>

    <resultMap id="bcAdv2" extends="BcAdvResult"
               type="com.zainanjing.zijin.domain.BcAdv">
        <result property="advId" column="advId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  name,  title,  area,  type,  imgUrl,  link_type,  link_module,  resource_id,  link_to,  link_url,  is_show,  sort,  create_time,  update_time,  status,  resource_param_ids     </sql>

    <select id="selectOptionalList" resultMap="bcAdv2">
        select a.*,
        a.id as advId
        from
        bc_adv a
        where
        a.status = 0
        and a.is_show = 1
        <if test="type != null and type != ''">
            AND a.type = #{type}
        </if>
        <if test="area != null and area != ''">
            AND a.area = #{area}
        </if>
        order by a.sort desc,a.create_time desc
        limit 0, 8
    </select>
</mapper>