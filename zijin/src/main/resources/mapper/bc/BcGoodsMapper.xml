<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcGoodsMapper">

    <select id="selectBcGoodsList" resultType="com.zainanjing.zijin.dto.BcGoodsDTO">
        SELECT
        g.*,
        gg.id AS goodsId,
        gg.title AS goodsDesc,
        gg.name AS goodsName,
        gg.price AS goodsSellPrice,
        gg.market_price AS goodsPrice,
        g.img_url AS goodsImgUrl,
        gg.salenum AS orderCnt,
        a.anchor_name AS anchorName,
        f.id AS bcId,
        f.name AS bcName
        FROM
        bc_goods g
        LEFT JOIN
        bc_forum f ON g.forum_id = f.id
        LEFT JOIN
        bc_anchor a ON g.anchor_id = a.user_id
        LEFT JOIN
        gdmm_goods gg ON g.goods_res_id = gg.id
        WHERE
        1=1
        <if test="p.anchorUid != null">
            AND g.anchor_id = #{p.anchorUid}
        </if>
        <if test="p.isRecommandAnchor != null and p.isRecommandAnchor != ''">
            AND g.is_recommand_anchor = #{p.isRecommandAnchor}
        </if>
        <if test="p.name != null and p.name != ''">
            AND gg.name LIKE CONCAT('%', #{p.name}, '%')
        </if>
        <if test="p.programName != null and p.programName != ''">
            AND f.name LIKE CONCAT('%', #{p.programName}, '%')
        </if>
        <if test="p.anchorName != null and p.anchorName != ''">
            AND a.anchor_name LIKE CONCAT('%', #{p.anchorName}, '%')
        </if>
        <if test="p.isShow != null">
            AND g.is_show = #{p.isShow}
        </if>
        <if test="p.isDel != null">
            AND g.is_del = #{p.isDel}
        </if>
        <if test="p.entityId != null">
            <if test="p.goodsType == 1">
                AND g.anchor_id = #{p.entityId}
            </if>
            <if test="p.goodsType != 1">
                AND g.forum_id = #{p.entityId}
            </if>
        </if>
        <if test="p.goodsIds != null and p.goodsIds.size() > 0">
            AND gg.id IN
            <foreach item="id" collection="p.goodsIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY
        g.sort_no DESC, g.id DESC
    </select>

</mapper>