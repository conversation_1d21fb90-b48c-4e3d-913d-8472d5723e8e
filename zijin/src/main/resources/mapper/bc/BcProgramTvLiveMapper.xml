<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcProgramTvLiveMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcProgramTvLive" id="ResultMap">
        <result property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="fmId" column="fm_id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="openType" column="open_type"/>
        <result property="linkUrl" column="link_url"/>
        <result property="url" column="url"/>
        <result property="description" column="description"/>
        <result property="view" column="view"/>
        <result property="number" column="number"/>
        <result property="interact" column="interact"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isShow" column="is_show"/>
        <result property="shareImg" column="share_img"/>
        <result property="isLive" column="is_live"/>
        <result property="status" column="status"/>
        <result property="liveOrder" column="live_order"/>
        <result property="topicid" column="topicid"/>
        <result property="title" column="title"/>
        <result property="isComment" column="is_comment"/>
        <result property="viewTimes" column="view_times"/>
        <result property="maxView" column="max_view"/>
        <result property="actualViewTimes" column="actual_view_times"/>
    </resultMap>

    <select id="searchProgramTvLive" resultMap="ResultMap">
        <![CDATA[
        SELECT(
                  CASE
                      WHEN (SELECT bvc.is_comment FROM bc_verify_config bvc WHERE bvc.verify_type = 5) = 0 THEN 1
                      WHEN (SELECT bvc.is_comment FROM bc_verify_config bvc WHERE bvc.verify_type = 5) = 1
                          THEN bc_program_tv_live.is_comment
                      END)                     AS is_comment,
              bc_program_tv_live.*,
              (actual_view_times * view_times) AS showView,
              CASE
                  WHEN is_show = 1 AND is_live = 1 AND (start_time < #{p.now} AND end_time > #{p.now}) THEN 1
                  WHEN is_show = 1 AND is_live = 1 AND start_time > #{p.now} THEN 2
                  WHEN is_show = 1 AND is_live = 1 AND end_time < #{p.now} THEN 3
                  WHEN is_show = 1 AND is_live = 0 THEN 4
                  END                          AS live_status
        FROM bc_program_tv_live
            ]]>
        <where>
            <if test="p.typeId != null and p.typeId != ''">
                type_id = #{p.typeId}
            </if>
            <if test="p.status != null">
                AND status = #{p.status}
            </if>
            <if test="p.isShow != null">
                AND is_show = #{p.isShow}
            </if>
            <if test="p.keyword != null and p.keyword != ''">
                AND name LIKE CONCAT('%', #{p.keyword}, '%')
            </if>
        </where>
        order by live_order desc, id desc
    </select>

    <select id="findById" resultMap="ResultMap">
       <![CDATA[
        SELECT (case
                    when (select bvc.is_comment from bc_verify_config bvc where bvc.verify_type = 5) = 0 then 1
                    when (select bvc.is_comment from bc_verify_config bvc where bvc.verify_type = 5) = 1
                        then bc_program_tv_live.is_comment
            end
                   ) as is_comment,
               bc_program_tv_live.*
        FROM bc_program_tv_live
        WHERE id = #{id}
        ]]>
    </select>
</mapper>