<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcCommentImageMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcCommentImage" id="BcCommentImageResult">
        <result property="id" column="id"/>
        <result property="refId" column="ref_id"/>
        <result property="refType" column="ref_type"/>
        <result property="imgUrl" column="img_url"/>
        <result property="imgWidth" column="img_width"/>
        <result property="imgHeight" column="img_height"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  ref_id,  ref_type,  img_url,  img_width,  img_height,  create_time,  update_time     </sql>

</mapper>