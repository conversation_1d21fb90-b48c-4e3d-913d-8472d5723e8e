<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcChoiceCommentMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcChoiceComment" id="BcChoiceCommentResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="parentId" column="parent_id"/>
        <result property="albumId" column="album_id"/>
        <result property="choiceId" column="choice_id"/>
        <result property="userId" column="user_id"/>
        <result property="content" column="content"/>
<!--        <result property="ip" column="ip"/>-->
<!--        <result property="region" column="region"/>-->
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="isShow" column="is_show"/>
        <result property="status" column="status"/>
        <result property="replyNums" column="reply_nums"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  parent_id,  album_id,  choice_id,  user_id,  content,  ip,  region,  is_anonymous,  is_show,  status,  reply_nums,  remark     </sql>
    <select id="selectChoiceCommentPage" parameterType="java.util.Map" resultMap="BcChoiceCommentResult">
        SELECT c.*,
        a.name AS albumName,
        cc.name AS choiceName,
        gu.user_name AS userName
        FROM bc_choice_comment c
        LEFT JOIN bc_choice_album a ON c.album_id = a.id
        LEFT JOIN bc_choice cc ON c.choice_id = cc.id
        LEFT JOIN gdmm_users gu ON c.user_id = gu.user_id
        <where>
            <if test="param.content != null and param.content != ''">
                AND c.content like CONCAT('%',#{param.content},"%")
            </if>
            <if test="param.userName != null and param.userName !='' ">
                AND gu.user_name = #{param.userName}
            </if>
            <if test="param.albumName != null and param.albumName !='' ">
                AND a.name = #{param.albumName}
            </if>
            <if test="param.choiceName != null and param.choiceName !='' ">
                AND cc.name = #{param.choiceName}
            </if>
            <if test="param.isShow != null and param.isShow !='' ">
                AND c.is_show = #{param.isShow}
            </if>
            <if test="param.status != null and param.status !='' ">
                AND c.status = #{param.status}
            </if>
        </where>
        ORDER BY id DESC
    </select>
    <select id="getMoreById" resultMap="BcChoiceCommentResult">
        SELECT c.*,
               a.name       AS albumName,
               cc.name      AS choiceName,
               gu.user_name AS userName
        FROM bc_choice_comment c
                 LEFT JOIN bc_choice_album a ON c.album_id = a.id
                 LEFT JOIN bc_choice cc ON c.choice_id = cc.id
                 LEFT JOIN gdmm_users gu ON c.user_id = gu.user_id
        WHERE c.id = #{id}
    </select>
    <select id="selectUserNameById" resultType="java.lang.String">
        SELECT user_name
        FROM gdmm_users
        WHERE user_id = #{userId}
    </select>
</mapper>