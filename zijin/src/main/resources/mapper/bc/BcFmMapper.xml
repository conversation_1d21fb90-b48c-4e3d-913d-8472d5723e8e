<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcFmMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcFm" id="BcFmResultMap">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
        <result property="shortName" column="short_name"/>
        <result property="channel" column="channel"/>
        <result property="m3u8" column="m3u8"/>
        <result property="mms" column="mms"/>
        <result property="description" column="description"/>
        <result property="url" column="url"/>
        <result property="logo" column="logo"/>
        <result property="openType" column="open_type"/>
        <result property="linkUrl" column="link_url"/>
        <result property="isShow" column="is_show"/>
        <result property="isRec" column="is_rec"/>
        <result property="sort" column="sort"/>
        <result property="channelId" column="channel_id"/>
        <result property="isLive" column="is_live"/>
        <result property="isUnicast" column="is_unicast"/>
        <result property="status" column="status"/>
    </resultMap>
    <resultMap id="bfFm2" extends="BcFmResultMap" type="com.zainanjing.zijin.domain.BcFm">
        <result property="fmId" column="fmId"/>
        <result property="programName" column="programName"/>
    </resultMap>

    <select id="findFmListByMap" parameterType="java.util.Map" resultMap="bfFm2">
        select
        f.*,
        f.id as fmId,
        p.name as programName
        from
        bc_fm f
        left join bc_program_list l on f.id=l.fm_id and f.is_show=1 and f.status = 0
        and #{paramMap.currDate} >= l.start_time and l.end_time >= #{paramMap.currDate} and find_in_set(#{paramMap.currWeek},l.week) and l.is_open=1
        and l.status=0
        left join bc_program p on l.program_id = p.id
        left join bc_forum u on l.program_id = u.program_id and l.fm_id = u.fm_id
        where
        f.status = 0 and f.is_show = 1
        <if test="paramMap.isRec != null and paramMap.isRec != ''">
            and f.is_rec = #{paramMap.isRec}
        </if>
        <if test="paramMap.keyword != null and paramMap.keyword != ''">
            and f.name like CONCAT('%',#{paramMap.keyword},'%')
        </if>
        group by f.id
        order by f.sort desc
    </select>


</mapper>