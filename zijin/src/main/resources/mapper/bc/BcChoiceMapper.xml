<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcChoiceMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcChoice" id="BcChoiceResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="albumId" column="album_id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="logo" column="logo"/>
        <result property="url" column="url"/>
        <result property="duration" column="duration"/>
        <result property="seconds" column="seconds"/>
        <result property="view" column="view"/>
        <result property="virtualView" column="virtual_view"/>
        <result property="isShow" column="is_show"/>
        <result property="sort" column="sort"/>
        <result property="shareIcon" column="share_icon"/>
        <result property="shareTitle" column="share_title"/>
        <result property="shareDesc" column="share_desc"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="idForThird" column="id_for_third"/>
        <result property="isRecommend" column="is_recommend"/>
        <result property="cardid" column="cardid"/>
        <result property="createUid" column="create_uid"/>
        <result property="approveStatus" column="approve_status"/>
        <result property="checkStatus" column="check_status"/>
        <result property="isPublish" column="is_publish"/>
        <result property="approveMember" column="approve_member"/>
        <result property="checkWorkFlow" column="check_work_flow"/>
    </resultMap>

    <resultMap type="com.zainanjing.zijin.dto.BcChoiceRankDTO" id="BcChoiceRankResult">
        <result property="id" column="id"/>
        <result property="create_time" column="create_time"/>
        <result property="album_id" column="album_id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="logo" column="logo"/>
        <result property="url" column="url"/>
        <result property="duration" column="duration"/>
        <result property="seconds" column="seconds"/>
        <result property="view" column="view"/>
        <result property="virtual_view" column="virtual_view"/>
        <result property="isShow" column="is_show"/>
        <result property="sort" column="sort"/>
        <result property="share_icon" column="share_icon"/>
        <result property="share_title" column="share_title"/>
        <result property="share_desc" column="share_desc"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="total_view" column="total_view"/>
        <result property="commentNum" column="comment_num"/>
        <result property="isComment" column="is_comment"/>
        <result property="seconds" column="seconds"/>
        <result property="album_name" column="album_name"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  album_id,  name,  description,  logo,  url,  duration,  seconds,  view,  virtual_view,  is_show,  sort,  share_icon,  share_title,  share_desc,  type,  status,  id_for_third,  is_recommend,  cardid,  create_uid,  approve_status,  check_status,  is_publish,  approve_member,  check_work_flow     </sql>

    <select id="selectRankList" resultMap="BcChoiceRankResult">
        SELECT
        bc.id,
        bc.create_time,
        bc.album_id,
        bc.name,
        bc.description,
        bc.share_icon,
        bc.logo,
        bc.url,
        bc.view,
        bc.virtual_view,
        bc.duration,
        bc.seconds,
        SUM(bcs.total_view) AS total_view
        FROM bc_choice_statistics bcs
        LEFT JOIN bc_choice bc ON bc.id = bcs.choice_id
        <where>
            <if test="type != null">
                AND bcs.type = #{type}
            </if>
            <if test="unixMonthTime != null">
                AND bcs.unix_month_time = #{unixMonthTime}
            </if>
            AND bc.is_show = '1'
            AND bc.status = '1'
        </where>
        GROUP BY bcs.choice_id
        ORDER BY total_view DESC, bcs.choice_id DESC
        limit ${start},${pageSize}
    </select>

</mapper>