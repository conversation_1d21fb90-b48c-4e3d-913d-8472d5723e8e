<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcAnchorNewsMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcAnchorNews" id="BcAnchorNewsResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
        <result property="anchorId" column="anchor_id"/>
        <result property="newsContent" column="news_content"/>
        <result property="coverFileUrl" column="cover_file_url"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileDuration" column="file_duration"/>
        <result property="visibleType" column="visible_type"/>
        <result property="soureType" column="soure_type"/>
        <result property="compNum" column="comp_num"/>
        <result property="isShow" column="is_show"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  is_del,  anchor_id,  news_content,  cover_file_url,  file_url,  file_duration,  visible_type,  soure_type,  comp_num,  is_show     </sql>
    <select id="selectAnchorNewsPage" parameterType="java.util.Map" resultMap="BcAnchorNewsResult">
        SELECT ban.*,
        ba.anchor_name AS anchorName,
        ba.anchor_img_url as anchorImgUrl,
        bf.name AS fmName,
        (SELECT count(1)
        FROM bc_anchor_news_reply banr
        WHERE banr.news_id = ban.id AND is_audit = 1 AND is_show = 1) AS conmmentNumb
        FROM bc_anchor_news ban
        LEFT JOIN bc_anchor ba ON ba.user_id = ban.anchor_id
        LEFT JOIN bc_fm bf ON bf.id = ba.channel_id
        <where>
            <if test="param.id != null and param.id != ''">
                AND ban.id = #{param.id}
            </if>
            <if test="param.anchorUid != null and param.anchorUid != ''">
                AND ban.anchor_id = #{param.anchorUid}
            </if>
            <if test="param.visibleType != null and param.visibleType != ''">
                AND ban.visible_type = #{param.visibleType}
            </if>
            <if test="param.newsContent != null and param.newsContent != ''">
                AND ban.news_content like CONCAT('%',#{param.newsContent},"%")
            </if>
            <if test="param.anchorName != null and param.anchorName != ''">
                AND ba.anchor_name like CONCAT('%',#{param.anchorName},"%")
            </if>
            <if test="param.isShow != null and param.isShow !='' ">
                AND ban.is_show = #{param.isShow}
            </if>
            <if test="param.soureType != null and param.soureType !='' ">
                AND ban.soure_type = #{param.soureType}
            </if>
            <if test="param.startTime != null and param.endTime != null and param.startTime != '' and param.endTime != '' ">
                AND ban.create_time between #{param.startTime} and #{param.endTime}
            </if>
        </where>
        ORDER BY ban.create_time DESC
    </select>
    <select id="selectMoreById" resultMap="BcAnchorNewsResult">
        SELECT ban.*,
               ba.anchor_name      AS anchorName,
               bf.name             AS fmName,
               (SELECT count(1)
                FROM bc_anchor_news_reply banr
                WHERE banr.news_id = ban.id
                  AND is_audit = 1
                  AND is_show = 1) AS conmmentNumb
        FROM bc_anchor_news ban
                 LEFT JOIN bc_anchor ba ON ba.user_id = ban.anchor_id
                 LEFT JOIN bc_fm bf ON bf.id = ba.channel_id
        WHERE ban.id = #{id}
    </select>

</mapper>