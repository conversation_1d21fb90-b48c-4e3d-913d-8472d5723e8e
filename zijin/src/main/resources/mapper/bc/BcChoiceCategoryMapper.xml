<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcChoiceCategoryMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcChoiceCategory" id="BcChoiceCategoryResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="logo" column="logo"/>
        <result property="isShow" column="is_show"/>
        <result property="url" column="url"/>
        <result property="sort" column="sort"/>
        <result property="shareIcon" column="share_icon"/>
        <result property="shareTitle" column="share_title"/>
        <result property="shareDesc" column="share_desc"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  name,  description,  logo,  is_show,  url,  sort,  share_icon,  share_title,  share_desc,  status,  type     </sql>

</mapper>