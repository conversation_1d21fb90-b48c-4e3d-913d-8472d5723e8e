<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcProgramListMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcProgramList" id="BcProgramListResult">
        <result property="id" column="id"/>
        <result property="programId" column="program_id"/>
        <result property="fmId" column="fm_id"/>
        <result property="columnId" column="column_id"/>
        <result property="code" column="code"/>
        <result property="week" column="week"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isOpen" column="is_open"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  program_id,  fm_id,  column_id,  code,  week,  start_time,  end_time,  create_time,  update_time,  is_open,  status     </sql>

    <select id="selectByFmId" resultType="java.lang.Integer">
        SELECT program_id
        FROM `bc_program_list`
        WHERE fm_id = #{fmId}
        ORDER BY id DESC
    </select>

    <select id="selectBcProgramListPage" parameterType="java.util.Map" resultMap="BcProgramListResult">
        SELECT bpl.*,
        bp.name AS programName,
        bp.is_show AS isShow,
        bf.name AS fmName
        FROM bc_program_list bpl
        LEFT JOIN bc_program bp ON bpl.program_id = bp.id
        LEFT JOIN bc_fm bf ON bpl.fm_id = bf.id
        <where>
            <if test="param.programName != null and param.programName != ''">
                AND bp.name like CONCAT('%',#{param.programName},"%")
            </if>
            <if test="param.fmName != null  and param.fmName != ''">
                AND bf.name like CONCAT('%',#{param.fmName},"%")
            </if>
            <if test="param.fmId != null  and param.fmId != ''">
                AND bpl.fm_id = #{param.fmId}
            </if>
            AND bpl.status = 0
        </where>
        ORDER BY bpl.id DESC
    </select>

    <select id="selectByFmIdAndProgramId" resultType="com.zainanjing.zijin.domain.BcProgramList">
        SELECT *
        FROM bc_program_list
        WHERE fm_id = #{channelId}
        AND program_id IN
        <foreach item="id" collection="idStrList" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status = 0
    </select>

    <select id="selectByBcListId" resultType="com.zainanjing.zijin.domain.BcProgramList">
        SELECT *
        FROM bc_program_list
        WHERE
        id IN
        <foreach item="id" collection="bcIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--    查一下主播关联的节目正在直播的 -->
    <select id="selectProgramLiveList" resultType="com.zainanjing.zijin.domain.BcProgramList">
        SELECT pl.*,p.name as programName, f.logo,f.description,f.id as forumId,fm.name as fmName,fm.url as fmUrl, 0 as status
        FROM bc_program_list pl
        INNER JOIN bc_program p ON pl.program_id = p.id AND p.is_show = 1
        AND p.status = 0
        INNER JOIN bc_forum f ON pl.fm_id = f.fm_id and pl.id = f.program_id and f.status = 0 and f.is_show = 1
        LEFT JOIN bc_fm fm ON pl.fm_id = fm.id
        WHERE pl.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND FIND_IN_SET(DAYOFWEEK(NOW()), week)
        AND pl.start_time &lt;= DATE_FORMAT(NOW(), '%H:%i')
        AND pl.end_time >= DATE_FORMAT(NOW(), '%H:%i')
        AND pl.is_open = 1
        AND pl.status = 0
    </select>

    <!--    查一下主播关联的节目回放的 -->
    <select id="selectProgramReplayList" resultType="com.zainanjing.zijin.domain.BcProgramList">
        SELECT pl.id,pl.fm_id,r.start_date,r.start_time,r.end_time, p.name as programName, f.logo, f.description, f.id as
        forumId, fm.name as fmName, r.url as fmUrl, 1 as status
        FROM bc_program_list pl
        INNER JOIN bc_program p ON pl.program_id = p.id AND p.is_show = 1
        AND p.status = 0
        INNER JOIN bc_forum f ON pl.fm_id = f.fm_id and pl.id = f.program_id and f.status = 0 and f.is_show = 1
        INNER JOIN bc_replay r ON r.code = f.code
        LEFT JOIN bc_fm fm ON pl.fm_id = fm.id
        WHERE r.code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        order by r.start_date DESC, r.start_time ASC, r.id DESC
        limit ${start},${limit}
    </select>

    <select id="selectOptionList" resultType="com.zainanjing.zijin.domain.BcProgramList">
        SELECT
        l.*
        FROM
        bc_program_list l
        LEFT JOIN bc_program bp ON l.program_id = bp.id
        WHERE
        l.status = 0 AND bp.`status` = 0
        <if test="paramMap.fmId != null and paramMap.fmId != ''">
            AND l.fm_id = #{paramMap.fmId}
        </if>
        <if test="paramMap.currWeek != null and paramMap.currWeek != ''">
            AND find_in_set(#{paramMap.currWeek}, l.week)
        </if>
        <if test="paramMap.currDate != null">
            AND #{paramMap.currDate} >= l.start_time AND l.end_time > #{paramMap.currDate}
        </if>
        <if test="paramMap.programId != null">
            AND l.program_id = #{paramMap.programId}
        </if>
        <if test="paramMap.id != null">
            AND l.id = #{paramMap.id}
        </if>
        ORDER BY l.start_time
        <if test="start != null and pageSize != null">
            LIMIT #{start}, #{pageSize}
        </if>
    </select>
</mapper>