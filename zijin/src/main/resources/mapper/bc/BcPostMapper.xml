<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcPostMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcPost" id="BcPostResult">
        <result property="id" column="id"/>
        <result property="forumId" column="forum_id"/>
        <result property="programId" column="program_id"/>
        <result property="topicId" column="topic_id"/>
        <result property="subject" column="subject"/>
        <result property="content" column="content"/>
        <result property="ip" column="ip"/>
        <result property="region" column="region"/>
        <result property="color" column="color"/>
        <result property="uid" column="uid"/>
        <result property="clickNum" column="click_num"/>
        <result property="commentNum" column="comment_num"/>
        <result property="praiseNum" column="praise_num"/>
        <result property="type" column="type"/>
        <result property="url" column="url"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="sort" column="sort"/>
        <result property="isRec" column="is_rec"/>
        <result property="isTop" column="is_top"/>
        <result property="category" column="category"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="seeLevel" column="see_level"/>
        <result property="status" column="status"/>
        <result property="replayId" column="replay_id"/>
        <result property="startDate" column="start_date"/>
        <result property="hasNewComment" column="has_new_comment"/>
        <result property="fmId" column="fm_id"/>
        <result property="videoImg" column="video_img"/>
        <result property="videoUrl" column="video_url"/>
        <result property="topType" column="top_type"/>
        <result property="forumNum" column="forum_num"/>
        <result property="isComment" column="is_comment"/>
        <result property="isVoice" column="is_voice"/>
        <result property="auditUid" column="audit_uid"/>
        <result property="auditTime" column="audit_time"/>
        <result property="remark" column="remark"/>
        <result property="coverThumb" column="cover_thumb"/>
        <result property="imgUrl" column="headImageAddress" />
        <result property="avatarType" column="avatarType" />
        <result property="programLogo" column="logo" />
    </resultMap>

    <resultMap id="bcPostMapExtend" extends="BcPostResult" type="com.zainanjing.zijin.domain.BcPost">
        <result property="fmName" column="fmName"/>
        <result property="fmShortName" column="fmShortName"/>
        <result property="startDate" column="start_date"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  forum_id,  program_id,  topic_id,  subject,  content,  ip,  region,  color,  uid,  click_num,  comment_num,  praise_num,  type,  url,  is_anonymous,  sort,  is_rec,  is_top,  category,  create_time,  update_time,  start_time,  end_time,  see_level,  status,  replay_id,  start_date,  has_new_comment,  fm_id,  video_img,  video_url,  top_type,  forum_num,  is_comment,  is_voice,  audit_uid,  audit_time,  remark,  cover_thumb     </sql>
    <select id="selectPostPage" parameterType="java.util.Map" resultMap="BcPostResult">
        SELECT bp.*,
        bf.name forumName,
        gu.user_name userName,
        topic.name topicName
        FROM bc_post bp
        LEFT JOIN bc_forum bf ON bp.forum_id = bf.id
        LEFT JOIN gdmm_users gu ON bp.uid = gu.user_id
        LEFT JOIN bc_topic topic ON bp.topic_id = topic.id
        <where>
            <if test="param.subject != null and param.subject != ''">
                AND bp.subject like CONCAT('%',#{param.subject},"%")
            </if>
            <if test="param.content != null and param.content != ''">
                AND bp.content like CONCAT('%',#{param.content},"%")
            </if>
            <if test="param.userName != null and param.userName != ''">
                AND gu.user_name like CONCAT('%',#{param.userName},"%")
            </if>
            <if test="param.status != null and param.status !='' ">
                AND bp.status = #{param.status}
            </if>
            <if test="param.forumId != null and param.forumId !='' ">
                AND bp.forum_id = #{param.forumId}
            </if>
            <if test="param.topicId != null and param.topicId !='' ">
                AND bp.topic_id = #{param.topicId}
            </if>
            AND bp.status != 1
            AND bp.type !=3
        </where>
        ORDER BY bp.is_top DESC,bp.is_rec DESC,bp.id DESC
    </select>

    <select id="selectMoreById" resultMap="BcPostResult">
        SELECT bp.*,
               bf.name      forumName,
               gu.user_name userName,
               topic.name   topicName
        FROM bc_post bp
                 LEFT JOIN bc_forum bf ON bp.forum_id = bf.id
                 LEFT JOIN gdmm_users gu ON bp.uid = gu.user_id
                 LEFT JOIN bc_topic topic ON bp.topic_id = topic.id
        WHERE bp.id = #{postId}
    </select>

    <select id="selectUserNameById" resultType="java.lang.String">
        SELECT user_name
        FROM gdmm_users
        WHERE user_id = #{uid}
    </select>

    <select id="search" resultType="com.zainanjing.zijin.domain.BcPost">
        select
        <!--如果uid不为空,查到的评论包含审核和未审核的 -->
        (select count(id)
        from bc_comment
        where post_id = bp.id
        and type = 1
        and (status = 0
        or (bp.uid = #{param.thisUid} and bp.status = 30)
        )) as comment_num,
        bp.*,eu.user_name ,eu.medal_level , bf.logo,bf.name as forumName,
        if(ISNULL(bfu.forum_id),0,1) as isHost,
        ea.address as headImageAddress,
        ea.type as avatarType,
        (select fm.name from bc_fm fm where fm.id=bf.fm_id) as fmName,
        (select fm.short_name from bc_fm fm where fm.id=bf.fm_id) as fmShortName,
        (SELECT IFNULL(sum(catflow.num),0)
        FROM gdmm_cat_flow catflow WHERE catflow.reward_type = 1
        AND catflow.resource_id = bp.id
        AND catflow.uid = bp.uid
        and catflow.type = 2
        and catflow.code = 22) as rewardNum
        from bc_post bp
        left join gdmm_users eu on bp.uid = eu.user_id
        left join bc_forum bf on bf.id = bp.forum_id
        left join bc_index_forum_user bfu on bp.uid = bfu.uid
        left join gdmm_avatar ea on bp.uid=ea.uid
        <where>
            <if test="param.topicId != null and param.topicId != ''">
                and bp.topic_id = #{param.topicId}
            </if>
            <if test="param.type != null and param.type != ''">
                and bp.type = #{param.type}
            </if>
            <if test="param.uid != null and param.uid != ''">
                and bp.uid = #{param.uid}
            </if>
            <if test="param.closeUids != null and param.closeUids.size() > 0">
                and bp.uid not in
                <foreach item="closeUid" collection="param.closeUids" open="(" separator="," close=")">
                    #{closeUid}
                </foreach>
            </if>
            <if test="param.isTop != null and param.isTop != ''">
                and bp.is_top = #{param.isTop}
                <if test="param.isTop == 0 or param.isTop == 1">
                    and bp.forum_id = #{param.forumId}
                </if>
            </if>
            <if test="param.isTop == null or param.isTop == ''">
                <if test="param.forumId != null and param.forumId != ''">
                    and bf.id = #{param.forumId}
                </if>
            </if>
            <if test="param.thisUid == null or param.thisUid == ''">
                <if test="param.status != null and param.status != ''">
                    and bp.status = #{param.status}
                </if>
            </if>
            <if test="param.thisUid != null and param.thisUid != ''">
                <if test="param.status != null and param.status != ''">
                    and (bp.status = 0 or (bp.uid=#{param.thisUid} and bp.status = 30))
                </if>
            </if>
            <if test="param.isForReply != null and param.isForReply != ''">
                <if test="param.forumId != null and param.forumId != ''">
                    and bp.forum_id = #{param.forumId}
                </if>
            </if>
            <if test="param.forumNum != null and param.forumNum != ''">
                and bp.forum_num lt #{param.forumNum} and bp.forum_num gt #{param.forumNumMin}
            </if>
            <if test="param.topLatestId != null and param.topLatestId != ''">
                and bp.id lt #{param.topLatestId}
            </if>
            <if test="param.seeLevel != null and param.seeLevel != ''">
                and bp.see_level = #{param.seeLevel}
            </if>
        </where>
        <if test="param.orderStartDate != null and param.orderStartDate != ''">
            <if test="param.orderStartDate == 'asc'">
                order by bp.start_date asc
            </if>
            <if test="param.orderStartDate == 'desc'">
                order by bp.start_date desc
            </if>
        </if>
        <if test="start != null and pageSize != null">
            limit #{start}, #{pageSize}
        </if>
    </select>

    <select id="countReward" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1)
        from gdmm_cat_flow gcf
        <where>
            <if test="param.code != null and param.code != ''">
                and gcf.code = #{param.code}
            </if>
            <if test="param.rewardType != null and param.rewardType != ''">
                and gcf.reward_type = #{param.rewardType}
            </if>
            <if test="param.uid != null and param.uid != ''">
                and gcf.uid = #{param.uid}
            </if>
            <if test="param.resourceId != null and param.resourceId != ''">
                and gcf.resource_id = #{param.resourceId}
            </if>
        </where>
    </select>

    <select id="findByParams" resultType="com.zainanjing.zijin.domain.BcPost">
        SELECT (SELECT count(id)
                FROM bc_comment
                WHERE post_id = bp.id
                  AND type = 1
                  AND (STATUS = 0 OR (bp.uid = #{uid} and bp.status = 30))) as comment_num,
               bp.*,
               eu.user_name,
               eu.medal_level,
               bf.logo,
               bf.NAME                                                        AS forumName,
               IF
               (ISNULL(bfu.forum_id), 0, 1)                                   AS isHost,
               ea.address                                                     AS headImageAddress,
               ea.type                                                        AS avatarType,
               bfm.NAME                                                       AS fmName,
               bfm.short_name                                                 AS fmShortName,
               (SELECT IFNULL(sum(catflow.num), 0)
                FROM gdmm_cat_flow catflow
                WHERE catflow.reward_type = 1
                  AND catflow.resource_id = bp.id
                  AND catflow.uid = bp.uid
                  AND catflow.type = 2
                  AND catflow.CODE = 22)                                      AS rewardNum
        FROM bc_post bp
                 LEFT JOIN gdmm_users eu ON bp.uid = eu.user_id
                 LEFT JOIN bc_forum bf ON bf.id = bp.forum_id
                 LEFT JOIN bc_fm bfm ON bf.fm_id = bfm.id
                 LEFT JOIN bc_index_forum_user bfu ON bp.uid = bfu.uid
                 LEFT JOIN gdmm_avatar ea ON bp.uid = ea.uid
        WHERE bp.id = #{postId} AND bp.status = 0
    </select>

</mapper>