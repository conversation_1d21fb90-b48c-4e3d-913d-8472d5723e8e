<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcChoiceAlbumMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcChoiceAlbum" id="BcChoiceAlbumResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="categoryId" column="category_id"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="anchor" column="anchor"/>
        <result property="produce" column="produce"/>
        <result property="description" column="description"/>
        <result property="descImgUrl" column="desc_img_url"/>
        <result property="isRefAnchor" column="is_ref_anchor"/>
        <result property="anchorUid" column="anchor_uid"/>
        <result property="view" column="view"/>
        <result property="virtualView" column="virtual_view"/>
        <result property="isHot" column="is_hot"/>
        <result property="isJingxuan" column="is_jingxuan"/>
        <result property="isShow" column="is_show"/>
        <result property="sort" column="sort"/>
        <result property="shareIcon" column="share_icon"/>
        <result property="shareTitle" column="share_title"/>
        <result property="shareDesc" column="share_desc"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="lanMuId" column="lan_mu_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  category_id,  name,  logo,  anchor,  produce,  description,  desc_img_url,  is_ref_anchor,  anchor_uid,  view,  virtual_view,  is_hot,  is_jingxuan,  is_show,  sort,  share_icon,  share_title,  share_desc,  type,  status, lan_mu_id     </sql>
    <select id="selectBcChoiceAlbumPage" parameterType="java.util.Map" resultMap="BcChoiceAlbumResult">
        SELECT a.*,
        c.name AS choiceCategoryName
        FROM bc_choice_album a
        LEFT JOIN bc_choice_category c ON a.category_id = c.id
        <where>
            <if test="param.name != null and param.name != ''">
                AND a.name like CONCAT('%',#{param.name},"%")
            </if>
            <if test="param.isShow != null and param.isShow !='' ">
                AND a.is_show = #{param.isShow}
            </if>
            <if test="param.type != null and param.type !='' ">
                AND a.type = #{param.type}
            </if>
            <if test="param.categoryId != null and param.categoryId !='' ">
                AND a.category_id = #{param.categoryId}
            </if>
            AND a.status = 1
        </where>
        ORDER BY a.id DESC
    </select>

</mapper>