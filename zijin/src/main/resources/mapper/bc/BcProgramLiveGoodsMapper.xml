<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcProgramLiveGoodsMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcProgramLiveGoods" id="BcProgramLiveGoodsResultMap">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="programLiveId" column="program_live_id"/>
        <result property="linkType" column="link_type"/>
        <result property="linkModule" column="link_module"/>
        <result property="linkTo" column="link_to"/>
        <result property="goodsResId" column="goods_res_id"/>
        <result property="imgUrl" column="img_url"/>
        <result property="imgLink" column="img_link"/>
        <result property="sortNo" column="sort_no"/>
        <result property="isExplain" column="is_explain"/>
        <result property="isShow" column="is_show"/>
        <result property="isDel" column="is_del"/>

        <result property="goodsName" column="goodsName"/>
        <result property="goodsImgUrl" column="goodsImgUrl"/>
        <result property="goodsPrice" column="goodsPrice"/>
        <result property="goodsTitle" column="goodsTitle"/>
    </resultMap>


    <select id="searchGoodsList" resultMap="BcProgramLiveGoodsResultMap">
        select t.*,
               goo.name    as `goodsName`,
               t.`img_url` as `goodsImgUrl`,
               goo.price   as `goodsPrice`,
               goo.title   as `goodsTitle`
        from bc_program_live_goods t
                 left join gdmm_goods goo on t.goods_res_id = goo.id
        where t.program_live_id = #{id}
          and t.is_del = 0
          and t.is_show = 'Y'
        ORDER BY t.sort_no desc
        limit #{startInt}, #{pageSizeInt}
    </select>


</mapper>