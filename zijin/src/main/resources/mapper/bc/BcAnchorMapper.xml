<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcAnchorMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcAnchor" id="BcAnchorResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
        <result property="channelId" column="channel_id"/>
        <result property="userId" column="user_id"/>
        <result property="bcId" column="bc_id"/>
        <result property="anchorName" column="anchor_name"/>
        <result property="isTalk" column="is_talk"/>
        <result property="isAudit" column="is_audit"/>
        <result property="anchorDesc" column="anchor_desc"/>
        <result property="anchorImgUrl" column="anchor_img_url"/>
        <result property="shareImgUrl" column="share_img_url"/>
        <result property="fileDuration" column="file_duration"/>
        <result property="isShow" column="is_show"/>
        <result property="listSortNo" column="list_sort_no"/>
        <result property="isRecMain" column="is_rec_main"/>
        <result property="mainSortNo" column="main_sort_no"/>
        <result property="topSortType" column="top_sort_type"/>
        <result property="topSortNo" column="top_sort_no"/>
        <result property="fansNum" column="fans_num"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  update_time,  is_del,  channel_id,  user_id,  bc_id,  anchor_name,  is_talk,  is_audit,  anchor_desc,  anchor_img_url,  share_img_url,  file_duration,  is_show,  list_sort_no,  is_rec_main,  main_sort_no,  top_sort_type,  top_sort_no,  fans_num     </sql>

    <select id="selectAnchorPage" parameterType="java.util.Map" resultMap="BcAnchorResult">
        SELECT
        ba.*,
        ( SELECT COUNT( 1 ) FROM bc_anchor_fans fans WHERE ba.id = fans.anchor_id ) AS fansNum,
        ( SELECT COUNT( 1 ) FROM bc_anchor_video video WHERE ba.id = video.anchor_id ) AS videoNum,
        ( SELECT COUNT( 1 ) FROM bc_anchor_image img WHERE ba.id = img.anchor_id ) AS imgNum,
        gu.user_name AS userName
        FROM
        bc_anchor ba
        LEFT JOIN gdmm_users gu ON gu.user_id = ba.user_id
        <where>
            <if test="param.anchorName != null and param.anchorName != ''">
                AND ba.anchor_name like CONCAT('%',#{param.anchorName},"%")
            </if>
            <if test="param.isRecMain != null  and param.isRecMain != ''">
                AND ba.is_rec_main=#{param.isRecMain}
            </if>
            <if test="param.isShow != null and param.isShow !='' ">
                AND ba.is_show = #{param.isShow}
            </if>
            AND ba.is_del = 0
        </where>
        ORDER BY
        <if test="param.topSortType != null and param.topSortType != ''">
            ba.top_sort_no,
        </if>
        ba.id DESC
    </select>

    <select id="queryByPhoneNumber" resultType="com.zainanjing.zijin.domain.BcCommonModel">
        SELECT user_id AS id, user_name AS value
        FROM gdmm_users
        WHERE mobile_phone = #{phoneNumber}
          AND `status` = 0
    </select>

    <select id="selectMoreById" resultMap="BcAnchorResult">
        SELECT ba.*,
               gu.user_name    AS userName,
               gu.mobile_phone AS phone
        FROM bc_anchor ba
                 LEFT JOIN gdmm_users gu ON ba.user_id = gu.user_id
        WHERE ba.id = #{id}
    </select>
</mapper>