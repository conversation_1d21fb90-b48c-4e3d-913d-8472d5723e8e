<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.zijin.mapper.BcAnchorVideoMapper">

    <resultMap type="com.zainanjing.zijin.domain.BcAnchorVideo" id="BcAnchorVideoResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="isDel" column="is_del"/>
        <result property="updateTime" column="update_time"/>
        <result property="anchorId" column="anchor_id"/>
        <result property="videoName" column="video_name"/>
        <result property="videoDesc" column="video_desc"/>
        <result property="videoDuration" column="video_duration"/>
        <result property="playUrl" column="play_url"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="shareImgUrl" column="share_img_url"/>
        <result property="isShow" column="is_show"/>
        <result property="sortNo" column="sort_no"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  create_time,  is_del,  update_time,  anchor_id,  video_name,  video_desc,  video_duration,  play_url,  cover_url,  share_img_url,  is_show,  sort_no     </sql>

</mapper>