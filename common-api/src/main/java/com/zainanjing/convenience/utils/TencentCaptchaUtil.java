package com.zainanjing.convenience.utils;

import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.sign.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @ClassName: TencentCaptchaUtil
 * @Description :腾讯验证码消费者
 * @date 2021年12月27日上午10:33:13
 */
@Service("tencentCaptchaUtil")
public class TencentCaptchaUtil {

    private Logger logger = LoggerFactory.getLogger(TencentCaptchaUtil.class);

    private static final String TencentCaptcha_MESSAGE = "【腾讯验证码】 ";

    private final String TencentCaptcha_URL = "https://vcode.zainanjing365.com";
    //用于签名的key
    private final String TencentCaptcha_KEY = "tengxunyanzhengmajiamidekey";

    //ticket ip randstr 都是客户端传过来的
    public Boolean verify(String client, String phone, String ticket, String ip, String randstr){
        Boolean flag = false;
        String logprefix = "腾讯验证码验证接口 ";
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("content-type", "application/json;charset=UTF-8");
        String sign = SignUtil.md5("randstr=" + randstr + TencentCaptcha_KEY);
        try {
            ticket = URLEncoder.encode(ticket, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            return false;
        }

        String url = TencentCaptcha_URL + "/tencentcaptcha/verify" + "?phone=" + phone + "&sign=" + sign + "&ticket=" + ticket + "&ip=" + ip + "&randstr=" + randstr + "&client=" + client;
        logger.debug(TencentCaptcha_MESSAGE + "{}请求url:{}", logprefix, url);
        String resp = HttpUtil.get(url,null, String.class);
        logger.debug(TencentCaptcha_MESSAGE + "{}返回:{}", logprefix, resp);

        if (StrUtil.isNull(resp)) {
            logger.error(TencentCaptcha_MESSAGE + "{}返回信息为空!", logprefix);
            return false;
        }
        flag = Boolean.valueOf(resp);
        return flag;
    }


}
