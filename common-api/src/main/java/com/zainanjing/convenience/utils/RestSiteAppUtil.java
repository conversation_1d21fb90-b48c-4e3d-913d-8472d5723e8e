package com.zainanjing.convenience.utils;

import com.ruoyi.common.utils.CollUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.sign.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class RestSiteAppUtil {

    private static final Logger log = LoggerFactory.getLogger(RestSiteAppUtil.class);

    @Value("${system.wap-inter.app-id:''}")
    private String appId = "110";

    @Value("${system.wap-inter.app-key:''}")
    private String appKey = "1692866d6363c0bd792dc7e7346d81aa";

    @Value("${system.site-app-url:''}")
    private String url = "https://orientalmianyangt.zainanjing365.com/siteapp/";

    /**
     * 根据ID查询 /users/findUserByIds
     */
    public Map<String, String> getUserById(String userId) {
        Map<String, Object> params = new TreeMap<>();
        params.put("appId", appId);
        params.put("userIds", userId);
        params.put("signature", makeSign(params, appKey));
        JsonNode jsonNode = HttpUtil.get(url + "/users/findUserByIds", params);
        if (null != jsonNode && "200".equals(jsonNode.get("error").asText())) {
            Map<String, String> mapResult = new HashMap<>();
            JsonNode jsonArray = jsonNode.get("data");
            if (CollUtil.isNotEmpty(jsonArray)) {
                jsonArray.forEach(item -> {
                    mapResult.put("uid", item.get("uid").asText());
                    mapResult.put("mobile", item.get("mobile").asText());
                    mapResult.put("username", item.get("username").asText());
                    mapResult.put("imageUrl", item.get("imageUrl").asText());
                });
                return mapResult;
            }
        }
        return new HashMap<>();
    }

    //根据手机号码模糊查询
    public List<String> getMobiles(String mobilePhone) {
        Map<String, Object> params = new TreeMap<>();
        params.put("appId", appId);
        params.put("mobilePhone", mobilePhone);
        params.put("signature", makeSign(params, appKey));
        JsonNode jsonObject = HttpUtil.get(url + "/users/findUserInfoByLikeMobile", params);
        if (null != jsonObject && "200".equals(jsonObject.get("error").asText())) {
            List<String> result = new ArrayList<>();
            JsonNode array = jsonObject.get("data");
            if (CollUtil.isNotEmpty(array)) {
                array.forEach(item -> {
                    result.add(item.get("mobilePhone").asText());
                    log.debug("手机号码：{}用户id：{}", item.get("mobilePhone"), item.get("userId"));
                });
                return result;
            }
        }
        return new ArrayList<>();
    }

    //根据手机批量查询,返回手机号码和用户id
    public Map<String, String> getUsers(List<String> mobiles) {
        Map<String, Object> params = new TreeMap<>();
        params.put("appId", appId);
        params.put("mobilePhones", mobiles.stream().reduce((a, b) -> a + "," + b).get());
        params.put("signature", makeSign(params, appKey));
        JsonNode jsonObject = HttpUtil.get(url + "/users/findUserInfoByMobiles", params);
        if (null != jsonObject && "200".equals(jsonObject.get("error").asText())) {
            Map<String, String> mapResult = new HashMap<>();
            JsonNode array = jsonObject.get("data");
            if (CollUtil.isNotEmpty(array)) {
                array.forEach(item -> {
                    mapResult.put(item.get("mobilePhone").asText(), item.get("userId").asText());
                    log.debug("手机号码：{}用户id：{}", item.get("mobilePhone"), item.get("userId"));
                });
                return mapResult;
            }
        }
        return new HashMap<>();
    }

    /**
     * 获取拉黑的用户
     */
    public List<Long> getBlackList(Long uid) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("service_name", "gdmmUsers");
        requestMap.put("method_name", "subscriptionCloseList");
        requestMap.put("appId", appId);
        requestMap.put("uid", uid);
        requestMap.put("signature", makeSign(requestMap, appKey));
        List<Long> result = new ArrayList<>();
        try {
            JsonNode jsonObject = HttpUtil.get(url + "/gdmm/invoke", requestMap);
            jsonObject.get("data").forEach(item -> {
                result.add(item.get("uid").asLong());
            });
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }


    private String makeSign(Map data, String apiKey) {
        String sign = null;
        Map<String, String> treeMap = new TreeMap<String, String>(data);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry entry : treeMap.entrySet()) {
            sb = sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(apiKey);
        log.debug("签名：字符串{}", sb);
        sign = SignUtil.md5(sb.toString());
        return sign;
    }

}
