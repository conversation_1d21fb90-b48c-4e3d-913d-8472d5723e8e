package com.zainanjing.convenience.utils;


import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.http.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Lazy(false)
@Component
public class WeChartUtil {

    protected static final Logger logger = LoggerFactory.getLogger(WeChartUtil.class);

    public static Map<String, String> weixinMap;

    static {
        weixinMap = new HashMap<>();
    }

    /*
     * 微信分享相关
     */
    public final static String WEIXIN_KEY_APPID = "appid";
    public final static String WEIXIN_KEY_SECRET = "secret";
    public final static String WEIXIN_KEY_EXPIRE_TIME = "expireTime";
    public final static String WEIXIN_KEY_TICKET = "ticket";

    public static String WEIXIN_APPID;

    public static String WEIXIN_SECRET;

    @Value("${system.share.weixin-appid:wx8781066f0edcdbc3}")
    public void setWeixinAppid(String weixinAppid) {
        WeChartUtil.WEIXIN_APPID = weixinAppid;
    }

    @Value("${system.share.weixin-secret:a96fb1ad55b2989a9ea6ff0ab277b466}")
    public void setWeixinSecret(String weixinSecret) {
        WeChartUtil.WEIXIN_SECRET = weixinSecret;
    }

    public final static String WEIXIN_URL_TOKEN = "https://api.weixin.qq.com/cgi-bin/token";
    public final static String WEIXIN_GRANT_TYPE = "client_credential";
    public final static String WEIXIN_URL_TICKET = "https://api.weixin.qq.com/cgi-bin/ticket/getticket";
    public final static String WEIXIN_JSAPI = "jsapi";

    /**
     * @return String
     * @Title:getAccessToken
     * @Description 获取微信access_token是公众号的全局唯一票据，公众号调用各接口时都需使用access_token。
     */
    // https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential?secret=5f373a3033210789b6796232d1f616c1&appid=wx7d3c1e9392b50f59
    // 正确//{"access_token":"guADriUe6szJPoaXbRHH60tETomSzUnHygQWHtQRP3LcV2O6POfa-6VD0TJxFpicVdy0LlYq6fLbi29kNYnyosKtGHL9VCzhMX8Iived9IsKFHcACARMO","expires_in":7200}
    // 出错//{"errcode":40002,"errmsg":"invalid grant_type hint: [tD2XKA0574ken1]"}
    @SuppressWarnings("unchecked")
    public static String getAccessToken() {
        String accessToken = null;

        Map param = new HashMap();
        param.put("grant_type", WeChartUtil.WEIXIN_GRANT_TYPE);
        param.put("appid", WeChartUtil.WEIXIN_APPID);
        param.put("secret", WeChartUtil.WEIXIN_SECRET);


        JsonNode jsonObj = HttpUtil.get(WeChartUtil.WEIXIN_URL_TOKEN, param);
        if (jsonObj != null) {
            accessToken = jsonObj.get("access_token").asText();
            if (StrUtil.isEmpty(accessToken) || "null".equals(accessToken)) {
                logger.error("微信二次分享出错 getAccessToken返回:{}", jsonObj);
            }
        }
        return accessToken;
    }

    /**
     * @return String
     * @Title:getJsapiTicket
     * @Description 获取jsapi_ticket是公众号用于调用微信JS接口的临时票据。正常情况下，jsapi_ticket的有效期为7200秒
     * ，通过access_token来获取。
     */
    // https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=FI-x_zMtNRluvS2GHkp0pHcAdJzF3-qxGoA5L8epDoq-OSj64uHxyWKzfC3zfX9jDmrlDbXFN8e4LwQuAAXTCXXYfQRJrdu4GDxFKllwgvObrELsSbrpbCBwxI5wbMboLSGhAFAAHV&type=jsapi
    // 正确//{"errcode":0,"errmsg":"ok","ticket":"kgt8ON7yVITDhtdwci0qeYqKMCwSbdSlSlJSKarv79g0LWbXM8R72XpCRfp0Bo3tapPL2lvYC750acFN0n3sfQ","expires_in":7200}
    // 错误//{"errcode":40001,"errmsg":"invalid credential, access_token is invalid or not latest hint: [eQTf0546vr32!]"}
    public static String getJsapiTicket(String accessToken) {
        String ticket = null;
        Map param = new HashMap();
        param.put("access_token", accessToken);
        param.put("type", WeChartUtil.WEIXIN_JSAPI);

        JsonNode jsonObj = HttpUtil.get(WeChartUtil.WEIXIN_URL_TICKET, param);
        if (jsonObj != null) {
            ticket = jsonObj.get("ticket").asText();
        }
        return ticket;
    }

    public static String getWeixinTicket() {
        String ticket = null;
        // 先判断过期时间
        String expireTimeString = weixinMap
                .get(WeChartUtil.WEIXIN_KEY_EXPIRE_TIME);
        ticket = weixinMap.get(WeChartUtil.WEIXIN_KEY_TICKET);
        Integer now = getTimestamp();
        if (!StrUtil.isEmpty(expireTimeString)) {
            Integer expireTime = Integer.parseInt(expireTimeString);
            // 如果超过25分钟 重新获取，直接覆盖
            if (now - expireTime >= 1500 || StrUtil.isEmpty(ticket)) {
                ticket = getJsapiTicket(WeChartUtil.getAccessToken());
                weixinMap.put(WeChartUtil.WEIXIN_KEY_TICKET, ticket);
                weixinMap.put(WeChartUtil.WEIXIN_KEY_EXPIRE_TIME,
                        String.valueOf(now));
            }
            // 如果时间戳key都没有直接发请求获取 并放入map
        } else {
            ticket = getJsapiTicket(WeChartUtil.getAccessToken());
            weixinMap.put(WeChartUtil.WEIXIN_KEY_TICKET, ticket);
            weixinMap
                    .put(WeChartUtil.WEIXIN_KEY_EXPIRE_TIME, String.valueOf(now));
        }
        return ticket;

    }

    public static Integer getTimestamp() {
        return Integer.parseInt(String.valueOf(System.currentTimeMillis())
                .substring(0, 10));
    }
}
