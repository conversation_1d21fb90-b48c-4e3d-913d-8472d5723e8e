package com.zainanjing.convenience.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.sign.SignUtil;
import com.zainanjing.common.constant.SensitiveException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConditionalOnProperty(name = "wangyi.check-url")
public class WangyiCheckUtil {

    protected static final Logger logger = LoggerFactory.getLogger(WangyiCheckUtil.class);

    @Value("${wangyi.check-url}")
    private String url;

    @Value("${wangyi.check-client}")
    private String client;

    @Value("${wangyi.check-key}")
    private String key;

    private final String textCheckUrl = "/wangyiyidun/checkText";

    private final String imgCheckUrl = "/wangyiyidun/checkImageList";


    public void checkText(Integer errorCode, String key, String text) {
        if (StrUtil.isBlank(text)) {
            return;
        }
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("text", text);
        if (StrUtil.isNotBlank(key)) {
            requestMap.put("key", key);
        }
        requestMap.put("client", this.client);
        requestMap.put("sign", SignUtil.md5("text=" + text + this.key));
        JsonNode post = HttpUtil.postForm(this.url + this.textCheckUrl, requestMap);
        if ("200".equals(post.get("code").asText()) && "false".equals(post.get("isPass").asText())) {
            logger.warn("wangyi-check text check failed: {}", post.get("msg").asText());
            throw new SensitiveException(198, "内容中含有违规信息，提交失败。", post.get("minGanArray").toString());
        } else {
            logger.debug("wangyi-check text check passed");
        }
    }

    /**
     * check 文字
     *
     * @param text
     * @return
     */
    public void checkText(String text) {
        checkText(198, null, text);
    }

    public void checkText(String key, String text) {
        checkText(198, key, text);
    }

//    public static void main(String[] s) {
//        WangyiCheckUtil wangyiCheckUtil = new WangyiCheckUtil();
//        wangyiCheckUtil.setUrl("https://imagecensoringt.zainanjing365.com");
//        wangyiCheckUtil.setClient("client1");
//        wangyiCheckUtil.setKey("dcc7c193b6ef484ab47338b08d6e1e6d");
//        wangyiCheckUtil.checkText("蔡英文不对");
//    }

    /**
     * check 图片
     *
     * @param imageUrlList
     * @return
     */
    public void checkImages(String imageUrlList) {
        checkImages(198, imageUrlList);
    }

    public void checkImages(Integer errorCode, String imageUrlList) {
        if (StrUtil.isBlank(imageUrlList)) {
            return;
        }
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("imageUrlList", imageUrlList);
        requestMap.put("imageName", "imageNames");
        requestMap.put("client", this.client);
        requestMap.put("sign", SignUtil.md5("imageUrlList=" + imageUrlList + this.key));
        JsonNode post = HttpUtil.postForm(this.url + this.imgCheckUrl, requestMap);
        if ("200".equals(post.get("code").asText()) && (ObjUtil.isNotNull(post.get("isPass")) && "false".equals(post.get("isPass").asText()))) {
            logger.warn("wangyi-check text check failed: {}", post.get("msg").asText());
            throw new SensitiveException(198, "内容中含有违规信息，提交失败。", post.get("minGanArray").toString());
        } else {
            logger.debug("wangyi-check text check passed");
        }
    }


    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
