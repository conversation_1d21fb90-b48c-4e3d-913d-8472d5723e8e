package com.zainanjing.convenience.config;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ExceptionUtil;
import com.ruoyi.common.utils.LogUtils;
import com.ruoyi.common.utils.id.IdUtil;
import com.zainanjing.common.constant.SensitiveException;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.support.web.ResultData;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 参数异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Object handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        log.error("请求地址'{}',参数校验异常:{}", request.getRequestURI(), e.getMessage());
        return request.getServletPath().startsWith("/api-2c") ? new ResultData(400, e.getMessage()) : ResResult.error(400, e.getMessage());
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Object handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
                                                      HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e.getMethod());
        return request.getServletPath().startsWith("/api-2c") ? new ResultData(405, e.getMessage()) : ResResult.error(e.getMessage());
    }

    /**
     * 敏感词异常
     */
    @ExceptionHandler(SensitiveException.class)
    public Object SensitiveException(SensitiveException e, HttpServletRequest request) {
        log.error(e.getMessage(), e);
        Integer code = e.getCode() == null ? 500 : e.getCode();
        return request.getServletPath().startsWith("/api-2c") ? new ResultData(code, e.getMessage(), e.getDetailMessage()) : new ResResult(code, e.getMessage(), e.getDetailMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public Object handleServiceException(ServiceException e, HttpServletRequest request) {
        log.error(e.getMessage(), e);
        Integer code = e.getCode() == null ? 500 : e.getCode();
        return request.getServletPath().startsWith("/api-2c") ? new ResultData(code, e.getMessage()) : ResResult.error(code, e.getMessage());
    }

    @ExceptionHandler({HttpMessageConversionException.class, ServletRequestBindingException.class, NoResourceFoundException.class})
    public Object handleInvalidFormatException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常:{}", requestURI, e.getMessage());
        return request.getServletPath().startsWith("/api-2c") ? new ResultData(400, "资源不存在") : ResResult.error(400, "资源不存在");
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public Object handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String logId = IdUtil.getSnowflakeNextIdStr();
        log.error("请求地址'{}',异常编号'{}',发生系统异常.{}", requestURI, logId, ExceptionUtil.getExceptionMessage(e));
        LogUtils.sendErrorAsyncMsg(e.getMessage(), logId, requestURI);
        return request.getServletPath().startsWith("/api-2c") ? new ResultData(500, "网络开小差, 请稍后重试") : ResResult.error(500, "网络开小差, 请稍后重试");
    }

}
