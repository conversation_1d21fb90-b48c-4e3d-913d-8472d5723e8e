package com.zainanjing.convenience.config;

import com.ruoyi.common.config.SwitchDSInterceptor;
import com.ruoyi.common.enums.DataSourceType;
import com.zainanjing.convenience.support.interceptor.CTokenAuthInterceptor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;
import java.util.Optional;

/**
 * 配置拦截器
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "system.api")
public class FrontendWebConfig implements WebMvcConfigurer {

    private List<String> authList;

    public void setAuthList(List<String> authList) {
        this.authList = authList;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(switchDS()).addPathPatterns("/api-ae/**", "/api-sq/**", "/api-bc/**");
        //C端拦截
        InterceptorRegistration interceptorRegistration = registry.addInterceptor(getCTokenAuthInterceptor()).addPathPatterns(
                "/api-2c/**",
                "/api-bc/**"
        );
        if (Optional.ofNullable(authList).isPresent()) {
            interceptorRegistration.addPathPatterns(authList);
        }
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("https://*.zainanjing365.com", "https://*.njgdmm.com", "http://*.znjdev.com:1234","https://*.gdmmdev.com")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true);
    }

    @Bean
    public SwitchDSInterceptor switchDS() {
        return new SwitchDSInterceptor(DataSourceType.SLAVE);
    }

    @Bean
    public CTokenAuthInterceptor getCTokenAuthInterceptor() {
        return new CTokenAuthInterceptor();
    }
}
