package com.zainanjing.convenience.support.usercenter;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.NumberUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.LoginUserType;
import com.ruoyi.common.utils.JSONUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.sign.SignUtil;
import com.zainanjing.convenience.support.APISystemConfig;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class GdmmUserInfoClient {

    private final String gdmmTokenPre = "GDMMTOKEN::";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    //获取列表信息，并且鉴权， 需要加入redis缓存，
    public List<GdmmUserInfo> getGdmmUserInfoByKeys(String uid, Set<String> uIds) {
        List<GdmmUserInfo> gdmmUserInfoList = new ArrayList<>();
        if (CollUtil.isEmpty(uIds)) {
            return gdmmUserInfoList;
        }
        //因为关注操作为及时操作，停用缓存
//        List<String> gdmmUsers = multiGet(uIds);
//        if (CollUtil.isNotEmpty(gdmmUsers)) {
//            gdmmUsers.forEach(x -> {
//                if(StrUtil.isNotEmpty(x)) {
//                    gdmmUserInfoList.add(JSONObject.parseObject(x, GdmmUserInfo.class));
//                }
//            });
//        }
//        if (gdmmUserInfoList.size() == uIds.size()) {
//            return gdmmUserInfoList;
//        }

        Set<String> querySets = new HashSet<>();
        uIds.forEach(x -> {
            Optional<GdmmUserInfo> any = gdmmUserInfoList.stream().filter(m -> m.getUid().equals(x)).findAny();
            if (!any.isPresent()) {
                querySets.add(x);
            }
        });
        if (StrUtil.isBlank(APISystemConfig.siteAppToken)) {
            getSiteAppToken();
        }

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("service_name", "gdmmUsers");
        requestMap.put("method_name", "queryUserList");
        requestMap.put("bianminUid", APISystemConfig.siteAppUid);
        requestMap.put("bianminToken", APISystemConfig.siteAppToken);
        requestMap.put("uid", uid);
        String collect = querySets.stream().collect(Collectors.joining("_"));
        requestMap.put("uids", collect);
        requestMap.put("sign", SignUtil.md5("uids=" + collect + APISystemConfig.siteAppToken));
        JsonNode returnNode = HttpUtil.get(APISystemConfig.siteAppUrl + "/gdmm/invoke", requestMap);
        try {
            if (Objects.isNull(returnNode) || List.of("500", "111").equals(returnNode.get("error").asText())) {
                requestMap.put("bianminToken", getSiteAppToken());
                requestMap.put("sign", SignUtil.md5("uids=" + collect + APISystemConfig.siteAppToken));
                returnNode = HttpUtil.get(APISystemConfig.siteAppUrl + "/gdmm/invoke", requestMap);
            }
            List<GdmmUserInfo> gdmmUserInfoList1 = JSONUtil.parseArray(returnNode.get("data"), GdmmUserInfo.class);
            gdmmUserInfoList.addAll(gdmmUserInfoList1);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return gdmmUserInfoList;

    }

    //通过接口获取siteAppToken
    private String getSiteAppToken() {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("service_name", "gdmmUsers");
        requestMap.put("method_name", "bianminAddFensi");
        requestMap.put("bianminUid", APISystemConfig.siteAppUid);
        requestMap.put("sign", SignUtil.md5("bianminUid=" + APISystemConfig.siteAppUid));
        JsonNode returnNode = HttpUtil.get(APISystemConfig.siteAppUrl + "/gdmm/invoke", requestMap);
        String resultToken = returnNode.get("data").get("bianminToken").asText();
        if (StrUtil.isNotEmpty(resultToken)) {
            APISystemConfig.siteAppToken = resultToken;
        }
        return resultToken;
    }

    //获取单独用户信息，并且鉴权
    public GdmmUserInfo gdmmUserInfo(String uId, String gdmmInfo) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("uid", uId);
        requestMap.put("gdmmInfo", gdmmInfo);
        requestMap.put("sign", SignUtil.md5("uid=" + uId + gdmmInfo));
        try {
            JsonNode returnNode = HttpUtil.get(APISystemConfig.siteAppUrl + "/users/findUserInfoByGdmmInfo", requestMap);
            return JSONUtil.parseObject(returnNode.get("data"), GdmmUserInfo.class);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return null;
    }

    /**
     * 验证token
     *
     * @param token
     * @return
     */
    public boolean checkToken(String token) {
        if (!StrUtil.containsAny(token, ".")) {
            return false;
        }
        String[] split = token.split("\\.");
        GdmmUserInfo gdmmUserInfo = null;
        if (!NumberUtil.isNumeric(split[0])) {
            return false;
        }
        if (cacheHasKey(token) && cacheHasKey(split[0])) {
            String s = cacheGet(split[0]);
            gdmmUserInfo = JSONObject.parseObject(s, GdmmUserInfo.class);
        } else {
            gdmmUserInfo = gdmmUserInfo(split[0], split[1]);
            if (ObjUtil.isNull(gdmmUserInfo) || StrUtil.isBlank(gdmmUserInfo.getUid())) {
                return false;
            }
            cacheSet(split[0], gdmmUserInfo, 45L);
            cacheSet(token, split[0], 30L);
        }
        if (ObjUtil.isNull(gdmmUserInfo)) {
            return false;
        }
        gdmmUserInfo.setGdmmInfo(split[1]);
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(Long.valueOf(gdmmUserInfo.getUid()));
        loginUser.setUserName(gdmmUserInfo.getMobile());
        loginUser.setNickName(gdmmUserInfo.getUsername());
        loginUser.setAvatar(gdmmUserInfo.getHeadimg());
        loginUser.setLoginUserType(LoginUserType.MEMBER);
        SecurityUtils.setLoginUser(loginUser);
        return true;
    }

    private boolean cacheHasKey(String key) {
        return stringRedisTemplate.hasKey(this.gdmmTokenPre + key);
    }

    private void cacheSet(String key, Object ob, Long time) {
        stringRedisTemplate.opsForValue().set(this.gdmmTokenPre + key, JSONObject.toJSONString(ob), Duration.ofMinutes(time));
    }

    private String cacheGet(String key) {
        return stringRedisTemplate.opsForValue().get(this.gdmmTokenPre + key);
    }

    private List<String> multiGet(Set<String> uIds) {
        Set<String> queryIds = uIds.stream().map(x -> this.gdmmTokenPre + x).collect(Collectors.toSet());
        return stringRedisTemplate.opsForValue().multiGet(queryIds);
    }

}
