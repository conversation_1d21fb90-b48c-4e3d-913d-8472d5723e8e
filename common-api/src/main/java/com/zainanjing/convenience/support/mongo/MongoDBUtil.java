package com.zainanjing.convenience.support.mongo;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoException;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Filters;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import com.ruoyi.common.utils.StrUtil;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.BeanUtils;
import java.beans.PropertyDescriptor;

import java.io.File;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: MongoDBUtil
 * @Description: mongodb操作数据工具类
 * @date 2017年5月15日 下午12:38:27
 */
public class MongoDBUtil {

    private static Logger logger = LoggerFactory.getLogger(MongoDBUtil.class);


    /**
     * @return void
     * @throws MongoException,Exception
     * @Title: dropDatabase
     * @Description: 删除数据库
     */
    public static void dropDatabase() throws MongoException, Exception {
        /*MongoManager.getDb().drop();*/
        // 释放mongoDB连接
        // MongoManager.close();
    }

    /**
     * @param @param 集合对应的实体类(也可为空，为空将操作默认集合)
     * @return void
     * @throws MongoException,Exception
     * @Title: dropCollection
     * @Description: 删除集合
     */
    public static void dropCollection(Class<?> entityClass) throws MongoException, Exception {
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:dropCollection,集合名称不能为空！！！");
            return;
        }
        MongoManager.getDb().getCollection(collName).drop();
        // 释放mongoDB连接
        // MongoManager.close();
    }

    /**
     * @param entityClass 集合对应的实体类(也可为空，为空将操作默认集合)
     * @return void
     * @throws MongoException,Exception
     * @Title: createCollection
     * @Description:添加集合
     */
    public static void createCollection(Class<?> entityClass) throws MongoException, Exception {
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:createCollection,集合名称不能为空！！！");
            return;
        }
        MongoManager.getDb().createCollection(collName);
        // 释放mongoDB连接
        // MongoManager.close();
    }

    /***
     *
     * @Description: 根据ID查询mongoDB数据
     * <AUTHOR>
     * @param <T>
     * @date 2017年6月5日 下午4:50:47
     * @param id
     * @param entityClass
     */
    public static <T> Object findById(Serializable id, Class<T> entityClass) throws MongoException, Exception {
        MongoCollection<Document> collection = null;
        Document document = new Document();
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:find,集合名称不能为空！！！");
            return null;
        }
        collection = MongoManager.getDb().getCollection(collName);
        document = collection.find(Filters.eq("id", id)).first();
        if (document == null) {
            return null;
        }
        T bean = entityClass.newInstance();
        recyleDocument(document, bean);
        return bean;

    }

    /**
     * @param entityClass 实体类(不能为空,为空将无法对返回结果进行解析)
     * @param @return
     * @return List<T>
     * @throws MongoException,Exception
     * @Title: find
     * @Description: 查询指定集合的文档记录，返回一个List<T>
     */
    public static <T> List<T> find(Map<String, Object> filterMap, Class<T> entityClass) throws MongoException, Exception {
        MongoCollection<Document> collection = null;
        List<T> result = new ArrayList<T>();//返回结果集
        List<Document> documentList = new ArrayList<Document>();//存储查询Mongodb文档集
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:find,集合名称不能为空！！！");
            return result;
        }
        //mongodb过滤条件对象
        BasicDBObject filter = new BasicDBObject();
        //添加过滤条件
        filterCondition(filterMap, filter);
        collection = MongoManager.getDb().getCollection(collName);
        MongoCursor<Document> cursor = collection.find(filter).iterator();
        while (cursor.hasNext()) {
            documentList.add(cursor.next());
        }
        //将Mongodb对象转换为java实体对象
        result = document2Bean(documentList, entityClass);

        return result;
    }

    /**
     * @param filterMap   简单查询条件
     * @param orderByMap  排序条件(-1是倒序，1是正序)
     * @param entityClass 实体类(不能为空,为空将无法对返回结果进行解析)
     * @param @return
     * @return List<T>
     * @throws MongoException,Exception
     * @Title: find
     * @Description: 根据过滤条件对指定集合查询 ，返回 List<T>
     */
    public static <T> List<T> find(Map<String, Object> filterMap, Map<String, Integer> orderByMap, Class<T> entityClass) throws MongoException, Exception {
        MongoCollection<Document> collection = null;
        List<T> result = new ArrayList<T>();//返回结果集
        List<Document> documentList = new ArrayList<Document>();//存储查询Mongodb文档集
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:find,集合名称不能为空！！！");
            return result;
        }
        //mongodb过滤条件对象
        BasicDBObject filter = new BasicDBObject();
        //添加过滤条件
        filterCondition(filterMap, filter);
        //mongodb排序条件对象
        BasicDBObject orderBy = new BasicDBObject();

        if (orderByMap != null && orderByMap.size() > 0) orderBy.putAll(orderByMap);

        collection = MongoManager.getDb().getCollection(collName);
        MongoCursor<Document> cursor = collection.find(filter).sort(orderBy).iterator();
        while (cursor.hasNext()) {
            documentList.add(cursor.next());
        }
        //将Mongodb对象转换为java实体对象
        result = document2Bean(documentList, entityClass);
        return result;
    }

    /**
     * @param filter      复杂过滤条件
     * @param orderByMap  排序条件(-1是倒序，1是正序)
     * @param entityClass 实体类(不能为空,为空将无法对返回结果进行解析)
     * @param @return
     * @return List<T>
     * @throws MongoException,Exception
     * @Title: find
     * @Description:根据过滤条件对指定集合查询 ，返回 List<T>
     */
    public static <T> List<T> find(BasicDBObject filter, Map<String, Integer> orderByMap, Map<String, Integer> sortMap, Class<T> entityClass) throws MongoException, Exception {
        MongoCollection<Document> collection = null;
        List<T> result = new ArrayList<T>();//返回结果集
        List<Document> documentList = new ArrayList<Document>();//存储查询Mongodb文档集
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:find,集合名称不能为空！！！");
            return result;
        }
        if (filter == null) filter = new BasicDBObject();//如果过滤条件为空，全部查询
        Integer pageNo = (sortMap == null || sortMap.get("pageNo") == null) ? 0 : sortMap.get("pageNo");//开始页为空，从第一条开始查询
        Integer pageSize = (sortMap == null || sortMap.get("pageSize") == null) ? Integer.MAX_VALUE : sortMap.get("pageSize");//查询条数为空，全部查询
        //mongodb排序条件对象
        BasicDBObject orderBy = new BasicDBObject();
        if (orderByMap != null && orderByMap.size() > 0) orderBy.putAll(orderByMap);

        collection = MongoManager.getDb().getCollection(collName);
        MongoCursor<Document> cursor = collection.find(filter).sort(orderBy).skip(pageNo).limit(pageSize).iterator();
        while (cursor.hasNext()) {
            documentList.add(cursor.next());
        }
        //将Mongodb对象转换为java实体对象
        result = document2Bean(documentList, entityClass);

        return result;
    }


    /**
     * @param filterMap   简单过滤条件
     * @param orderByMap  排序条件(-1是倒序，1是正序)
     * @param pageNo      页码
     * @param pageSize    每页大小
     * @param entityClass 实体类(不能为空,为空将无法对返回结果进行解析)
     * @param @return
     * @return List<T>
     * @throws MongoException,Exception
     * @Title: find
     * @Description:根据过滤条件对指定集合分页查询 ，返回 List<T>
     */
    public static <T> List<T> find(Map<String, Object> filterMap, Map<String, Integer> orderByMap, int pageNo, int pageSize, Class<T> entityClass) throws MongoException, Exception {
        MongoCollection<Document> collection = null;
        List<T> result = new ArrayList<T>();//返回结果集

        //20240716 所有mongodb加上try catch ，mongodb连不上 也不要影响程序。
        try {
            List<Document> documentList = new ArrayList<Document>();//存储查询Mongodb文档集
            //获取集合名称
            String collName = collectionName(entityClass);
            if (StrUtil.isBlank(collName)) {
                logger.error("类名:MongoDBUtil,方法名为:find,集合名称不能为空！！！");
                return result;
            }
            //mongodb过滤条件对象
            BasicDBObject filter = new BasicDBObject();
            //添加过滤条件
            filterCondition(filterMap, filter);
            //mongodb排序条件对象
            BasicDBObject orderBy = new BasicDBObject();
            if (orderByMap != null && orderByMap.size() > 0) orderBy.putAll(orderByMap);

            collection = MongoManager.getDb().getCollection(collName);
            MongoCursor<Document> cursor = collection.find(filter).sort(orderBy).skip(pageNo).limit(pageSize).iterator();
            while (cursor.hasNext()) {
                documentList.add(cursor.next());
            }
            //将Mongodb对象转换为java实体对象
            result = document2Bean(documentList, entityClass);
        } catch (Exception e) {
            logger.error("mongodb  find查询异常，" + "--->" + e);
        }
        return result;
    }

    /**
     * @param filterMap 简单查询条件
     * @param @return
     * @return List<Map < String, Object>>
     * @throws MongoException,Exception
     * @Title: find
     * @Description: 查询默认集合，返回一个结果集Map
     */
    public static List<Map<String, Object>> find(Map<String, Object> filterMap) throws MongoException, Exception {
        MongoCollection<Document> collection = null;
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();//返回结果集
        //mongodb过滤条件对象
        BasicDBObject filter = new BasicDBObject();
        //添加过滤条件
        filterCondition(filterMap, filter);
        collection = MongoManager.getDb().getCollection(ConstantMongo.DEFAULT_NAME);
        MongoCursor<Document> cursor = collection.find(filter).iterator();
        while (cursor.hasNext()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.putAll(cursor.next());
            result.add(map);
        }
        return result;
    }

    /**
     * @param entityClass 集合对应的实体类(也可为空，为空将操作默认集合)
     * @param @return     若返回-1 为异常情况
     * @return long 统计值
     * @throws MongoException,Exception
     * @Title: getCount
     * @Description: 获取集合的记录数
     */

    public static long getCount(Class<?> entityClass) throws MongoException, Exception {
        //返回结果
        long result = ConstantMongo.ERRORCODE;
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:getCount,集合名称不能为空！！！");
            return result;
        }
        //mongodb集合对象
        MongoCollection<Document> collection = null;
        collection = MongoManager.getDb().getCollection(collName);
        result = collection.count();

        return result;
    }

    /**
     * @param filterMap   过滤条件(where后面的条件)
     * @param entityClass 集合对应的实体类(也可为空，为空将操作默认集合)
     * @param @return     返回-1为异常情况
     * @return long 统计值
     * @throws MongoException,Exception
     * @Title: getCount
     * @Description: 获取集合的记录数
     */
    public static long getCount(Map<String, Object> filterMap, Class<?> entityClass) throws MongoException, Exception {
        //返回结果
        long result = ConstantMongo.ERRORCODE;
        //20240716 所有mongodb加上try catch ，mongodb连不上 也不要影响程序。
        try {
            //获取集合名称
            String collName = collectionName(entityClass);
            if (StrUtil.isBlank(collName)) {
                logger.error("类名:MongoDBUtil,方法名为:getCount,集合名称不能为空！！！");
                return result;
            }
            //mongodb过滤条件对象
            BasicDBObject filter = new BasicDBObject();
            //添加过滤条件
            filterCondition(filterMap, filter);
            //mongodb集合对象
            MongoCollection<Document> collection = null;
            collection = MongoManager.getDb().getCollection(collName);
            result = collection.count(filter);

        } catch (Exception e) {
            logger.error("mongodb  getCount查询异常，" + "--->" + e);
        }
        return result;
    }

    /**
     * @param filterMap    过滤条件(where后面符合的条件)
     * @param notfilterMap 过滤条件(where后面不符合的条件)
     * @param entityClass  集合对应的实体类(也可为空，为空将操作默认集合)
     * @param @return      返回-1为异常情况
     * @return long 统计值
     * @throws MongoException,Exception
     * @Title: getCount
     * @Description: 获取集合的记录数
     */
    public static long getCount(Map<String, Object> filterMap, Map<String, Object> notfilterMap, Class<?> entityClass) throws MongoException, Exception {
        //返回结果
        long result = ConstantMongo.ERRORCODE;
        //20240716 所有mongodb加上try catch ，mongodb连不上 也不要影响程序。
        try {
            //获取集合名称
            String collName = collectionName(entityClass);
            if (StrUtil.isBlank(collName)) {
                logger.error("类名:MongoDBUtil,方法名为:getCount,集合名称不能为空！！！");
                return result;
            }
            //mongodb过滤条件对象
            BasicDBObject filter = new BasicDBObject();
            //添加满足的过滤条件
            filterConditions(filterMap, notfilterMap, filter);
            //mongodb集合对象
            MongoCollection<Document> collection = null;
            collection = MongoManager.getDb().getCollection(collName);
            result = collection.count(filter);
        } catch (Exception e) {
            logger.error("mongodb  getCount查询异常，" + "--->" + e);
        }
        return result;
    }


    /**
     * @return void
     * @throws MongoException,Exception
     * @Title: batchInsert
     * @Description: 批量添加文档(存入默认集合内)
     */
    public static void batchInsert(List<Map<String, Object>> dataList) throws MongoException, Exception {
        //判断集合数据
        if (dataList == null || dataList.size() == 0 || dataList.get(0) == null) {
            logger.error("类名:MongoDBUtil,方法名为:batchInsert,批量添加文档传入的数据对象为空！！！");
            return;
        }
        //转化为MongDB文档对象
        List<Document> documents = new ArrayList<Document>();
        for (Map<String, Object> map : dataList) {
            Document document = new Document();
            document.putAll(map);
            documents.add(document);
        }
        //这种结构不明确直接存入默认集合内
        MongoManager.getDb().getCollection(ConstantMongo.DEFAULT_NAME).insertMany(documents);
    }

    /**
     * @return void
     * @throws MongoException,Exception
     * @Title: insert
     * @Description: 单条添加文档(存入默认集合内)
     */
    public static void insert(Map<String, Object> data) throws MongoException, Exception {
        //判断集合
        if (data == null || data.size() == 0) {
            logger.error("类名:MongoDBUtil,方法名为:insert,传入的数据Map为空！！！");
            return;
        }
        //转化为MongDB文档对象
        Document document = new Document();
        ;
        document.putAll(data);
        //存入到默认集合内
        MongoManager.getDb().getCollection(ConstantMongo.DEFAULT_NAME).insertOne(document);
    }

    /**
     * @param bean 实体类（不能为空）
     * @return void
     * @throws MongoException,Exception
     * @Title: insert
     * @Description: 单条添加文档
     */
    public static <T> void insert(T bean) throws Exception {
        if (bean == null) {
            logger.error("类名:MongoDBUtil,方法名为:insert,传入的实体类为空！！！");
            return;
        }
        String collName = collectionName(bean.getClass());
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:insert,集合名称不能为空！！！");
            return;
        }
        // 直接插入 bean
        MongoManager.getDb().getCollection(collName, (Class<T>) bean.getClass()).insertOne(bean);
    }

    /**
     * @param filterMap   过滤条件(where后面的条件)
     * @param entityClass 集合对应的实体类(也可为空，为空将操作默认集合)
     * @param @return     返回-1 为异常情况
     * @return long 返回删除操作成功记录数
     * @throws MongoException,Exception
     * @Title: delete
     * @Description: 删除符合条件第一个文档
     */
    public static long delete(Map<String, Object> filterMap, Class<?> entityClass) throws MongoException, Exception {
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:delete,集合名称不能为空！！！");
            return ConstantMongo.ERRORCODE;
        }
        if (filterMap == null || filterMap.size() < 1) {
            logger.error("类名:MongoDBUtil,方法名为:delete,过滤条件不能为空！！！");
            return ConstantMongo.ERRORCODE;
        }

        //过滤条件对象
        BasicDBObject filter = new BasicDBObject();
        //添加过滤条件
        filterCondition(filterMap, filter);
        DeleteResult result = MongoManager.getDb().getCollection(collName).deleteOne(filter);

        return result.getDeletedCount();
    }

    /**
     * @param filterMap   过滤条件(where后面的条件)
     * @param entityClass 集合对应的实体类(也可为空，为空将操作默认集合)
     * @param @return     返回-1 为异常情况
     * @return long 返回删除操作成功记录数
     * @throws MongoException,Exception
     * @Title: batchDelete
     * @Description: 删除符合条件多个文档
     */
    public static long batchDelete(Map<String, Object> filterMap, Class<?> entityClass) throws MongoException, Exception {
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:batchDelete,集合名称不能为空！！！");
            return ConstantMongo.ERRORCODE;
        }

        if (filterMap == null || filterMap.size() < 1) {
            logger.error("类名:MongoDBUtil,方法名为:batchDelete,过滤条件不能为空！！！");
            return ConstantMongo.ERRORCODE;
        }
        //mongodb过滤条件对象
        BasicDBObject filter = new BasicDBObject();
        //添加过滤条件
        filterCondition(filterMap, filter);
        DeleteResult result = MongoManager.getDb().getCollection(collName).deleteMany(filter);

        return result.getDeletedCount();
    }

    /**
     * @param filterMap   过滤条件(where后面的条件)
     * @param updateMap   修改条件(set后面的条件)
     * @param entityClass 集合对应的实体类(也可为空，为空将操作默认集合)
     * @param @return     返回-1 为异常情况
     * @return long 修改成功记录数
     * @throws MongoException,Exception
     * @Title: update
     * @Description: 修改满足条件的第一个文档
     */
    public static long update(Map<String, Object> filterMap, Map<String, Object> updateMap, Class<?> entityClass) throws MongoException, Exception {
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:update,集合名称不能为空！！！");
            return ConstantMongo.ERRORCODE;
        } else if (updateMap == null || updateMap.size() < 1) {
            logger.error("类名:MongoDBUtil,方法名为:update,传入的修改条件为空！！！");
            return ConstantMongo.ERRORCODE;
        } else if (filterMap == null || filterMap.size() < 1) {
            logger.error("类名:MongoDBUtil,方法名为:update,传入的过滤条件为空！！！");
            return ConstantMongo.ERRORCODE;
        }

        //过滤条件对象(譬如 where后面的条件)
        BasicDBObject filter = new BasicDBObject();
        //修改条件对象(譬如set后面的条件)
        Document update = new Document();
        //过滤条件
        filterCondition(filterMap, filter);
        //修改条件
        updateCondition(updateMap, update);
        UpdateResult result = MongoManager.getDb().getCollection(collName).updateOne(filter, update);

        return result.getModifiedCount();
    }

    /**
     * @param filterMap 过滤条件(where后面的条件)
     * @param bean      实体bean(不能为空)
     * @param @return   返回-1 为异常情况
     * @return long 修改成功记录数
     * @throws MongoException,Exception
     * @Title: update
     * @Description: 修改满足条件的第一个文档
     */
    public static <T> long update(Map<String, Object> filterMap, T bean) throws MongoException, Exception {
        //获取集合名称
        String collName = collectionName(bean.getClass());
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:update,集合名称不能为空！！！");
            return ConstantMongo.ERRORCODE;
        } else if (filterMap == null || filterMap.size() < 1) {
            logger.error("类名:MongoDBUtil,方法名为:update,传入的过滤条件为空！！！");
            return ConstantMongo.ERRORCODE;
        }
        //过滤条件对象(譬如 where后面的条件)
        BasicDBObject filter = new BasicDBObject();
        //过滤条件
        filterCondition(filterMap, filter);
        //修改对象(譬如set后面的条件)
        Document update = new Document(ConstantMongo.SET, bean2Document(bean));
        UpdateResult result = MongoManager.getDb().getCollection(collName).updateOne(filter, update);
        return result.getModifiedCount();
    }


    /**
     * @param filterMap   过滤条件(where后面的条件)
     * @param updateMap   修改条件(set后面的条件)
     * @param entityClass 集合对应的实体类(也可为空，为空将操作默认集合)
     * @param @return     返回-1为异常情况
     * @return long 返回修改成功的记录数
     * @throws MongoException,Exception
     * @Title: batchUpdate
     * @Description: 批量修改文档
     */

    public static long batchUpdate(Map<String, Object> filterMap, Map<String, Object> updateMap, Class<?> entityClass) throws MongoException, Exception {
        //获取集合名称
        String collName = collectionName(entityClass);
        if (StrUtil.isBlank(collName)) {
            logger.error("类名:MongoDBUtil,方法名为:batchUpdate,集合名称不能为空！！！");
            return ConstantMongo.ERRORCODE;
        } else if (updateMap == null || updateMap.size() < 1) {
            logger.error("类名:MongoDBUtil,方法名为:batchUpdate,传入的修改条件为空！！！");
            return ConstantMongo.ERRORCODE;
        } else if (filterMap == null || filterMap.size() < 1) {
            logger.error("类名:MongoDBUtil,方法名为:batchUpdate,传入的过滤条件为空！！！");
            return ConstantMongo.ERRORCODE;
        }
        //过滤条件对象(譬如 where后面的条件)
        BasicDBObject filter = new BasicDBObject();
        //修改条件对象(譬如set后面的条件)
        Document update = new Document();
        //过滤条件
        filterCondition(filterMap, filter);
        //修改条件
        updateCondition(updateMap, update);
        UpdateResult result = MongoManager.getDb().getCollection(collName).updateMany(filter, update);

        return result.getModifiedCount();

    }

    /**
     * @param filterMap 传入的过滤条件Map
     * @param filter    Mongodb过滤条件对象
     * @return void
     * @throws MongoException,Exception
     * @Title: filterCondition
     * @Description: 这里的过滤条件相为in 或者 ==
     */
    @SuppressWarnings("unchecked")
    private static void filterCondition(Map<String, Object> filterMap, BasicDBObject filter) {
        if (filterMap != null && filterMap.size() > 0) {
            // 过滤条件,多条件查询需要先定义一个BasicDBObject数组来存储多个条件
            List<BasicDBObject> objects = new ArrayList<BasicDBObject>();
            for (Map.Entry<String, Object> entry : filterMap.entrySet()) {
                if (entry.getValue() instanceof List) {
                    BasicDBList values = new BasicDBList();
                    for (Object id : (List<Object>) entry.getValue()) {
                        values.add(id);
                    }
                    objects.add(new BasicDBObject(entry.getKey().toString(), new BasicDBObject("$in", values)));
                } else {
                    objects.add(new BasicDBObject(entry.getKey().toString(), entry.getValue()));
                }
            }
            //目前的条件都是and ,如果有特殊要求再进行修改
            filter.put(ConstantMongo.AND, objects);
        }
    }

    /**
     * @param filterMap    符合条件Map(相当于where后面的in 或==)
     * @param notfilterMap 不符合条件Map(相当于where后面的notin 或!=)
     * @param filter       Mongodb过滤条件对象
     * @return void
     * @throws MongoException,Exception
     * @Title: filterConditions
     * @Description: 这里的过滤条件相为not in 或者 != 或者in 或者 ==
     */
    @SuppressWarnings("unchecked")
    private static void filterConditions(Map<String, Object> filterMap, Map<String, Object> notfilterMap, BasicDBObject filter) {
        // 过滤条件,多条件查询需要先定义一个BasicDBObject数组来存储多个条件
        List<BasicDBObject> objects = new ArrayList<BasicDBObject>();
        //in 或者 ==
        if (filterMap != null && filterMap.size() > 0) {
            for (Map.Entry<String, Object> entry : filterMap.entrySet()) {
                if (entry.getValue() instanceof List) {
                    BasicDBList values = new BasicDBList();
                    for (Object id : (List<Object>) entry.getValue()) {
                        values.add(id);
                    }
                    objects.add(new BasicDBObject(entry.getKey().toString(), new BasicDBObject(ConstantMongo.IN, values)));
                } else {
                    objects.add(new BasicDBObject(entry.getKey().toString(), entry.getValue()));
                }
            }
        }
        //not in 或者 ！=
        if (notfilterMap != null && notfilterMap.size() > 0) {
            for (Map.Entry<String, Object> entry : notfilterMap.entrySet()) {
                if (entry.getValue() instanceof List) {
                    BasicDBList values = new BasicDBList();
                    for (Object id : (List<Object>) entry.getValue()) {
                        values.add(id);
                    }
                    objects.add(new BasicDBObject(entry.getKey().toString(), new BasicDBObject(ConstantMongo.NIN, values)));
                } else {
                    objects.add(new BasicDBObject(entry.getKey().toString(), new BasicDBObject(ConstantMongo.NE, entry.getValue())));
                }
            }
        }

        //目前的条件都是and ,如果有特殊要求再进行修改
        if (objects != null && objects.size() > 0) {
            filter.put(ConstantMongo.AND, objects);
        }

    }

    /**
     * @param updateMap 传入的修改条件Map
     * @param update    Mongodb修改条件对象
     * @return void
     * @throws MongoException,Exception
     * @Title: updateCondition
     * @Description: 封装修改条件
     */
    private static void updateCondition(Map<String, Object> updateMap, Document update) {
        if (updateMap != null && updateMap.size() > 0) {
            //临时对象
            Document temp = new Document();
            for (Map.Entry<String, Object> entry : updateMap.entrySet()) {
                temp.put(entry.getKey(), entry.getValue());
            }
            //mongodb修改条件
            update.put(ConstantMongo.SET, temp);
        }
    }

    /**
     * @param entityClass 实体类
     * @param @return
     * @return String
     * @throws
     * @Title: collectionName
     * @Description:获取集合名称
     */
    private static String collectionName(Class<?> entityClass) {
        String collName = ConstantMongo.DEFAULT_NAME; //默认集合名称
        if (entityClass == null) {
            logger.error("传入的实体类参数为空，将对默认集合进行操作.");
            return collName;
        }
        collName = entityClass.getSimpleName();
        //获取集合都会添加默认前缀
        return ConstantMongo.PREFIX + collName;
    }

    /**
     * @param bean    实体类
     * @param @return 返回转换后Document对象
     * @param @throws IllegalArgumentException
     * @param @throws IllegalAccessException
     * @return Document
     * @throws
     * @Title: bean2Document
     * @Description: 将bean转换为Document对象
     */
    private static <T> Document bean2Document(T bean) throws IllegalArgumentException, IllegalAccessException {
        if (bean == null) {
            logger.error("java实体对象转换为Document对象,java实体对象为空！");
            return null;
        }
        //文档对象
        Document document = new Document();
        //获取对象对应类中的所有属性域
        Field[] fields = bean.getClass().getDeclaredFields();
        for (Field field : fields) {
            //获取属性名
            String varName = field.getName();
            if (ConstantMongo.SERIALFILED.equals(varName))//不保存序列化字段值
                continue;

            //修改访问控制权限(暂时保留)
            boolean accessFlag = field.isAccessible();
            //修改访问控制权限
            if (!accessFlag) {
                field.setAccessible(true);
            }
            Object param = field.get(bean);
            if (param == null) {//如果该字段值为空,不保存
                continue;
            } else if (param instanceof Integer) {//判断变量的类型
                int value = ((Integer) param).intValue();
                document.put(varName, value);
            } else if (param instanceof String) {
                String value = (String) param;
                document.put(varName, value);
            } else if (param instanceof Double) {
                double value = ((Double) param).doubleValue();
                document.put(varName, value);
            } else if (param instanceof Float) {
                float value = ((Float) param).floatValue();
                document.put(varName, value);
            } else if (param instanceof Long) {
                long value = ((Long) param).longValue();
                document.put(varName, value);
            } else if (param instanceof Boolean) {
                boolean value = ((Boolean) param).booleanValue();
                document.put(varName, value);
            } else if (param instanceof Date) {
                Date value = (Date) param;
                document.put(varName, value);
            } else if (param instanceof BigDecimal) {
                BigDecimal value = (BigDecimal) param;
                document.put(varName, value.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()); //保留两位小数
            } else if ((param instanceof File) || (param instanceof MultipartFile)) { //如果是文件变量 不存储
                continue;
            } else {
                /**
                 * 1、如果数据为非字符串或非基本类型需要进一步进行处理
                 * 2、方法不完善，目前只针对bean里面含有bean、List、Map,以及这三者的嵌套，以后遇到问题，再进行改进
                 * 3、因为此方法采用了递归方式进行 反射 bean属性进行存储，所以譬如 bean里面循环调用就不能采用此方法。
                 * 4、对第三条举一个栗子，比如保存bean,bean里面有一个bean1的属性,而bean1里面有一个属性是bean，这种情况无法用该方法，这样递归会出现死循环
                 */
                String packageUrl = field.getType().toString(); //获取该属性的包路径
                if ((param instanceof List) && ((List<?>) param).size() > 0) {
                    document.put(varName, toParserList(param));
                } else if ((param instanceof Map) && ((Map<?, ?>) param).size() > 0) {
                    document.put(varName, toParserMap(param));
                } else if (packageUrl.startsWith(ConstantMongo.CLASS) && packageUrl.contains(ConstantMongo.PACKAGEURL)) {
                    document.put(varName, bean2Document(param));
                }//判断这个属性是不是一个bean(如果引入外部bean再进行修改暂时这样)
            }
            //恢复访问控制权限
            field.setAccessible(accessFlag);
        }
        return document;
    }

    /**
     * @param @param  param
     * @param @return
     * @param @throws IllegalArgumentException
     * @param @throws IllegalAccessException
     * @return List<Object>
     * @throws
     * @Title: toParserList
     * @Description: 循环解析为mongodb支持存储的List结构
     */
    private static List<Object> toParserList(Object param) throws IllegalArgumentException, IllegalAccessException {
        List<Object> list = new ArrayList<Object>();
        @SuppressWarnings("unchecked")
        Iterator<Object> it = (Iterator<Object>) ((List<?>) param).iterator();
        while (it.hasNext()) {
            Object object = it.next();
            String packageUrl = object.getClass().toString();//获取该属性的包路径
            if (packageUrl.startsWith(ConstantMongo.BASETYPE) || packageUrl.startsWith(ConstantMongo.DATETYPE)) {
                list.add(object);
            } else if (object instanceof List) {
                list.add(toParserList(object));
            } else if (object instanceof Map) {
                list.add(toParserMap(object));
            } else if (packageUrl.startsWith(ConstantMongo.CLASS) && packageUrl.contains(ConstantMongo.PACKAGEURL)) {
                list.add(bean2Document(object));
            }//该属性是否为一个bean(如果引入外部bean再进行修改暂时这样)
        }
        return list;
    }

    /**
     * @param @param  param
     * @param @return
     * @param @throws IllegalArgumentException
     * @param @throws IllegalAccessException
     * @return Map<String, Object>
     * @throws
     * @Title: toParserMap
     * @Description: 循环解析为mongodb支持存储的Map结构
     */
    private static Map<String, Object> toParserMap(Object param) throws IllegalArgumentException, IllegalAccessException {
        Map<String, Object> map = new HashMap<String, Object>();
        @SuppressWarnings("unchecked")
        Iterator<Map.Entry<String, Object>> it = ((Map<String, Object>) param).entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Object> entry = it.next();
            String key = entry.getKey();
            Object value = entry.getValue();
            String packageUrl = value.getClass().toString();//获取该属性的包路径
            if (packageUrl.startsWith(ConstantMongo.BASETYPE) || packageUrl.startsWith(ConstantMongo.DATETYPE)) {
                map.put(key, value);
            } else if (value instanceof List) {
                map.put(key, toParserList(value));
            } else if (value instanceof Map) {
                map.put(key, toParserMap(value));
            } else if (packageUrl.startsWith(ConstantMongo.CLASS) && packageUrl.contains(ConstantMongo.PACKAGEURL)) {
                map.put(key, bean2Document(value));
            }//判断是否是一个bean(如果引入外部bean再进行修改暂时这样)
        }
        return map;
    }

    /**
     * @param documents   集合对象
     * @param entityClass 目标类
     * @param @return
     * @param @throws     InstantiationException
     * @param @throws     IllegalAccessException
     * @param @throws     InvocationTargetException
     * @return List<T>
     * @throws Exception
     * @throws
     * @Title: document2Bean
     * @Description: 将Document对象转换为实体对象
     */
    private static <T> List<T> document2Bean(List<Document> documents, Class<T> entityClass) throws Exception {
        List<T> list = new ArrayList<>();
        if (documents == null || documents.isEmpty()) {
            logger.error("Document对象转化为java实体对象,Document对象为空！");
            return list;
        }
        for (Document document : documents) {
            T bean = entityClass.getDeclaredConstructor().newInstance();
            recyleDocument(document, bean);
            list.add(bean);
        }
        return list;
    }

    /**
     * @param @param  document
     * @param @param  bean
     * @param @throws Exception
     * @return void
     * @throws
     * @Title: recyleDocument
     * @Description: 递归文档对象
     */
    private static void recyleDocument(Document document, Object bean) throws Exception {
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(bean.getClass());
        for (PropertyDescriptor pd : propertyDescriptors) {
            String varName = pd.getName();
            if ("class".equals(varName)) continue;
            Class<?> paramType = pd.getPropertyType();
            Object value = document.get(varName);
            if (value == null) continue;
            recyleValue(varName, value, paramType, bean, pd);
        }
    }

    /**
     * @param @param  key
     * @param @param  value
     * @param @param  param
     * @param @param  bean
     * @param @throws Exception
     * @return void
     * @throws
     * @Title: recyleValue
     * @Description: 递归文档对象中每一个字段
     */
    private static void recyleValue(String key, Object value, Class<?> param, Object bean, PropertyDescriptor pd) throws Exception {
        if (value instanceof List && List.class.isAssignableFrom(param)) {
            pd.getWriteMethod().invoke(bean, toRecyleList(value));
        } else if (value instanceof Map && Map.class.isAssignableFrom(param)) {
            pd.getWriteMethod().invoke(bean, toRecyleMap(value));
        } else if (value instanceof Document && !param.isPrimitive() && !param.getName().startsWith("java.")) {
            Object nestedBean = param.getDeclaredConstructor().newInstance();
            recyleDocument((Document) value, nestedBean);
            pd.getWriteMethod().invoke(bean, nestedBean);
        } else {
            pd.getWriteMethod().invoke(bean, value);
        }
    }

    /**
     * @param @param  value
     * @param @return
     * @return List<Object>
     * @throws
     * @Title: toRecyleList
     * @Description: 将mongodb中存入List类型的对象转为java List类型
     */
    private static List<Object> toRecyleList(Object value) {
        List<Object> list = new ArrayList<Object>();
        @SuppressWarnings("unchecked")
        Iterator<Object> it = (Iterator<Object>) ((List<?>) value).iterator();
        while (it.hasNext()) {
            Object object = it.next();
            String packageUrl = object.getClass().toString();//获取该属性的包路径
            if (packageUrl.startsWith(ConstantMongo.BASETYPE) || packageUrl.startsWith(ConstantMongo.DATETYPE)) {
                list.add(object);
            } else if (object instanceof List) {
                list.add(toRecyleList(object));
            } else if (object instanceof Map) {
                list.add(toRecyleMap(object));
            } else {
                //遇到特殊的再进行处理
            }
        }
        return list;

    }

    /**
     * @param @param  param
     * @param @return
     * @return Map<String, Object>
     * @throws
     * @Title: toRecyleMap
     * @Description: 将mongodb中存入Map类型的对象转为java Map类型
     */
    private static Map<String, Object> toRecyleMap(Object param) {
        Map<String, Object> map = new HashMap<String, Object>();
        @SuppressWarnings("unchecked")
        Iterator<Map.Entry<String, Object>> it = ((Map<String, Object>) param).entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Object> entry = it.next();
            String key = entry.getKey();
            Object value = entry.getValue();
            String packageUrl = value.getClass().toString();//获取该属性的包路径
            if (packageUrl.startsWith(ConstantMongo.BASETYPE) || packageUrl.startsWith(ConstantMongo.DATETYPE)) {
                map.put(key, value);
            } else if (value instanceof List) {
                map.put(key, toRecyleList(value));
            } else if (value instanceof Map) {
                map.put(key, toRecyleMap(value));
            } else {
                //遇到特殊的再进行处理
            }
        }
        return map;
    }


    /**
     * @param @param  bean
     * @param @param  key
     * @param @return
     * @param @throws Exception
     * @return Class<?>
     * @throws
     * @Title: getPropertyType
     * @Description: 反射获取bean实例
     */
    private static Class<?> getPropertyType(Object bean, String key) throws Exception {
        Class<?> clazz = bean.getClass();
        Field f = clazz.getDeclaredField(key);
        Class<?> t = f.getType();
        if (t.isAssignableFrom(List.class)) {
            Type gt = f.getGenericType();
            if (gt == null) {
                return t;
            }
            if (gt instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) gt;
                Class<?> genericClazz = (Class<?>) pt.getActualTypeArguments()[0];
                if (genericClazz != null) {
                    return genericClazz;
                }
            }
        }
        return t;
    }

//    public static void main(String[] args) throws MongoException, Exception {
//        SqMessage sqMessage = new SqMessage();
//        sqMessage.setSqId(212L);
//        sqMessage.setResourceNum(BigDecimal.ONE);
//        try {
////            MongoDBUtil.insert(sqMessage);
//            Object byId = MongoDBUtil.findById(212, SqMessage.class);
//            System.out.println("----");
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }

}
