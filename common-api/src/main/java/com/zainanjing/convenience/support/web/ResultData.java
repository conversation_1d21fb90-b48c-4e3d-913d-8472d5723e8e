package com.zainanjing.convenience.support.web;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.HashMap;
import java.util.Map;

/**
 * 订阅号标准返回值， 仅针对api-2c接口
 */
public class ResultData {
    private int resCode;

    private String resMessage;

    private Object resData;

    public ResultData() {
        this.resCode = 0;
        this.resMessage = "OK";
    }

    public ResultData(int nCode, String errorMessage) {
        this.resCode = nCode;
        this.resMessage = errorMessage;
    }

    public ResultData(Object obj) {
        this.resCode = 0;
        this.resMessage = "OK";
        this.resData = obj;
    }

    public ResultData(int nCode, String errorMessage, Object obj) {
        this.resCode = nCode;
        this.resMessage = errorMessage;
        this.resData = obj;
    }

    private ResultData(IPage page) {
        this.resCode = 0;
        this.resMessage = "OK";
        this.resData = new HashMap();
        Map<String, Object> data = (Map<String, Object>) this.resData;
        data.put("objs", page.getRecords());
        data.put("pageNo", page.getCurrent());
        data.put("pageSize", page.getSize());
        data.put("totalCount", page.getTotal());
        data.put("totalPages", page.getPages());
    }

    public void addContentData(String sKey, Object objValue) {
        if (this.resData == null)
            this.resData = new HashMap();
        Map<String, Object> data = (Map<String, Object>) this.resData;
        data.put(sKey, objValue);
    }

    public int getCode() {
        return this.resCode;
    }

    public void setCode(int resCode) {
        this.resCode = resCode;
    }

    public String getMessage() {
        return this.resMessage;
    }

    public void setMessage(String resMessage) {
        this.resMessage = resMessage;
    }

    public Object getContent() {
        return this.resData;
    }

    public void setContent(Object o) {
        this.resData = o;
    }
}
