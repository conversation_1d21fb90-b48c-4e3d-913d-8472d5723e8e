package com.zainanjing.convenience.support.interceptor;


import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.convenience.support.usercenter.GdmmUserInfoClient;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.lang.reflect.Method;

/**
 * 鉴权token拦截器
 *
 * <AUTHOR>
 */
public class CTokenAuthInterceptor implements HandlerInterceptor {

    @Resource
    private GdmmUserInfoClient gdmmUserInfoClient;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object handler) {
        // 如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();

        // 判断接口是否需要登录
        Anonymous methodAnnotation = method.getAnnotation(Anonymous.class);

        String authHeader = httpServletRequest.getHeader("authorization");
        //执行jwt头部校验
        if (null == authHeader && methodAnnotation == null) {
            httpServletResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return false;
        }
        boolean state = gdmmUserInfoClient.checkToken(authHeader);
        if (!state && methodAnnotation == null) {
            //验证失败
            httpServletResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return false;
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {
        SecurityUtils.removeLoginUser();
    }
}
