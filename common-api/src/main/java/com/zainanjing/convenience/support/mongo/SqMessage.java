package com.zainanjing.convenience.support.mongo;

import lombok.Getter;
import lombok.Setter;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.math.BigDecimal;

@Setter
@Getter
public class SqMessage implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @BsonProperty("_id")
    private ObjectId _id;

    // 私信ID
    @BsonProperty("id")
    private Long sqId;

    // 私信类型 1文字 2图片
    private Integer type;
    // 私信内容
    private String content;
    // 发件已读 1是0否
    private Integer senderRead;
    // 收件已读 1是0否
    private Integer receiverRead;
    // 发件删除 1是0否
    private Integer senderDelete;
    // 收件删除 1是0否
    private Integer receiverDelete;
    // 发件人uid
    private Long sender;
    // 收件人uid
    private Long receiver;
    //私信双方uid  下划线分隔  小的在前  222_333
    private String twoUid;
    // 创建时间
    private Integer createTime;
    // 更新时间
    private Integer updateTime;
    //状态 0正常   1删除
    private Integer status;


    /*
     * 以下是非表字段
     *
     */
    private Long sqMessageId;//与id相等  返回给前端用
    private Long friendUid = 0L;//发消息对象的uid
    private Integer friendAvatarType = 0;//0表示php那边的图片   1表示java
    private String friendImageUrl = "";//发消息对象的头像
    private String friendName = "";//发消息对象的昵称
    private String friendPhone = "";//发消息对象的手机号
    private Long unReadNum = 0L;//未读消息数


    //以下是 type 6抢购，拍卖，涨价拍，7社区通知，8关注，9交易 产生的存于mongodb中的系统消息=================================
    //61 抢购 62 竞拍 63 涨价拍 71 评论 72点赞 73打赏 81 关注 91发货 92 签收（去评价） 93退货申请成功 94 退货申请失败
    private String code;
    //TODO 6 9 合并
    //6 抢购
    private String productThumbnail; //商品图片
    private String productTitle;//商品简介
    private Integer qiangGouTime;//抢购时间
    private Long goodsId;//商品id
    //6 竞拍
    private BigDecimal bid;//竞拍出价
    //	private Integer endTime;//结束时间 与涨价拍共用
//	private String productThumbnail; //商品图片 与 抢购共用 
    //6 涨价拍
    private BigDecimal lastBid;//最新出价
    private Integer endTime;//结束时间

    //7 社区通知
    //社区通知类型 BCPOST:广播帖 BCCOMMENT:广播评论 SQPOST:社区帖 SQCOMMENT:社区评论	GOODSCOMMENT:商品评价
    private String sqType;
    //资源id sqType对应的主键 广播帖子id 广播评论id 社区帖子id 社区评论id 商品评论id
    private Long resourceId;
    private BigDecimal resourceNum; //数量（可扩展） 如果code=73是被打赏猫粮
    //帖子或评论被人评论时 优先显示  1 帖子首图  2 帖子主题  3 帖子内容
    private String imgUrl;//1帖子首图
    //2帖子主题 (资源名称 sqType对应的名称 广播帖子名称 广播评论名称 社区帖子名称 社区评论名称 商品名称)
    private String resourceName;
    private String postContent;//3帖子内容

    //8 关注
    private Long otherUid;// 对方id 比如谁关注了你
    private String otherName;// 对方昵称 比如谁关注了你
    private String otherImgUrl;// 对方头像 比如谁关注了你
    //9 交易消息
    private Long orderId;//订单主键
    private Integer orderStatus; //订单状态
    private String shippingName; //物流公司
    private String goodsName;//商品名
    private Integer goodsNum;//商品数量
    //	private String productThumbnail; //商品图片 这个与抢购共用字段
    private String expressNo;//运单号
    //以上是 抢购，涨价拍，社区通知，交易 产生的存于mongodb中的系统消息=================================

    //10 订阅号消息20250320
    private Integer postId;//订阅号帖子id
    private Integer postType;//订阅号帖子类型
    private Integer commentId; //订阅号评论id
    private String videoUrl;//视频地址


}
