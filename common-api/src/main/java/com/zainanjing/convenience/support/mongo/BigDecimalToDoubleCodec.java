package com.zainanjing.convenience.support.mongo;

import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;

import java.math.BigDecimal;

public class BigDecimalToDoubleCodec implements Codec<BigDecimal> {
    @Override
    public BigDecimal decode(BsonReader reader, DecoderContext context) {
        return BigDecimal.valueOf(reader.readDouble());
    }

    @Override
    public void encode(BsonWriter writer, BigDecimal value, EncoderContext context) {
        writer.writeDouble(value.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
    }

    @Override
    public Class<BigDecimal> getEncoderClass() {
        return BigDecimal.class;
    }
}