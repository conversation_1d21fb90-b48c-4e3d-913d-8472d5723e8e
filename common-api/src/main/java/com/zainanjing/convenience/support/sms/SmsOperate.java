package com.zainanjing.convenience.support.sms;

import java.util.Map;

/**
 * 短信操作
 *
 * <AUTHOR>
 */
public interface SmsOperate {

    /**
     * 短信验证码通知
     *
     * @param phoneNum     手机号码
     * @param templateCode 模板号
     * @param code         验证码
     * @return 执行反馈
     */
    boolean sendCode(String phoneNum, String templateCode, String code);

    /**
     * 短信消息通知
     *
     * @param phoneNum     手机号码
     * @param templateCode 模板号
     * @param params       可替换参数
     * @return 执行反馈
     */
    boolean sendMessage(String phoneNum, String templateCode, Map<String, String> params);


}
