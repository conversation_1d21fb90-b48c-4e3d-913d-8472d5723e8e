package com.zainanjing.convenience.support.mongo;

import com.mongodb.*;
import com.mongodb.client.MongoDatabase;
import com.ruoyi.common.utils.StrUtil;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: MongoManager
 * @Description: 连接MongoDB 工具类
 * @date 2017年5月15日 上午9:52:19
 */

@Component
@Lazy(false)
public class MongoManager {

    protected static final Logger logger = LoggerFactory.getLogger(MongoManager.class);

    private static MongoClient client = null; // 连接Mongodb客户端对象
    private static MongoDatabase db = null;// 默认数据库对象
    private static String HOST;
    private static int PORT = 27017;
    private static String USERNAME;
    private static String PASSWORD;
    private static String DATABASE;
    private static int PERHOST = 10;
    private static int TIMEOUT = 1000 * 5 * 1;
    private static int MAXWAITMILLIS = 1000 * 5 * 2;
    private static int BLOCKFORTHREADS = 5;
    private static int IDLETIME = 0;
    private static int LIFETIME = 0;
    private static int SOCKETTIMEOUT = 0;
    private static boolean KEEPALIVE = true;

    @Value("${spring.data.mongodb.host:''}")
    public void setHost(String host) {
        HOST = host;
    }

    @Value("${spring.data.mongodb.port:27017}")
    public void setPort(int port) {
        PORT = port;
    }

    @Value("${spring.data.mongodb.username:''}")
    public void setUsername(String username) {
        USERNAME = username;
    }

    @Value("${spring.data.mongodb.password:''}")
    public void setPassword(String password) {
        PASSWORD = password;
    }

    @Value("${spring.data.mongodb.database:''}")
    public void setDatabase(String database) {
        DATABASE = database;
    }


    /**
     * 初始化MongoDB连接
     */
    private static void initMongoDB() {
        logger.debug("连接MongoDB服务器,初始化一个连接实例开始！！！");
        try {
            if (StrUtil.isEmpty(HOST)) {
                return;
            }
            // 连接地址
            ServerAddress serverAddress = new ServerAddress(HOST, PORT);
            /*
             * 安全连接配置，此配置必须用户名和密码
             */
            MongoCredential credentials = MongoCredential.createCredential(
                    USERNAME, DATABASE, PASSWORD.toCharArray());
            List<MongoCredential> credentialsList = new ArrayList<MongoCredential>();
            credentialsList.add(credentials);
            /*
             * 连接池配置
             */
            MongoClientOptions.Builder build = new MongoClientOptions.Builder();
            build.connectionsPerHost(PERHOST);// 与目标数据库可以建立的最大链接数
            build.connectTimeout(TIMEOUT);// 与数据库建立链接的超时时间(1min)
            build.maxWaitTime(MAXWAITMILLIS);// 一个线程成功获取到一个可用数据库之前的最大等待时间(2min)
            build.threadsAllowedToBlockForConnectionMultiplier(BLOCKFORTHREADS);//如果当前所有的connection都在使用中，则每个connection上可以有50个线程排队等待
            build.maxConnectionIdleTime(IDLETIME);
            build.maxConnectionLifeTime(LIFETIME);
            build.socketTimeout(SOCKETTIMEOUT);
            build.socketKeepAlive(KEEPALIVE);
            build.codecRegistry(CodecRegistries.fromRegistries(
                    CodecRegistries.fromProviders(new CustomCodecProvider()),
                    client.getDefaultCodecRegistry(),
                    CodecRegistries.fromProviders(
                            PojoCodecProvider.builder().automatic(true).build())
            ));
            MongoClientOptions myOptions = build.build();

            //client = new MongoClient(serverAddress, myOptions);
            //测试阶段暂时不用安全连接
            client = new MongoClient(serverAddress, credentialsList, myOptions);

            if (client != null) {
                //获取默认数据库对象
                db = client.getDatabase(DATABASE);
            }
            //
            if (db == null) {
                throw new MongoException("连接MongoDB服务器,初始化一个连接实例,并获取默认数据库为null");
            }
            logger.debug("连接MongoDB服务器,初始化一个连接实例结束！！！");
        } catch (Exception e) {
            logger.error("mongoDB连接参数host{},dataBase:{},userName:{},passWord:{},port{}", HOST, DATABASE, USERNAME, PASSWORD, PORT);
            logger.error("连接MongoDB服务器,初始化一个连接实例出现异常！！！", e);
        }
    }

    /**
     * @param @return
     * @return MongoClient
     * @throws
     * @Title: getInstance
     * @Description: 获取mongoDB实例
     */
    public static MongoClient getInstance() {
        if (client == null) {
            initMongoDB();
        }
        return client;
    }

    /**
     * @param @return
     * @return MongoDatabase
     * @throws
     * @Title: getDb
     * @Description: 获取数据库
     */

    public static MongoDatabase getDb() {
        if (db != null)
            return db;

        return getInstance().getDatabase(DATABASE);
    }


    // 关闭客户端连接
    public static void close() {
        if (client != null) {
            client.close();
            client = null;
            db = null;
        }
    }

}