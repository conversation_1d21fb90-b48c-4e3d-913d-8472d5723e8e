package com.zainanjing.convenience.support.captcha;

/**
 * 验证码操作
 *
 * <AUTHOR>
 */
public interface CaptchaOperation {

    /**
     * 发送验证码
     *
     * @param phoneNumber 手机号码
     * @param code        验证码
     * @param captchaType 验证码类型
     * @param smsTemplate 短信模板
     * @return true/false
     */
    boolean send(String phoneNumber, String code, CaptchaType captchaType, String smsTemplate);

    /**
     * 验证码校验
     *
     * @param phoneNumber 手机号码
     * @param code        验证码
     * @param captchaType 验证码类型
     * @return 执行反馈
     */
    boolean check(String phoneNumber, String code, CaptchaType captchaType);


    /**
     * 获取验证码
     * @param phoneNumber
     * @param captchaType
     * @return
     */
    String getCode(String phoneNumber, CaptchaType captchaType);


    /**
     * 失效验证码
     *
     * @param captchaType
     * @param phoneNum
     * @return
     */
    boolean invalidate(CaptchaType captchaType, String phoneNum);

    /**
     * 发送回执验证码
     *
     * @param phoneNumber
     * @param orderId
     * @param code
     * @param captchaType
     * @param smsTemplate
     * @return
     */
    boolean send(String phoneNumber, Long orderId, String code, CaptchaType captchaType, String smsTemplate);

    /**
     * 发送短信消息至商家
     *
     * @param phoneNumber  商家号码
     * @param orderId      订单编号
     * @param userID       用户名称
     * @param templateCode 模板号码
     * @return 执行反馈
     */
    boolean notifyMerchant(String phoneNumber, String orderId, String userID, String templateCode);
}
