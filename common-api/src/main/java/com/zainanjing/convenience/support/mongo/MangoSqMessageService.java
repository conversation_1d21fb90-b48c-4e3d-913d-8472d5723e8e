package com.zainanjing.convenience.support.mongo;

import com.mongodb.MongoException;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class MangoSqMessageService {

    private final Logger log = LoggerFactory.getLogger(MangoSqMessageService.class);

    public SqMessage initSqMessageSq(String sqType, Long resourceId, String resourceName, String postContent,
                                     BigDecimal resourceNum, String imgUrl, Long otherUid, String otherName, String otherImgUrl) {
        SqMessage sqMessage = new SqMessage();
        //社区通知类型 BCPOST:广播帖 BCCOMMENT:广播评论 SQPOST:社区帖 SQCOMMENT:社区评论	GOODSCOMMENT:商品评价
        sqMessage.setSqType(sqType);
        //资源id sqType对应的主键 广播帖子id 广播评论id 社区帖子id 社区评论id 商品评论id
        sqMessage.setResourceId(resourceId);
        //资源名称 sqType对应的名称 广播帖子名称 广播评论名称 社区帖子名称 社区评论名称 商品名称
        sqMessage.setResourceName(resourceName);
        sqMessage.setPostContent(postContent);
        sqMessage.setResourceNum(resourceNum); //数量（可扩展） 如果code=73是被打赏猫粮
        sqMessage.setImgUrl(imgUrl);//帖子首图
        sqMessage.setOtherUid(otherUid);
        sqMessage.setOtherName(otherName);
        sqMessage.setOtherImgUrl(otherImgUrl);
        return sqMessage;
    }

    public void saveMongodbSqMessage(SqMessage sqMessage, Integer type, String code, Long uid, String content) {
        if (sqMessage == null) {
            sqMessage = new SqMessage();
        }
        sqMessage.setSqId(CommonUtil.redisCommonAdd(RedisKey.SQ_MESSAGE_ID.getKey()));//这里有可能是0！！！
        sqMessage.setContent(content);
        sqMessage.setType(type);
        sqMessage.setCode(code);
        sqMessage.setReceiver(uid);
        sqMessage.setSender(76659l);//迁移过来写死
        sqMessage.setTwoUid(makeTwoUidString(76659l, uid));
        sqMessage.setSenderDelete(Constants.FOU);
        sqMessage.setSenderRead(Constants.SHI);
        sqMessage.setReceiverDelete(Constants.FOU);
        sqMessage.setReceiverRead(Constants.FOU);
        Integer now = CommonUtil.getTimestamp();
        sqMessage.setCreateTime(now);
        sqMessage.setUpdateTime(now);
        sqMessage.setStatus(Constants.FOU);
        try {
            MongoDBUtil.insert(sqMessage);
        } catch (Exception e) {
            log.error("保存消息失败", e);
            throw new MongoException("保存消息失败");
        }
    }

    public String makeTwoUidString(Long uid, Long friendUidLong) {
        StringBuffer sb = new StringBuffer();
        //这里不考虑2个人的uid相等的情况
        if (uid.compareTo(friendUidLong) == -1) {
            sb.append(uid);
            sb.append("_");
            sb.append(friendUidLong);
        } else if (uid.compareTo(friendUidLong) == 1) {
            sb.append(friendUidLong);
            sb.append("_");
            sb.append(uid);
        }
        return sb.toString();
    }
}
