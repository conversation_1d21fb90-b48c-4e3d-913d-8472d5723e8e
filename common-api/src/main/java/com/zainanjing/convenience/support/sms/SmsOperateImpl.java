package com.zainanjing.convenience.support.sms;

import com.ruoyi.common.utils.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.core.service.ISysConfigService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.sign.SignUtil;
import com.zainanjing.convenience.modules.dict.domain.DictData;
import com.zainanjing.convenience.modules.dict.service.DictDataService;
import com.zainanjing.convenience.support.APISystemConfig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 短信通知阿里云实现
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SmsOperateImpl implements SmsOperate {

    @Autowired
    private SmsConfig smsConfig;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private DictDataService dictDataService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 产品名称:云通信短信API产品,开发者无需替换
     */
    private static final String PRODUCT = "Dysmsapi";

    /**
     * 产品域名,开发者无需替换
     */
    private static final String DOMAIN = "dysmsapi.aliyuncs.com";


    @Override
    public boolean sendCode(String phoneNum, String templateCode, String code) {
        String key = "sendNumLimit:" + phoneNum;
        Boolean hasKey = stringRedisTemplate.hasKey(key);
        if (!hasKey) {
            stringRedisTemplate.opsForValue().set(key, "1", 24, TimeUnit.HOURS);
        } else {
            Long increment = stringRedisTemplate.opsForValue().increment(key);
            if (increment > 30) {
                throw new ServiceException("发送次数超限");
            }
        }

        Map<String, String> params = new HashMap<>(1);
        params.put("code", code);
        return sendMessage(phoneNum, templateCode, params);
    }

    @Override
    public boolean sendMessage(String phoneNum, String templateCode, Map<String, String> params) {
        if (!APISystemConfig.smsFlag) {
            return true;//不开启不真实的发短信
        }
        //通过系统参数判断
        String smsType = sysConfigService.selectConfigByKey("sys.sms.type");
        if ("BINLI".equalsIgnoreCase(smsType)) {
            return binliSend(phoneNum, templateCode, params);
        } else {
            return aliSend(phoneNum, templateCode, params);
        }
    }

    private boolean binliSend(String phoneNum, String templateCode, Map<String, String> params) {
        if (StrUtil.isEmpty(SmsConfig.BINLI_ACCOUNT) || StrUtil.isEmpty(SmsConfig.BINLI_PASSWORD) || StrUtil.isEmpty(SmsConfig.BINLI_URL)) {
            log.error("滨礼品账号未配置");
            return false;
        }
        DictData smsTemplate = dictDataService.findByDictCode("sms_template", templateCode);
        if (null == smsTemplate) {
            log.error("短信模版内容未配置");
            return false;
        }
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("content-type", "application/json;charset=UTF-8");
        //根据模版code 获取模版内容，根据模版内容替换参数
        //设置请求参数

        JsonNode respJson = null;
        try {

            respJson = HttpUtil.post(SmsConfig.BINLI_URL, Map.of("account", SmsConfig.BINLI_ACCOUNT,
                    "pwd", SignUtil.md5(SmsConfig.BINLI_PASSWORD),
                    "mobiles", phoneNum,
                    "content", StrUtil.format(smsTemplate.getDictName(), params)));
            JsonNode code = respJson.get("code");
            if (code != null && "00".equalsIgnoreCase(code.asText())) {
                log.info("滨礼短信接口成功,respJson:" + respJson);
                return true;
            } else {
                log.error("滨礼接口返回值:{}", respJson);
            }
        } catch (Exception e) {
            log.error("滨礼短信接口失败,respJson:" + respJson);
        }
        return false;
    }

    private boolean aliSend(String phoneNum, String templateCode, Map<String, String> params) {
        return false;
    }
}
