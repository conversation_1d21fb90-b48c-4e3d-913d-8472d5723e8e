package com.zainanjing.convenience.support.usercenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GdmmUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String uid;

    private String username;

    private String mobile;

    private String headimg;

    private String title;

    private String medalLevel;

    private String gdmmInfo;//用户接口调用

    private String userId;

    private String mobilePhone;

    private String hasAttenEachOther;

    @JsonIgnore
    public String getGdmmInfo() {
        return gdmmInfo;
    }

    @JsonProperty
    public void setGdmmInfo(String gdmmInfo) {
        this.gdmmInfo = gdmmInfo;
    }

    @JsonIgnore
    public String getMobilePhone() {
        return mobilePhone;
    }

    @JsonProperty
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    @JsonIgnore
    public String getUserId() {
        return userId;
    }

    @JsonProperty
    public void setUserId(String userId) {
        this.userId = userId;
        this.uid = String.valueOf(userId);
    }

    public void setUsername(String username){
        this.username = username;
    }

    public void setUserName(String userName){
        this.username = userName;
    }

}
