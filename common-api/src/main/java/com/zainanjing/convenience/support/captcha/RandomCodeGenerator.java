package com.zainanjing.convenience.support.captcha;

import java.util.Random;

/**
 * 随机码生成(数字)
 *
 * <AUTHOR>
 */
public class RandomCodeGenerator {

    /**
     * 执行
     *
     * @param digits 位数
     * @return 随机码
     */
    public static String exec(int digits) {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < digits; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
}
