package com.zainanjing.convenience.support.mongo;

import org.bson.codecs.Codec;
import org.bson.codecs.configuration.CodecProvider;
import org.bson.codecs.configuration.CodecRegistry;

import java.math.BigDecimal;

public class CustomCodecProvider implements CodecProvider {
    @Override
    @SuppressWarnings("unchecked")
    public <T> Codec<T> get(Class<T> clazz, CodecRegistry registry) {
        if (clazz == BigDecimal.class) {
            return (Codec<T>) new BigDecimalToDoubleCodec();
        }
        return null;
    }
}