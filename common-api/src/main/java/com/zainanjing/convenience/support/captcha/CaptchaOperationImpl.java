package com.zainanjing.convenience.support.captcha;

import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.base.BaseException;
import com.zainanjing.convenience.support.sms.SmsOperate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 验证码相关操作实现
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CaptchaOperationImpl implements CaptchaOperation {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SmsOperate smsOperate;

    @Override
    public boolean send(String phoneNumber, String code, CaptchaType captchaType, String smsTemplate) {
        boolean state;

        // 执行短信发送
        if (smsOperate.sendCode(phoneNumber, smsTemplate, code)) {
            //发送成功
            // 执行redis中保存并设置过期
            // 过期时间5分钟
            int expire = 5;
            try {
                stringRedisTemplate.opsForValue().set(getKey(phoneNumber, captchaType), code, expire, TimeUnit.MINUTES);
                state = true;
            } catch (Exception e) {
                log.error("验证码保存至redis出错:", e);
                state = false;
            }
        } else {
            // 发送失败
            state = false;
        }
        return state;
    }

    @Override
    public boolean check(String phoneNumber, String code, CaptchaType captchaType) {
        String savedCode;
        try {
            String key = getKey(phoneNumber, captchaType);
            savedCode = stringRedisTemplate.opsForValue().get(key);
            if (code.equals(savedCode)) {
                stringRedisTemplate.delete(key);
                return true;
            } else if (StrUtil.isNotBlank(savedCode)) {
                String checkKey = "checkMsgLimit:" + key;
                Boolean hasKey = stringRedisTemplate.hasKey(key);
                if (!hasKey) {
                    stringRedisTemplate.opsForValue().set(checkKey, "1", 5, TimeUnit.MINUTES);
                } else {
                    Long increment = stringRedisTemplate.opsForValue().increment(checkKey);
                    if (increment > 3) {
                        stringRedisTemplate.delete(key);
                        throw new ServiceException("请重新发送验证码");
                    }
                }
                return false;
            }
            return false;
        } catch (Exception e) {
            log.error("验证码从redis中获取失败:", e);
            return false;
        }
    }

    @Override
    public String getCode(String phoneNumber, CaptchaType captchaType) {
        String savedCode;
        try {
            savedCode = stringRedisTemplate.opsForValue().get(getKey(phoneNumber, captchaType));
            return savedCode;
        } catch (Exception e) {
            log.error("验证码从redis中获取失败:", e);
        }
        return "";
    }

    @Override
    public boolean invalidate(CaptchaType captchaType, String phoneNum) {
        boolean state;
        try {
            String cacheKey = getKey(phoneNum, captchaType);
            stringRedisTemplate.delete(cacheKey);
            state = true;
        } catch (Exception e) {
            log.error("废弃验证码失效:", e);
            state = false;
        }
        return state;
    }

    /**
     * 发送回执验证码
     *
     * @param phoneNumber 手机号码
     * @param orderId     订单号
     * @param code        验证码
     * @param captchaType 验证码类型
     * @param smsTemplate 短信模板
     * @return 执行反馈
     */
    @Override
    public boolean send(String phoneNumber, Long orderId, String code, CaptchaType captchaType, String smsTemplate) {
        boolean state;

        // 执行短信发送
        if (smsOperate.sendCode(phoneNumber, smsTemplate, code)) {
            //发送成功
            // 执行redis中保存并设置过期
            // 过期时间5分钟
            int expire = 5;
            try {
                stringRedisTemplate.opsForValue().set(getKey(phoneNumber + orderId, captchaType), code, expire, TimeUnit.MINUTES);
                state = true;
            } catch (Exception e) {
                log.error("验证码保存至redis出错:", e);
                state = false;
            }
        } else {
            // 发送失败
            state = false;
        }
        return state;
    }

    @Override
    public boolean notifyMerchant(String phoneNumber, String orderId, String userID, String templateCode) {
        boolean state;
        Map<String, String> params = new HashMap<>(2);
        params.put("orderID", orderId);
        params.put("userID", userID);
        try {
            smsOperate.sendMessage(phoneNumber, templateCode, params);
            state = true;
            log.info("通知商家短信发送:成功-[phoneNumber:{}]", phoneNumber);
        } catch (Exception e) {
            state = false;
            log.error("通知商家短信发送失败:", e);
        }
        return state;
    }

    private String getKey(String phoneNumber, CaptchaType captchaType) {
        return captchaType.toString().toLowerCase() + "-" + phoneNumber;
    }
}
