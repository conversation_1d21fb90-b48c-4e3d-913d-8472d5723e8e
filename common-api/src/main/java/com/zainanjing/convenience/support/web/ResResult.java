package com.zainanjing.convenience.support.web;

/**
 * 平台标准返回值
 */
public class ResResult {
    /**
     *
     */
    // 错误代码
    private Integer error;
    // 提示消息
    private String msg;
    // 返回的数据
    private Object data;

    public ResResult() {
    }

    public ResResult(Integer error, String msg) {
        this.error = error;
        this.msg = msg;
    }

    public ResResult(Integer error, String msg, Object data) {
        this.error = error;
        this.msg = msg;
        this.data = data;
    }

    public Integer getError() {
        return error;
    }

    public void setError(Integer error) {
        this.error = error;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public static ResResult success() {
        return success(null);
    }

    public static ResResult success(Object data) {
        return success("成功", data);
    }

    public static ResResult successMsg(String msg) {
        return success(msg,  null);
    }

    public static ResResult success(String msg, Object data) {
        return new ResResult(200, msg, data);
    }

    public static ResResult error() {
        return error("失败");
    }

    public static ResResult error(String msg) {
        return error(msg,  null);
    }

    public static ResResult error(String msg, Object data) {
        return new ResResult(500, msg, data);
    }

    public static ResResult error(int code, String msg) {
        return new ResResult(code, msg,  null);
    }

}
