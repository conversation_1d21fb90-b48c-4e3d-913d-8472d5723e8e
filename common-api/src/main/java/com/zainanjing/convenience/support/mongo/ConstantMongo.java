package com.zainanjing.convenience.support.mongo;


/**
 * 
 * @ClassName: ConstantMongo 
 * @Description: MongoDB常量类
 * <AUTHOR>
 * @date 2017年5月19日 上午9:13:37 
 *
 */
public class ConstantMongo {
	
	protected static long ERRORCODE = -1; //默认操作失败返回值（修改、删除和统计功能中作为操作失败的返回记录值）
	
	protected static String DEFAULT_NAME = "gdmm.default"; //默认集合名称
	
	protected static String PREFIX = "gdmm.";//集合名称前缀
	
	protected static String PACKAGEURL = "zainanjing365"; //本地bean包部分路径
	
	protected static String BASETYPE ="class java.lang";//java 字符串和基本类型 的包部分路径
	
	protected static String DATETYPE ="class java.util.Date";//java Date对象的包路径
	
	protected static String MAPTYPE = "java.util.Map";//java Map对象的包路径
	
	protected static String CLASS = "class"; //包路径中的 关键词
	
	protected static String BIGDECIMALTYPE = "java.math.BigDecimal"; //BigDecimal 包路径中的 关键词
	
	protected static String SERIALFILED = "serialVersionUID";//序列化字段名称
	
	
	/**
	 * mongodb查询条件常量
	 */
	protected static String AND = "$and"; // and 
	protected static String OR = "$or";  //or
	protected static String EQ = "$eq"; // ==
	protected static String NE = "$ne"; // !=
	
	protected static String GT = "$gt";  //>
	protected static String GTE = "$gte"; //>=
	
	protected static String LT = "$lt";  //<
	protected static String LTE = "$lte"; //<=
	
	protected static String IN = "$in"; // in 
	protected static String NIN = "$nin";//not in

	protected static String SORT = "$sort"; // 排序关键字
	protected static String SET = "$set"; //修改 set 关键字 
	
	protected static String INC = "$inc";
	
}
