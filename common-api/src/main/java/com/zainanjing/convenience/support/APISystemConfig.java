package com.zainanjing.convenience.support;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 */
@Lazy(false)
@Component
public class APISystemConfig {

    public static Boolean wangyiCheck;

    public static Boolean smsFlag;

    public static String userCenterUrl;

    public static String siteAppUrl;

    public static String siteAppToken;

    public static String siteAppUid;

    public static Boolean getWangyiCheck() {
        return wangyiCheck;
    }

    @Value("${system.wangyi-check:true}")
    public void setWangyiCheck(Boolean wangyiCheck) {
        APISystemConfig.wangyiCheck = wangyiCheck;
    }

    public static String apiUrl;

    public String getApiUrl() {
        return apiUrl;
    }

    @Value("${system.api-url:''}")
    public void setApiUrl(String apiUrl) {
        APISystemConfig.apiUrl = apiUrl;
    }

    public static String adminUrl;

    public String getAdminUrl() {
        return adminUrl;
    }

    @Value("${system.admin-url:''}")
    public void setAdminUrl(String adminUrl) {
        APISystemConfig.adminUrl = adminUrl;
    }

    public Boolean getSmsFlag() {
        return smsFlag;
    }

    @Value("${system.sms-flag:true}")
    public void setSmsFlag(Boolean smsFlag) {
        APISystemConfig.smsFlag = smsFlag;
    }

    public String getUserCenterUrl() {
        return userCenterUrl;
    }

    @Value("${system.user-center-url}")
    public void setUserCenterUrl(String userCenterUrl) {
        APISystemConfig.userCenterUrl = userCenterUrl;
    }

    public String getSiteAppUrl() {
        return siteAppUrl;
    }

    @Value("${system.site-app-url}")
    public void setSiteAppUrl(String siteAppUrl) {
        APISystemConfig.siteAppUrl = siteAppUrl;
    }

    public String getSiteAppToken() {
        return siteAppToken;
    }

    @Value("${system.site-app-token:}")
    public void setSiteAppToken(String siteAppToken) {
        APISystemConfig.siteAppToken = siteAppToken;
    }

    public String getSiteAppUid() {
        return siteAppUid;
    }

    @Value("${system.site-app-uid}")
    public void setSiteAppUid(String siteAppUid) {
        APISystemConfig.siteAppUid = siteAppUid;
    }
}
