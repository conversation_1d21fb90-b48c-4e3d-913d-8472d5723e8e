package com.zainanjing.convenience.support.sms;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 短信配置
 *
 * <AUTHOR>
 */
@Component
@Lazy(false)
public class SmsConfig {

    /**
     * 短信accessKeyId
     */
    public static String ACCESSKEY_ID;

    @Value("${sms.accessKey:}")
    public void setAccesskeyId(String accesskeyId) {
        SmsConfig.ACCESSKEY_ID = accesskeyId;
    }

    /**
     * 短信accessKeySecret
     */
    public static String ACCESSKEY_SECRET;

    @Value("${sms.accessSecret:}")
    public  void setAccesskeySecret(String accesskeySecret) {
        ACCESSKEY_SECRET = accesskeySecret;
    }

    @Value("${sms.template.commonCaptchaTemplate}")
    public  void setCommonCaptchaTemplate(String commonCaptchaTemplate) {
        COMMON_CAPTCHA_TEMPLATE = commonCaptchaTemplate;
    }

    @Value("${sms.template.forgetPwdCaptchaTemplate}")
    public  void setForgetPwdCaptchaTemplate(String forgetPwdCaptchaTemplate) {
        FORGET_PWD_CAPTCHA_TEMPLATE = forgetPwdCaptchaTemplate;
    }

    @Value("${sms.template.registerCaptchaTemplate}")
    public  void setRegisterCaptchaTemplate(String registerCaptchaTemplate) {
        REGISTER_CAPTCHA_TEMPLATE = registerCaptchaTemplate;
    }

    @Value("${sms.template.receiptCaptchaTemplate}")
    public  void setReceiptCaptchaTemplate(String receiptCaptchaTemplate) {
        RECEIPT_CAPTCHA_TEMPLATE = receiptCaptchaTemplate;
    }

    @Value("${sms.template.notifyMerchantTemplate}")
    public  void setNotifyMerchantTemplate(String notifyMerchantTemplate) {
        NOTIFY_MERCHANT_TEMPLATE = notifyMerchantTemplate;
    }

    @Value("${sms.template.storeCloseTemplate}")
    public  void setStoreCloseTemplate(String storeCloseTemplate) {
        STORE_CLOSE_TEMPLATE = storeCloseTemplate;
    }

    /**
     * 通用验证码模板
     */
    public static String COMMON_CAPTCHA_TEMPLATE ;

    /**
     * 忘记密码验证码短信模板
     */
    public static String FORGET_PWD_CAPTCHA_TEMPLATE;

    /**
     * 注册验证码短信模板
     */
    public static String REGISTER_CAPTCHA_TEMPLATE;

    /**
     * 回执验证码短信模板
     */
    public static String RECEIPT_CAPTCHA_TEMPLATE;

    /**
     * 5分钟通知商户接单短信模板
     */
    public static String NOTIFY_MERCHANT_TEMPLATE;

    /**
     * 店铺注销短信模板
     */
    public static String STORE_CLOSE_TEMPLATE;

    public static String BINLI_ACCOUNT;

    public static String BINLI_PASSWORD;

    public static String BINLI_URL;

    @Value("${sms.binli.account}")
    public void setBinliAccount(String binliAccount) {
        BINLI_ACCOUNT = binliAccount;
    }

    @Value("${sms.binli.password}")
    public void setBinliPassword(String binliPassword) {
        BINLI_PASSWORD = binliPassword;
    }

    @Value("${sms.binli.url}")
    public void setBinliUrl(String binliUrl) {
        BINLI_URL = binliUrl;
    }
}
