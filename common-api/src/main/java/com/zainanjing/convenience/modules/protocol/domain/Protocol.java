package com.zainanjing.convenience.modules.protocol.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 商家入驻条款
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-25
 */
@TableName("sys_protocol")
public class Protocol implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	@TableId
	private Long id;
	/**
	 * 协议名称
	 */
	private String protocolName;
	/**
	 * 内容
	 */
	private String content;

	public String getProtocolName() {
		return protocolName;
	}

	public void setProtocolName(String protocolName) {
		this.protocolName = protocolName;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
}
