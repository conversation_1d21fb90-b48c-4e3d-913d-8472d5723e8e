package com.zainanjing.convenience.modules.file.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件管理对象 sys_file_info
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@TableName(value = "sys_file_info")
public class SysFileInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文件code
     */
    @TableId(type = IdType.INPUT)
    private String fileCode;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件类型
     */
    @JsonIgnore
    private String fileType;

    /**
     * 文件路径
     */
    @JsonIgnore
    private String filePath;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 创建者
     */
    @JsonIgnore
    private String createBy;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 更新者
     */
    @JsonIgnore
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;

}
