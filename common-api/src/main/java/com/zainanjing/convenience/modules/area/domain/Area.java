package com.zainanjing.convenience.modules.area.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 平台产品类别
 * </p>
 *
 * <AUTHOR>
 * @since 2018-09-07
 */
@TableName("sys_area")
@Data
public class Area {

	private static final long serialVersionUID = 1L;

	@TableId("id")
	private Long id;

	private String areaName;
	private Long parentId;
	@JsonIgnore
	private String route;
	@JsonIgnore
	private String routeName;
	@JsonIgnore
	private Integer lvl;
	/**
	 * 是否删除
	 */
	@JsonIgnore
	private boolean deleted;
	@JsonIgnore
	private Date createTime;
	@JsonIgnore
	private Integer sortNum;
	@JsonIgnore
	private Long createrId;
	@JsonIgnore
	private Date lastModifyTime;
	@JsonIgnore
	private Long lastModifierId;

}