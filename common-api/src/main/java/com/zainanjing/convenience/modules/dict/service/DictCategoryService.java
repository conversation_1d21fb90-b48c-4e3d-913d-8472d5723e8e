package com.zainanjing.convenience.modules.dict.service;


import com.zainanjing.convenience.modules.dict.domain.DictCategory;
import com.ruoyi.common.core.service.GenericCurdService;

/**
 * Created by AllanLoo on 2017/6/27.
 */
public interface DictCategoryService extends GenericCurdService<DictCategory> {

    /**
     * 根据id获取字典类型
     * @param aLong
     * @return
     */
    DictCategory getById(Long aLong);

    /**
     * 根据类型编码获取类型
     * @param categoryCode
     * @return
     */
    DictCategory findByCategoryCode(String categoryCode);

    void removeById(Long id);
}
