package com.zainanjing.convenience.modules.protocol.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.convenience.modules.protocol.constant.EnumProtocol;
import com.zainanjing.convenience.modules.protocol.domain.Protocol;
import com.zainanjing.convenience.modules.protocol.mapper.ProtocolMapper;
import com.zainanjing.convenience.modules.protocol.service.ProtocolService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商家入驻条款 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-25
 */
@Service
public class ProtocolServiceImpl extends GenericCurdServiceImpl<ProtocolMapper, Protocol> implements ProtocolService {
    private ProtocolMapper protocolMapper;

    @Override
    public String getProtocolContent(EnumProtocol enumProtocol) {
        Protocol protocol = this.lambdaQuery().eq(Protocol::getProtocolName, enumProtocol.toString()).one();
        if (protocol == null) {
            return "";
        }
        return protocol.getContent();
    }
}
