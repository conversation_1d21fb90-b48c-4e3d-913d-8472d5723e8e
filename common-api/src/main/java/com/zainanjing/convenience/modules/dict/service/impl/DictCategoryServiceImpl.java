package com.zainanjing.convenience.modules.dict.service.impl;


import com.zainanjing.convenience.modules.dict.mapper.DictCategoryMapper;
import com.zainanjing.convenience.modules.dict.domain.DictCategory;
import com.zainanjing.convenience.modules.dict.service.DictCategoryService;
import com.zainanjing.convenience.modules.dict.service.DictDataService;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Created by AllanLoo on 2017/6/27.
 */
@Service
public class DictCategoryServiceImpl  extends GenericCurdServiceImpl<DictCategoryMapper, DictCategory>
        implements DictCategoryService {

    private DictCategoryMapper dictCategoryMapper;

    @Autowired
    private DictDataService dictDataService;

    @Override
    public DictCategory getById(Long aLong) {
        return this.getById(aLong);
    }

    @Override
    public DictCategory findByCategoryCode(String categoryCode) {
        List<DictCategory> dictCategoryList = this.lambdaQuery().eq(DictCategory::getCategoryCode, categoryCode).list();
        if(dictCategoryList != null && dictCategoryList.size() > 0){
            return dictCategoryList.get(0);
        }
        return null;
    }

    @Transactional
    @Override
    public void removeById(Long id) {
        DictCategory dictCategory = this.getById(id);
        if(null != dictCategory) {
            dictDataService.removeByCategoryId(dictCategory.getCategoryCode());
            this.removeById(id);
        }
    }
}
