package com.zainanjing.convenience.modules.dict.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.convenience.modules.dict.domain.DictData;
import com.zainanjing.convenience.modules.dict.mapper.DictDataMapper;
import com.zainanjing.convenience.modules.dict.service.DictDataService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * Created by Allan<PERSON>oo on 2017/6/27.
 */
@Service
public class DictDataServiceImpl extends GenericCurdServiceImpl<DictDataMapper, DictData> implements DictDataService {

    @Override
    public DictData getById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<DictData> findByCategoryCode(String categoryCode) {
        return this.lambdaQuery().eq(DictData::getCategoryCode, categoryCode).orderByAsc(DictData::getSortNo).list();
    }

    @Override
    public DictData findByDictCode(String categoryCode, String dictCode) {
        List<DictData> dictDatas = this.lambdaQuery().eq(DictData::getDictCode, dictCode).eq(DictData::getCategoryCode, categoryCode).list();
        if (dictDatas != null && dictDatas.size() > 0) {
            return dictDatas.get(0);
        }
        return null;
    }

    @Override
    public void removeByCategoryId(String categoryCode) {
        this.lambdaUpdate().eq(DictData::getCategoryCode, categoryCode).remove();
    }
}
