package com.zainanjing.convenience.modules.dict.service;


import com.zainanjing.convenience.modules.dict.domain.DictData;
import com.ruoyi.common.core.service.GenericCurdService;
import java.util.List;

/**
 * Created by AllanLoo on 2017/6/27.
 */
public interface DictDataService extends GenericCurdService<DictData> {

    DictData getById(Long id);

    List<DictData> findByCategoryCode(String categoryCode);

    DictData findByDictCode(String categoryCode, String dictCode);

    /**
     * 根据类别id删除
     * @param categoryCode
     */
    void removeByCategoryId(String categoryCode);
}
