package com.zainanjing.convenience.modules.protocol.service;

import com.zainanjing.convenience.modules.protocol.constant.EnumProtocol;
import com.zainanjing.convenience.modules.protocol.domain.Protocol;
import com.ruoyi.common.core.service.GenericCurdService;

/**
 * <p>
 * 商家入驻条款 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-25
 */
public interface ProtocolService extends GenericCurdService<Protocol> {
    /**
     * 获取协议内容
     * @param enumProtocol
     * @return
     */
	String getProtocolContent(EnumProtocol enumProtocol);
}
