package com.zainanjing.convenience.modules.dict.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;


/**
 * 字典类型表
 * Created by AllanLoo on 2017/6/27.
 */
@TableName("sys_dict_type")
public class DictCategory implements Serializable {


    @TableId("dict_id")
    private Long id;

    /**
     * 类别编码
     */
    @TableField("dict_type")
    private String categoryCode;
    /**
     * 类别名称
     */
    @TableField("dict_name")
    private String categoryName;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField("create_by")
    private Long creater;

    @TableField("create_time")
    private Date createTime;
    @TableField(value = "update_by")
    private Long lastModifier;

    @TableField("update_time")
    private Date lastModifyTime;

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreater() {
        return creater;
    }

    public void setCreater(Long creater) {
        this.creater = creater;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getLastModifier() {
        return lastModifier;
    }

    public void setLastModifier(Long lastModifier) {
        this.lastModifier = lastModifier;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
