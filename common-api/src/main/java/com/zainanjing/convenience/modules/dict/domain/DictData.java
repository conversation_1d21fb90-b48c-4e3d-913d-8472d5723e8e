package com.zainanjing.convenience.modules.dict.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;


/**
 * Created by AllanLoo on 2017/6/27.
 * 字典数据
 * 类别下面的小类数据
 */
@TableName("sys_dict_data")
public class DictData implements Serializable {

    @TableId("dict_code")
    private Long id;

    /**
     * 类别id
     */
    @TableField(exist = false)
    private Long categoryId;

    @TableField("dict_type")
    private String categoryCode;
    /**
     *  编码
     */
    @TableField("dict_value")
    private String dictCode;
    /**
     * 字典名称
     */
    @TableField("dict_label")
    private String dictName;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * 排序编号
     */
    @TableField("dict_sort")
    private Integer sortNo;

    @TableField("create_by")
    private String creater;

    @TableField("create_time")
    private Date createTime;
    @TableField(value = "update_by")
    private String lastModifier;

    @TableField("update_time")
    private Date lastModifyTime;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getDictCode() {
        return dictCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getLastModifier() {
        return lastModifier;
    }

    public void setLastModifier(String lastModifier) {
        this.lastModifier = lastModifier;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }
}
