package com.zainanjing.convenience.handlers.endpointb.dto;

import com.zainanjing.convenience.support.captcha.CaptchaType;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 通用验证码入参
 *
 * <AUTHOR>
 */
@Data
public class CaptchaParamDTO {
    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不可为空")
    @Length(min = 11, max = 11, message = "手机号码长度必须为11位")
    private String phoneNumber;

    /**
     * 验证码类型
     */
    private CaptchaType captchaType = CaptchaType.COMMON;

    private String txTicket;

    private String txRandstr;

}
