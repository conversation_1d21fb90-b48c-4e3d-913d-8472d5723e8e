package com.zainanjing.convenience.handlers.endpointb.dto;

import com.zainanjing.convenience.support.captcha.CaptchaType;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 通用验证码校验入参
 *
 * <AUTHOR>
 */
@Data
public class CaptchaCheckParamDTO {

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不可为空")
    @Length(min = 11, max = 11, message = "手机号码长度必须为11位")
    private String phoneNumber;

    @NotBlank(message = "验证码不可为空")
    @Length(min = 6, max = 6, message = "必须提供6位验证码")
    private String code;

    /**
     * 验证码类型
     */
    private CaptchaType captchaType = CaptchaType.COMMON;
}
