package com.zainanjing.convenience.handlers.endpointc;

import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.convenience.modules.dict.service.DictDataService;
import com.zainanjing.convenience.modules.file.service.ISysFileInfoService;
import com.zainanjing.convenience.support.web.ResultData;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController(value = "2cDictController")
@RequestMapping("/api-2c")
public class DictController {

    @Autowired
    private DictDataService dictDataService;

    @Resource
    private ISysFileInfoService sysFileInfoService;

    @Anonymous
    @GetMapping("/v1.0/dict/list")
    public ResultData getDictListByCa(@RequestParam(value = "categoryCode") String categoryCode) {
        ResultData resultData = new ResultData();
        resultData.setContent(dictDataService.findByCategoryCode(categoryCode));
        return resultData;
    }

    @Anonymous
    @PostMapping("/v1.0/dict/lists")
    public ResultData getDictListByCategoryCodes(@RequestBody Map<String, List<String>> categoryCodeMap) {
        ResultData resultData = new ResultData();
        if (null != categoryCodeMap && categoryCodeMap.get("categoryCodes") != null) {
            categoryCodeMap.get("categoryCodes").forEach(categoryCode -> resultData.addContentData(categoryCode, dictDataService.findByCategoryCode(categoryCode)));
        }
        return resultData;
    }

    /**
     * 根据code获取文件地址
     */
    @Anonymous
    @GetMapping("/v1.0/sys/getFile")
    public ResultData getFile(@RequestParam(value = "code") String code) {
        ResultData resultData = new ResultData();
        resultData.setContent(sysFileInfoService.getById(code));
        return resultData;
    }
}
