package com.zainanjing.convenience.handlers.endpointc;

import com.zainanjing.convenience.modules.protocol.constant.EnumProtocol;
import com.zainanjing.convenience.modules.protocol.service.ProtocolService;
import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.convenience.support.web.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by AllanLoo on 2018/9/6.
 */
@RestController("2cProtocolController")
@RequestMapping("/api-2c")
public class ProtocolController {
    @Autowired
    private ProtocolService protocolService;

    /**
     * 统一协议接口
     * @param name
     * @return
     */
    @GetMapping("/v1.0/protocol/{name}")
    @Anonymous
    public ResultData getProtocolV10(@PathVariable String name){
        String content = protocolService.getProtocolContent(EnumProtocol.valueOf(name));
        return new ResultData(content);
    }

}
