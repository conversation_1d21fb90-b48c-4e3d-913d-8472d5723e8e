package com.zainanjing.convenience.handlers.endpointc;

import com.ruoyi.common.annotation.Anonymous;
import com.zainanjing.convenience.handlers.endpointc.dto.AreaResDto;
import com.zainanjing.convenience.modules.area.domain.Area;
import com.zainanjing.convenience.modules.area.service.AreaService;
import com.zainanjing.convenience.utils.ArraysToTreeUtil;
import com.zainanjing.convenience.support.web.ResultData;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController("2cAreaController")
@RequestMapping("/api-2c")
public class AreaController {

    @Autowired
    private AreaService areaService;

    @Anonymous
    @GetMapping("/v1.0/area/listNames")
    public ResultData areaListNames() {
        ResultData responseData = new ResultData();
        List<Area> areas = areaService.findAll();
        List<AreaResDto> collect = areas.stream().map(area -> {
            AreaResDto areaResDto = new AreaResDto();
            BeanUtils.copyProperties(area, areaResDto);
            return areaResDto;
        }).collect(Collectors.toList());

        List<AreaResDto> tree = new ArraysToTreeUtil<AreaResDto>()
                .code(AreaResDto::getId)
                .parent(AreaResDto::getParentId)
                .children(menu -> {
                    if (menu.getChildren() == null) {
                        menu.setChildren(new ArrayList<>());
                    }
                    return menu.getChildren();
                }).tree(collect);

        responseData.setContent(tree);
        return responseData;
    }

}
