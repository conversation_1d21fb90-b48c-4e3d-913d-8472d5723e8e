package com.zainanjing.convenience.handlers.endpointc;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.ip.IpUtils;
import com.zainanjing.convenience.handlers.endpointb.dto.CaptchaParamDTO;
import com.zainanjing.convenience.support.APISystemConfig;
import com.zainanjing.convenience.support.captcha.CaptchaOperation;
import com.zainanjing.convenience.support.captcha.CaptchaType;
import com.zainanjing.convenience.support.captcha.RandomCodeGenerator;
import com.zainanjing.convenience.support.sms.SmsConfig;
import com.zainanjing.convenience.utils.TencentCaptchaUtil;
import com.zainanjing.convenience.support.web.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 验证码Controller
 *
 * <AUTHOR>
 */
@RestController(value = "2cCaptchaController")
@RequestMapping("/api-2c")
public class CaptchaController {

    @Autowired
    private CaptchaOperation captchaOperation;

    @Resource
    private TencentCaptchaUtil tencentCaptchaUtil;

    /**
     * 获取通用验证码
     *
     * @param captchaParamDTO 验证码入参
     * @return 执行反馈
     */
    @PostMapping("/v1.0/getCommonCaptcha")
    public ResultData getCommonCaptchaV10(@Valid @RequestBody CaptchaParamDTO captchaParamDTO, HttpServletRequest request) {
        ResultData resultData = new ResultData();
        if(!tencentCaptchaUtil.verify("",captchaParamDTO.getPhoneNumber(),captchaParamDTO.getTxTicket(), IpUtils.getIpAddr(request),captchaParamDTO.getTxRandstr())){
            resultData.setCode(2045);
            resultData.setMessage("腾讯验证码失败!");
            return resultData;
        }
        boolean result = captchaOperation.send(captchaParamDTO.getPhoneNumber(), RandomCodeGenerator.exec(6), captchaParamDTO.getCaptchaType(), SmsConfig.COMMON_CAPTCHA_TEMPLATE);
        if (result) {
            resultData.setCode(0);
            resultData.setMessage("验证码发送成功!");
        } else {
            resultData.setCode(2045);
            resultData.setMessage("获取验证码失败!");
        }
        return resultData;
    }

    /**
     * 获取通用验证码
     *
     * @param phone 验证码入参
     * @return 执行反馈
     */
    @GetMapping("/v1.0/getSendCaptchaValue/{phone}")
    public ResultData getSendCaptchaValue(@PathVariable String phone) {
        if(APISystemConfig.smsFlag){
            return new ResultData(-1,"正式发送无法从此接口获取");
        }
        String code = captchaOperation.getCode(phone, CaptchaType.COMMON);
        return new ResultData(code);
    }

}
