package com.zainanjing.convenience.handlers.endpointc;


import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.sign.SignUtil;
import com.zainanjing.convenience.support.web.ResultData;
import com.zainanjing.convenience.utils.WeChartUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Random;

@RestController("2cShareController")
@RequestMapping("/api-2c")
public class ShareController {

    @Anonymous
    @GetMapping("/share/v1/wechat")
    public ResultData getWechatShareInfo(@RequestParam String url) {
        ResultData resultData = new ResultData();
        String appId = WeChartUtil.WEIXIN_APPID;
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonceStr = getNonceStr();
        String signature = getSign(WeChartUtil.getWeixinTicket(), URLDecoder.decode(url, StandardCharsets.UTF_8), timestamp, nonceStr);
        resultData.addContentData("appId", appId);
        resultData.addContentData("timestamp", timestamp);
        resultData.addContentData("nonceStr", nonceStr);
        resultData.addContentData("signature", signature);
        return resultData;
    }

    private String getNonceStr() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new SecureRandom();
        StringBuilder nonceStr = new StringBuilder(32);
        for (int i = 0; i < 32; i++) {
            nonceStr.append(chars.charAt(random.nextInt(chars.length())));
        }
        return nonceStr.toString();
    }

    private String getSign(String jsapiTicket, String url, String oldTimeStamp, String oldNonceStr) {
        String string = "jsapi_ticket=" + jsapiTicket + "&noncestr=" + oldNonceStr + "&timestamp=" + oldTimeStamp + "&url=" + url;
        return SignUtil.sha1(string);
    }

}
