package com.zainanjing.convenience.handlers.endpointc;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.convenience.support.APISystemConfig;
import com.zainanjing.official.constant.ArticleConstant;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.service.IArticleService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller("2cNewsPageController")
@RequestMapping("/api-2c")
public class NewsPageController {

    @Resource
    private IArticleService iArticleService;

    @Anonymous
    @GetMapping("/page/article/{id}")
    public void getPage(@PathVariable Long id,
                        @RequestParam(value = "displayMode", required = false) String displayMode,
                        @RequestParam(value = "appscheme", required = false) String appscheme,
                        @RequestParam(value = "mode", defaultValue = "0") Integer mode,
                        Model model, HttpServletResponse response) {
        response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY); // 设置 301 状态码
        // 获取新闻
        Article article = iArticleService.lambdaQuery().eq(Article::getId, id)
                .eq(mode != 1, Article::getEnabled, 0)
                .eq(mode != 1, Article::getStatus, 0)
                .select(Article::getId, Article::getType)
                .last("limit 1")
                .one();

        if (ObjUtil.isNotNull(article)) {
            String url = "";
            String urlParams = (StrUtil.isBlank(appscheme) ? "" : ("&appscheme=" + appscheme))
                    + (StrUtil.isBlank(displayMode) ? "" : ("&displayMode=" + displayMode))
                    + (ObjUtil.notEqual(0,mode) ? ("&mode=" + mode) : "" );
            if (ObjUtil.equals(article.getType(), ArticleConstant.ARTICLE_TYPE_NORMAL_SPLIT)) {
                url = String.format("%s/h5/official/news4.html?id=%d%s",
                        APISystemConfig.adminUrl, id, urlParams);
            } else if (ObjUtil.equals(article.getType(), ArticleConstant.ARTICLE_TYPE_COLLECT)) {
                url = String.format("%s/h5/official/collection.html?id=%d%s",
                        APISystemConfig.adminUrl, id, urlParams);
            } else if (ObjUtil.equals(article.getType(), ArticleConstant.ARTICLE_TYPE_VIDEO)) {
                url = String.format("%s/h5/official/video.html?id=%d%s",
                        APISystemConfig.adminUrl, id, urlParams);
            } else if (ObjUtil.equals(article.getType(), ArticleConstant.ARTICLE_TYPE_NORMAL)) {
                url = String.format("%s/h5/official/article.html?id=%d%s",
                        APISystemConfig.adminUrl, id, urlParams);
            }
            if (StrUtil.isNotBlank(url)) {
                response.setHeader("Location", url);
                return;
            }
        }

        response.setHeader("Location", APISystemConfig.adminUrl + "/404.html");

    }
}
