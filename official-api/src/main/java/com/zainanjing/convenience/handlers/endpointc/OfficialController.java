package com.zainanjing.convenience.handlers.endpointc;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.service.ISysConfigService;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.convenience.support.APISystemConfig;
import com.zainanjing.convenience.support.captcha.CaptchaOperation;
import com.zainanjing.convenience.support.captcha.CaptchaType;
import com.zainanjing.convenience.support.usercenter.GdmmUserInfo;
import com.zainanjing.convenience.support.usercenter.GdmmUserInfoClient;
import com.zainanjing.convenience.support.web.ResultData;
import com.zainanjing.convenience.utils.RestSiteAppUtil;
import com.zainanjing.official.constant.ArticleConstant;
import com.zainanjing.official.constant.OfficialConfig;
import com.zainanjing.official.domain.*;
import com.zainanjing.official.dto.OfficialAccountDTO;
import com.zainanjing.official.event.OfAccountEvent;
import com.zainanjing.official.service.*;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController("2cOfficialController")
@RequestMapping("/api-2c")
public class OfficialController {

    protected static final Logger logger = LoggerFactory.getLogger(OfficialController.class);

    @Autowired
    private IOfficialAccountService iOfficialAccountService;

    @Autowired
    private IOfficialFollowService iOfficialFollowService;

    @Autowired
    private IArticleService iArticleService;

    @Autowired
    private IOfficialAccountCategoryService iOfficialAccountCategoryService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IOfficialAccountApplyService iOfficialAccountApplyService;

    @Autowired
    private IOfficialArticleCollectService iOfficialArticleCollectService;

    @Autowired
    private IOfficialArticleLikeService iOfficialArticleLikeService;

    @Resource
    private GdmmUserInfoClient gdmmUserInfoClient;

    @Resource
    private RestSiteAppUtil restSiteAppUtil;

    @Resource
    private IOfficialOperLogService officialOperLogService;

    @Resource
    private IArticleCollectionRefService iArticleCollectionRefService;

    @Value("${system.official.black-flag:false}")
    private boolean blackFlag;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 订阅号分类（全部）
     *
     * @return ResultData
     */
    @Anonymous
    @GetMapping("/v1.0/official/categories")
    public ResultData officialCategories() {
        QueryWrapper<OfficialAccountCategory> ew = new QueryWrapper<>();
        ew.eq("deleted", 0);
        ew.in("category_type", "0", "1");
        ew.orderByDesc("sort");
        List<OfficialAccountCategory> officialAccountCategories = iOfficialAccountCategoryService.list(ew);
        return new ResultData(officialAccountCategories);
    }

    /**
     * 订阅号分类（全部）按照类型查询
     *
     * @return ResultData
     */
    @Anonymous
    @GetMapping("/v1.1/official/categoriesByType")
    public ResultData officialCategoriesByTypeV11(@RequestParam(required = false, value = "categoryType") String categoryType,
                                                  @RequestParam(required = false, value = "recommended") String recommended) {
        QueryWrapper<OfficialAccountCategory> ew = new QueryWrapper<>();
        ew.eq("deleted", 0);
        ew.eq(StrUtil.isNotBlank(categoryType), "category_type", categoryType);
        ew.eq(StrUtil.isNotBlank(recommended), "recommended", recommended);

        ew.orderByDesc("sort");
        List<OfficialAccountCategory> officialAccountCategories = iOfficialAccountCategoryService.list(ew);

        return new ResultData(officialAccountCategories);
    }

    /**
     * 订阅号分类（全部）按照类型查询
     *
     * @return ResultData
     */
    @Anonymous
    @GetMapping("/v1.0/official/categoriesByType")
    public ResultData officialCategoriesByType(@RequestParam(required = false, value = "categoryType") String categoryType,
                                               @RequestParam(required = false, value = "recommended") String recommended) {
        QueryWrapper<OfficialAccountCategory> ew = new QueryWrapper<>();
        ew.eq("deleted", 0);
        ew.eq(StrUtil.isNotBlank(categoryType), "category_type", categoryType);
        ew.eq(StrUtil.isNotBlank(recommended), "recommended", recommended);
        ew.in("category_type", "0", "1");

        ew.orderByDesc("sort");
        List<OfficialAccountCategory> officialAccountCategories = iOfficialAccountCategoryService.list(ew);

        return new ResultData(officialAccountCategories);
    }


    @Autowired
    private CaptchaOperation captchaOperation;

    /**
     * 新增申请
     *
     * @return ResultData
     */
    @Log(title = "订阅号申请", businessType = BusinessType.UPDATE)
    @PostMapping("/v1.0/official/apply")
    public ResultData officialApply(@Valid @RequestBody OfficialAccountApply officialAccountApply) {
        //初始化固定数据
        officialAccountApply.setStatus(0);
        officialAccountApply.setCreateBy(StrUtil.toStringOrNull(SecurityUtils.getUserId()));

        if (ObjUtil.equals(officialAccountApply.getApplyType(), Integer.valueOf(1))) {//如果是个人，机构的参数制空
            officialAccountApply.setOrganCode("");
            officialAccountApply.setOrganCodeImg("");
            officialAccountApply.setOrganGrantImg("");
            officialAccountApply.setOrganName("");
            officialAccountApply.setOrganType("");
        }

        //数据更新
        if (officialAccountApply.getId() != null) {
            QueryWrapper<OfficialAccount> ew = new QueryWrapper<>();
            ew.eq("apply_id", officialAccountApply.getId());
            if (iOfficialAccountService.count(ew) > 0) {
                officialAccountApply.setStatus(3);
            } else {
                if (StrUtil.isEmpty(officialAccountApply.getSmsCode()) || !captchaOperation.check(officialAccountApply.getApplicantPhone(), officialAccountApply.getSmsCode(), CaptchaType.COMMON)) {
                    return new ResultData(2046, "验证码不正确");
                }
            }
            //需判断创建人不可以更新
            OfficialAccountApply officialAccountApplyOld = iOfficialAccountApplyService.getById(officialAccountApply.getId());
            if (!officialAccountApplyOld.getCreateBy().equals(officialAccountApply.getCreateBy())) {
                return new ResultData(500, "申请人不一致");
            }
            iOfficialAccountApplyService.updateById(officialAccountApply);
        } else {
            if (StrUtil.isEmpty(officialAccountApply.getSmsCode()) || !captchaOperation.check(officialAccountApply.getApplicantPhone(), officialAccountApply.getSmsCode(), CaptchaType.COMMON)) {
                return new ResultData(2046, "验证码不正确");
            }
            //确认验证码
            iOfficialAccountApplyService.save(officialAccountApply);
        }
        return new ResultData();
    }

    /**
     * 新增申请（版本V1.1)
     *
     * @return ResultData
     */
    @Log(title = "订阅号申请", businessType = BusinessType.UPDATE)
    @PostMapping("/v1.1/official/apply")
    public ResultData officialApplyV11(@Valid @RequestBody OfficialAccountApply officialAccountApply) {
        //初始化固定数据
        officialAccountApply.setStatus(0);
        officialAccountApply.setCreateBy(StrUtil.toStringOrNull(SecurityUtils.getUserId()));

        officialAccountApply.setApplyType(1);
        officialAccountApply.setOrganCode("");
        officialAccountApply.setOrganCodeImg("");
        officialAccountApply.setOrganGrantImg("");
        officialAccountApply.setOrganName("");
        officialAccountApply.setOrganType("");

        //数据更新
        if (officialAccountApply.getId() != null) {
            QueryWrapper<OfficialAccount> ew = new QueryWrapper<>();
            ew.eq("apply_id", officialAccountApply.getId());
            if (iOfficialAccountService.count(ew) > 0) {
                officialAccountApply.setStatus(3);
            }
            //需判断创建人不可以更新
            OfficialAccountApply officialAccountApplyOld = iOfficialAccountApplyService.getById(officialAccountApply.getId());
            if (!officialAccountApplyOld.getCreateBy().equals(officialAccountApply.getCreateBy())) {
                return new ResultData(500, "申请人不一致");
            }
            iOfficialAccountApplyService.updateById(officialAccountApply);
        } else {
            //确认验证码
            iOfficialAccountApplyService.save(officialAccountApply);
        }
        return new ResultData();
    }

    /**
     * 我的申请
     */
    @GetMapping("/v1.0/official/myApply")
    public ResultData officialApply(@RequestParam(value = "uId") String uId) {
        if (ObjUtil.equals(StrUtil.toStringOrNull(SecurityUtils.getUserId()), uId)) {
            QueryWrapper<OfficialAccountApply> ew = new QueryWrapper<>();
            ew.eq("create_by", uId);
            List<OfficialAccountApply> officialAccountApplies = iOfficialAccountApplyService.list(ew);
            if (CollUtil.isNotEmpty(officialAccountApplies)) {
                return new ResultData(officialAccountApplies.get(0));
            }
        }
        return new ResultData();
    }

    /**
     * 返回全部订阅号信息，带订阅号分类；订阅号是否关注信息，入参为本人id信息,其中cUid用于筛选，customerId 用于匹配返回是否订阅。入无需筛选则不传cid
     * 后续需要验证token和id
     */
    @Anonymous
    @GetMapping("/v1.0/official/list")
    public ResultData officialList(@RequestParam(value = "pageNum",defaultValue = "1") int pageNum,
                                   @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                   @RequestParam(value = "cId", required = false) Long cId,
                                   @RequestParam(value = "recommended", required = false) Integer recommended,
                                   @RequestParam(value = "categoryId", required = false) Integer categoryId) {
        Long customerId = SecurityUtils.getUserId();
        Map<String, Object> params = new HashMap<>();
        if (ObjUtil.isNotNull(cId)) params.put("isMyFollow", true);
        params.put("recommended", recommended);
        params.put("categoryId", categoryId);
        params.put("customerId", customerId);

        if (null != customerId && blackFlag) {
            params.put("notCreateIds", restSiteAppUtil.getBlackList(customerId));
        }

        ResultData resultData = new ResultData();
        IPage<OfficialAccountDTO> officialAccountDTOPage = iOfficialFollowService.queryOfficialAccounts(Page.of(pageNum,pageSize),params);
        resultData.addContentData("objs", officialAccountDTOPage.getRecords());
        resultData.addContentData("totalCount", officialAccountDTOPage.getTotal());
        resultData.addContentData("totalPages", officialAccountDTOPage.getTotal());
        return resultData;
    }

    /**
     * 获取订阅号详情
     */
    @Anonymous
    @GetMapping("/v1.0/official/detail")
    public ResultData officialDetail(@RequestParam(value = "officialId") Long officialId, @RequestParam(value = "customerId", required = false) String customerId) {
        List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(Arrays.asList(officialId), customerId);
        if (CollUtil.isEmpty(officialAccountList)) {
            return new ResultData();
        }
        if (officialAccountList.get(0).getEnabled() == 0 && officialAccountList.get(0).getCreateBy().equals(customerId)) {//获取自己的订阅号，且订阅号信息是禁用状态情况下
            return new ResultData(2056, "该账号已被停用，请联系客服处理");
        }
        return new ResultData(officialAccountList.get(0));
    }

    /**
     * 订阅订阅号接口
     */
    @PostMapping("/v1.0/official/follow")
    public ResultData followOfficial(@RequestBody OfficialFollow officialFollow) {
        try {
            officialFollow.setcUserId(SecurityUtils.getUserId());
            // 需要增加计算逻辑，统计粉丝数，后续优化redis
            if (iOfficialFollowService.save(officialFollow)) {
                iOfficialFollowService.updateFollowNum(officialFollow.getOfficialAccountId(), 1);
                officialOperLogService.saveLog(String.valueOf(officialFollow.getcUserId()), null, officialFollow.getOfficialAccountId(), 1);

                OfficialAccount officialAccount = new OfficialAccount();
                officialAccount.setId(officialFollow.getOfficialAccountId());
                applicationEventPublisher.publishEvent(new OfAccountEvent(this, officialAccount,
                        EventAction.builder().operator(SecurityUtils.getLoginUser()).type(BusinessType.FOLLOW).build()));
                return new ResultData();
            } else {
                return new ResultData(-1, "关注失败");
            }
        } catch (DuplicateKeyException duplicateKeyException) {
            return new ResultData(-1, "请不要重复关注");
        }
    }

    /**
     * 订阅订阅号接口
     */
    @GetMapping("/v1.0/official/follower/page")
    public ResultData officialFollowers(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize, @RequestParam("officialId") String officialId) {
        ResultData resultData = new ResultData();
        QueryWrapper<OfficialFollow> ew = new QueryWrapper<>();
        ew.eq("official_account_id", officialId);
        IPage<OfficialFollow> pageDemo = new Page(pageNum, pageSize);
        IPage<OfficialFollow> page = iOfficialFollowService.page(pageDemo, ew);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<GdmmUserInfo> gdmmUserInfoByKeys = gdmmUserInfoClient.getGdmmUserInfoByKeys(StrUtil.toStringOrNull(SecurityUtils.getUserId()), page.getRecords().stream().map(x -> String.valueOf(x.getcUserId())).collect(Collectors.toSet()));
            resultData.addContentData("objs", gdmmUserInfoByKeys);
        }
        resultData.addContentData("pageNo", page.getCurrent());
        resultData.addContentData("pageSize", page.getSize());
        resultData.addContentData("totalCount", page.getTotal());
        resultData.addContentData("totalPages", page.getPages());
        return resultData;

    }

    /**
     * 取消订阅号订阅接口
     */
    @PostMapping("/v1.0/official/cancel")
    public ResultData cancelOfficial(@RequestBody OfficialFollow officialFollow) {
        officialFollow.setcUserId(SecurityUtils.getUserId());
        Map<String, Object> deleteMap = new HashMap<>();
        deleteMap.put("official_account_id", officialFollow.getOfficialAccountId());
        deleteMap.put("c_user_id", officialFollow.getcUserId());
        if (iOfficialFollowService.removeByMap(deleteMap)) {
            iOfficialFollowService.updateFollowNum(officialFollow.getOfficialAccountId(), -1);
            officialOperLogService.saveLog(String.valueOf(officialFollow.getcUserId()), null, officialFollow.getOfficialAccountId(), 1);
            return new ResultData();
        } else {
            return new ResultData(-1, "取消失败");
        }
    }

    /**
     * 我的收藏列表
     */
    @GetMapping("/v1.0/official/myCollects")
    public ResultData myCollectNewsList(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize, @RequestParam(value = "cUId", required = false) String cUId) {
        cUId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        QueryWrapper<OfficialArticleCollect> ewCollect = new QueryWrapper<>();
        ewCollect.eq("c_user_id", cUId);
        ewCollect.orderByDesc("create_time");
        IPage<OfficialArticleCollect> pages = iOfficialArticleCollectService.page(new Page(pageNum, pageSize), ewCollect);
        List<Article> articles = new ArrayList<>();
        if (!pages.getRecords().isEmpty()) {
            QueryWrapper<Article> ew = new QueryWrapper<>();
            List<Long> articleIds = pages.getRecords().stream().map(OfficialArticleCollect::getArticleId).collect(Collectors.toList());
            ew.in("id", articleIds);
            ew.eq("status", 0);
            articles = iArticleService.list(ew);
            if (CollUtil.isNotEmpty(articles)) {
                //补充订阅号信息
                QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
                ewLikeQuery.eq("c_user_id", cUId);
                ewLikeQuery.in("article_id", articleIds);
                List<OfficialArticleLike> officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);

                List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(articles.stream().map(Article::getOfficialAccountId).collect(Collectors.toList()), cUId);
                List<ArticleCollectionRef> collectInfoByArticles = iArticleCollectionRefService.getCollectInfoByArticles(articles);
                for (Article x : articles) {
                    x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                    x.likeNumAddInit();
                    //设定集合信息
                    if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())) {
                        collectInfoByArticles.stream().filter(y -> y.getSubArticleId().equals(x.getId())).findFirst().ifPresent(y -> {
                            x.setCollectionId(y.getArticleId());
                            x.setCollectionTitle(y.getCollectionTitle());
                        });
                    } else if (ArticleConstant.ARTICLE_TYPE_COLLECT.equals(x.getType())) {
                        x.setViewed(iArticleCollectionRefService.getCollectViewedByArticleId(x.getId()));
                    }

                    x.setContent(null);
                    Optional<OfficialAccountDTO> any = officialAccountList.stream().filter(m -> Objects.equals(m.getId(), x.getOfficialAccountId())).findAny();
                    if (any.isPresent()) {
                        x.setSource(any.get().getName());
                        x.setSourceImgUrl(any.get().getLogoImage());
                        x.setOfficialCateName(any.get().getCategoryName());
                        x.setOfficialCreateBy(any.get().getCreateBy());
                        x.setIsOfficialFollow(any.get().getFollowed());
                    }
                    x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                    x.setIsCollect(1);//我的收藏，固定设置为已关注
                    Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                    if (anyLike.isPresent()) {
                        x.setIsLike(1);
                    }

                }
            }
        }

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", articles);
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }

    /**
     * 订阅号内部列表 ，分类信息返回订阅号内部列表数据，根据订阅号内部排序规则
     */
    @GetMapping("/v1.0/official/myArticles")
    public ResultData myOfficialNewsList(@RequestParam(value = "pageNum") int pageNum,
                                         @RequestParam(value = "pageSize") int pageSize,
                                         @RequestParam(value = "officialId", required = false) Long officialId,
                                         @RequestParam(value = "cUId") String cUId,
                                         @RequestParam(value = "categoryId", required = false) Integer categoryId) {

        if (ObjUtil.isNull(officialId)) {
            ResultData resultData = new ResultData();
            resultData.addContentData("objs", List.of());
            resultData.addContentData("pageNo", pageNum);
            resultData.addContentData("pageSize", pageSize);
            resultData.addContentData("totalCount", 0);
            resultData.addContentData("totalPages", 0);
            return resultData;
        }

        cUId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(Arrays.asList(officialId), null);
        if (CollUtil.isEmpty(officialAccountList)) {
            return new ResultData(-1, "不存在订阅号");
        }
        OfficialAccountDTO officialAccount = officialAccountList.get(0);
        QueryWrapper<Article> ew = new QueryWrapper<>();
        ew.eq(null != officialId, "official_account_id", officialId);
        ew.eq(null != cUId, "create_by", cUId);
        ew.eq(null != categoryId, "article_cat_id", categoryId);
        ew.in("status", Arrays.asList(0, 3, 4));
        ew.orderByDesc("is_top");
        ew.orderByDesc("create_time");

        IPage<Article> page = new Page(pageNum, pageSize);
        IPage<Article> pages = iArticleService.page(page, ew);

        if (CollUtil.isNotEmpty(pages.getRecords())) {
            List<Long> articleIds = pages.getRecords().stream().map(Article::getId).collect(Collectors.toList());
            QueryWrapper<OfficialArticleCollect> ewCollectQuery = new QueryWrapper<>();
            //审核完成状态
            ewCollectQuery.eq("c_user_id", cUId);
            ewCollectQuery.in("article_id", articleIds);
            List<OfficialArticleCollect> officialArticleCollects = iOfficialArticleCollectService.list(ewCollectQuery);
            QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
            //审核完成状态
            ewLikeQuery.eq("c_user_id", cUId);
            ewLikeQuery.in("article_id", articleIds);
            List<OfficialArticleLike> officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);
            List<ArticleCollectionRef> collectInfoByArticles = iArticleCollectionRefService.getCollectInfoByArticles(pages.getRecords());

            JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));

            for (Article x : pages.getRecords()) {
                x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                x.likeNumAddInit();
                if (StrUtil.isNotBlank(OfficialConfig.SHARE_IMG_URL)) {
                    x.setShareImgUrl(OfficialConfig.SHARE_IMG_URL);
                } else if (StrUtil.isNotBlank(x.getImgUrl())) {
                    x.setShareImgUrl(x.getImgUrl().contains(",") ? x.getImgUrl().split(",")[0] : x.getImgUrl());
                }
                //设定集合信息
                if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())) {
                    collectInfoByArticles.stream().filter(y -> y.getSubArticleId().equals(x.getId())).findFirst().ifPresent(y -> {
                        x.setCollectionId(y.getArticleId());
                        x.setCollectionTitle(y.getCollectionTitle());
                    });
                } else if (ArticleConstant.ARTICLE_TYPE_COLLECT.equals(x.getType())) {
                    x.setViewed(iArticleCollectionRefService.getCollectViewedByArticleId(x.getId()));
                }

                //评论总设置和单条设置有一个为关闭评论，整体关闭评论
                if (!"1".equals(config.getString("isComment"))) {
                    x.setIsComment(0L);
                }
                x.setContent(null);

                x.setSource(officialAccount.getName());
                x.setSourceImgUrl(officialAccount.getLogoImage());
                x.setOfficialCateName(officialAccount.getCategoryName());
                x.setOfficialCreateBy(officialAccount.getCreateBy());
                x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                Optional<OfficialArticleCollect> anyCollect = officialArticleCollects.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                if (anyCollect.isPresent()) {
                    x.setIsCollect(1);
                }
                if (anyLike.isPresent()) {
                    x.setIsLike(1);
                }

            }
        }
        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }


    /**
     * 新闻列表-未被分配到合集列表
     */
    @GetMapping("/v1.0/official/notSet/articles")
    public ResultData notSetArticles(@RequestParam(value = "pageNum") int pageNum,
                                     @RequestParam(value = "pageSize") int pageSize,
                                     @RequestParam(value = "categoryId", required = false, defaultValue = "100") Integer categoryId,
                                     @RequestParam(value = "collectionId", required = false) Long collectionId) {
        String cUId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        IPage<Article> pages = iArticleService.selectNotSetPage(pageNum, pageSize, categoryId, cUId, collectionId);

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }

    /**
     * 热门视频
     */
    @Anonymous
    @GetMapping("/v1.0/official/next/articles")
    public ResultData nextArticles(@RequestParam(value = "pageNum") int pageNum,
                                   @RequestParam(value = "pageSize") int pageSize,
                                   @RequestParam(value = "articleId") Long articleId,
                                   @RequestParam(value = "categoryId", required = false, defaultValue = "100") String categoryId,
                                   @RequestParam(value = "customerId", required = false) Long customerId) {

        //获取默认订阅号；
        QueryWrapper<OfficialAccount> officialAccountEntityWrapper = new QueryWrapper<>();
        officialAccountEntityWrapper.eq("defaulted", 1);
        officialAccountEntityWrapper.eq("deleted", 0);
        List<OfficialAccount> officialAccounts = iOfficialAccountService.list(officialAccountEntityWrapper);
        List<Long> collect = officialAccounts.stream().map(OfficialAccount::getId).collect(Collectors.toList());
        //获取订阅的订阅号；
        if (null != customerId) {
            QueryWrapper<OfficialFollow> followEw = new QueryWrapper<>();
            followEw.eq("c_user_id", customerId);
            List<OfficialFollow> officialFollows = iOfficialFollowService.list(followEw);
            //获取要查询的订阅号
            collect.addAll(officialFollows.stream().map(OfficialFollow::getOfficialAccountId).collect(Collectors.toList()));
        }

        Article article = iArticleService.getById(articleId);
        QueryWrapper<Article> ew = new QueryWrapper<>();
        ew.eq("status", ArticleConstant.ARTICLE_STATUS_APPROVE);
        //开启呈现
        ew.eq("enabled", 0);
        ew.in(CollUtil.isNotEmpty(collect), "official_account_id", collect);
        ew.eq("article_cat_id", categoryId);
        ew.lt("create_time", article.getCreateTime());
        //创建时间倒叙
        ew.orderByDesc("create_time");

        IPage<Article> page = new Page(pageNum, pageSize);
        IPage<Article> pages = iArticleService.page(page, ew);


        //补充订阅号信息
        if (!pages.getRecords().isEmpty()) {
            List<Long> articleIds = pages.getRecords().stream().map(Article::getId).collect(Collectors.toList());
            QueryWrapper<OfficialArticleCollect> ewCollectQuery = new QueryWrapper<>();
            //审核完成状态
            ewCollectQuery.eq("c_user_id", customerId);
            ewCollectQuery.in("article_id", articleIds);
            List<OfficialArticleCollect> officialArticleCollects = iOfficialArticleCollectService.list(ewCollectQuery);
            QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
            //审核完成状态
            ewLikeQuery.eq("c_user_id", customerId);
            ewLikeQuery.in("article_id", articleIds);
            List<OfficialArticleLike> officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);

            List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(collect, null);
            List<ArticleCollectionRef> collectInfoByArticles = iArticleCollectionRefService.getCollectInfoByArticles(pages.getRecords());

            JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));

            for (Article x : pages.getRecords()) {
                x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                x.likeNumAddInit();
                if (StrUtil.isNotBlank(OfficialConfig.SHARE_IMG_URL)) {
                    x.setShareImgUrl(OfficialConfig.SHARE_IMG_URL);
                } else if (StrUtil.isNotBlank(x.getImgUrl())) {
                    x.setShareImgUrl(x.getImgUrl().contains(",") ? x.getImgUrl().split(",")[0] : x.getImgUrl());
                }
                //设定集合信息
                if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())) {
                    collectInfoByArticles.stream().filter(y -> y.getSubArticleId().equals(x.getId())).findFirst().ifPresent(y -> {
                        x.setCollectionId(y.getArticleId());
                        x.setCollectionTitle(y.getCollectionTitle());
                    });
                }

                //评论总设置和单条设置有一个为关闭评论，整体关闭评论
                if (!"1".equals(config.getString("isComment"))) {
                    x.setIsComment(0L);
                }

                x.setContent(null);
                Optional<OfficialAccountDTO> any = officialAccountList.stream().filter(m -> Objects.equals(m.getId(), x.getOfficialAccountId())).findAny();
                if (any.isPresent()) {
                    x.setSource(any.get().getName());
                    x.setSourceImgUrl(any.get().getLogoImage());
                    x.setOfficialCateName(any.get().getCategoryName());
                    x.setOfficialCreateBy(any.get().getCreateBy());
                }
                x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                Optional<OfficialArticleCollect> anyCollect = officialArticleCollects.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                if (anyCollect.isPresent()) {
                    x.setIsCollect(1);
                }
                if (anyLike.isPresent()) {
                    x.setIsLike(1);
                }

            }
        }

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }

    /**
     * 个人草稿箱
     */
    @GetMapping("/v1.0/official/myDraftArticles")
    public ResultData myDraftArticles(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize", defaultValue = "20") int pageSize, @RequestParam(value = "cUId") String cUId) {
        cUId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        QueryWrapper<Article> ew = new QueryWrapper<>();
        ew.eq(null != cUId, "create_by", cUId);
        ew.eq("status", 2);
        ew.orderByDesc("create_time");

        IPage<Article> page = new Page(pageNum, pageSize);
        IPage<Article> pages = iArticleService.page(page, ew);
        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }

    /**
     * 个人草稿箱
     */
    @GetMapping("/v1.0/official/myDraftCount")
    public ResultData myDraftArticlesCount(@RequestParam(value = "cUId") String cUId) {
        cUId = StrUtil.toStringOrNull(SecurityUtils.getUserId());
        QueryWrapper<Article> ew = new QueryWrapper<>();
        ew.eq("create_by", cUId);
        ew.eq("status", 2);
        long i = iArticleService.count(ew);
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("num", i);
        return new ResultData(resultMap);

    }


    /**
     * 订阅号内部列表 ，分类信息返回订阅号内部列表数据，根据订阅号内部排序规则
     */
    @Anonymous
    @GetMapping("/v1.0/official/articles")
    public ResultData officialNewsList(@RequestParam(value = "pageNum") int pageNum,
                                       @RequestParam(value = "pageSize") int pageSize,
                                       @RequestParam(value = "officialId") Long officialId,
                                       @RequestParam(value = "customerId", required = false) String customerId,
                                       @RequestParam(value = "recommended", required = false) Integer recommended,
                                       @RequestParam(value = "categoryId", required = false) Integer categoryId) {

        List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(Arrays.asList(officialId), null);
        if (CollUtil.isEmpty(officialAccountList)) {
            return new ResultData(-1, "不存在订阅号");
        }
        OfficialAccountDTO officialAccount = officialAccountList.get(0);
        QueryWrapper<Article> ew = new QueryWrapper<>();
        //审核完成状态
        ew.eq("status", 0);
        //开启呈现
        ew.eq("enabled", 0);
        ew.eq(null != officialId, "official_account_id", officialId);
        ew.eq(null != recommended, "recommended", recommended);
        ew.eq(null != categoryId, "article_cat_id", categoryId);
        ew.orderByDesc("is_top");
        ew.orderByDesc("sort");
        ew.orderByDesc("create_time");

        IPage<Article> page = new Page(pageNum, pageSize);
        IPage<Article> pages = iArticleService.page(page, ew);

        //补充订阅号信息
        if (!pages.getRecords().isEmpty()) {
            List<Long> articleIds = pages.getRecords().stream().map(Article::getId).collect(Collectors.toList());
            QueryWrapper<OfficialArticleCollect> ewCollectQuery = new QueryWrapper<>();
            //审核完成状态
            ewCollectQuery.eq("c_user_id", customerId);
            ewCollectQuery.in("article_id", articleIds);
            List<OfficialArticleCollect> officialArticleCollects = iOfficialArticleCollectService.list(ewCollectQuery);
            QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
            //审核完成状态
            ewLikeQuery.eq("c_user_id", customerId);
            ewLikeQuery.in("article_id", articleIds);
            List<OfficialArticleLike> officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);

            List<ArticleCollectionRef> collectInfoByArticles = iArticleCollectionRefService.getCollectInfoByArticles(pages.getRecords());

            JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));

            pages.getRecords().forEach(x -> {
                x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                x.likeNumAddInit();
                if (StrUtil.isNotBlank(OfficialConfig.SHARE_IMG_URL)) {
                    x.setShareImgUrl(OfficialConfig.SHARE_IMG_URL);
                } else if (StrUtil.isNotBlank(x.getImgUrl())) {
                    x.setShareImgUrl(x.getImgUrl().contains(",") ? x.getImgUrl().split(",")[0] : x.getImgUrl());
                }
                //设定集合信息
                if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())) {
                    collectInfoByArticles.stream().filter(y -> y.getSubArticleId().equals(x.getId())).findFirst().ifPresent(y -> {
                        x.setCollectionId(y.getArticleId());
                        x.setCollectionTitle(y.getCollectionTitle());
                    });
                } else if (ArticleConstant.ARTICLE_TYPE_COLLECT.equals(x.getType())) {
                    x.setViewed(iArticleCollectionRefService.getCollectViewedByArticleId(x.getId()));
                }

                //设定评论信息
                if (!"1".equals(config.getString("isComment"))) {
                    x.setIsComment(0L);
                }

                x.setContent(null);
                x.setSource(officialAccount.getName());
                x.setSourceImgUrl(officialAccount.getLogoImage());
                x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                Optional<OfficialArticleCollect> anyCollect = officialArticleCollects.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                if (anyCollect.isPresent()) {
                    x.setIsCollect(1);
                }
                if (anyLike.isPresent()) {
                    x.setIsLike(1);
                }

            });
        }

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }


    /**
     * 根据父新闻ID查询子新闻ID
     */
    @Anonymous
    @GetMapping("/v1.0/official/subArticles")
    public ResultData subArticles(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                  @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                  @RequestParam(value = "articleId") Long articleId) {

        Article article = iArticleService.getById(articleId);
        if (null == article) {
            return new ResultData(-1, "不存在新闻");
        }
        OfficialAccount officialAccount = iOfficialAccountService.getById(article.getOfficialAccountId());

        IPage<Article> pages = iArticleService.getSubArticles(pageNum, pageSize, articleId);

        //补充订阅号信息
        if (!pages.getRecords().isEmpty()) {
            pages.getRecords().forEach(x -> {
                x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                x.likeNumAddInit();
                x.setContent(null);
                x.setSource(officialAccount.getName());
                x.setSourceImgUrl(officialAccount.getLogoImage());
                x.setCollectionId(article.getId());
                x.setCollectionTitle(article.getTitle());
                x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
            });
        }

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }

    @Anonymous
    @GetMapping("/v1.0/official/applyTemplate")
    public ResultData applyTemplate() {
        ResultData resultData = new ResultData();
        resultData.setContent(sysConfigService.selectConfigByKey("gdmm.official.template"));
        return resultData;
    }

}
