package com.zainanjing.convenience.handlers.endpointc;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonView;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.Views;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.service.ISysConfigService;
import com.ruoyi.common.core.service.ISysDictTypeService;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.dfa.WordTree;
import com.ruoyi.common.utils.html.EscapeUtil;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.zainanjing.common.constant.SensitiveException;
import com.zainanjing.convenience.support.APISystemConfig;
import com.zainanjing.convenience.support.web.ResultData;
import com.zainanjing.convenience.utils.ArraysToTreeUtil;
import com.zainanjing.convenience.utils.RestSiteAppUtil;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import com.zainanjing.official.constant.ArticleConstant;
import com.zainanjing.official.constant.OfficialConfig;
import com.zainanjing.official.domain.*;
import com.zainanjing.official.dto.OfficialAccountDTO;
import com.zainanjing.official.event.OfCommentEvent;
import com.zainanjing.official.search.EArticleService;
import com.zainanjing.official.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController("2cNewsController")
@RequestMapping("/api-2c")
public class NewsController {

    protected static final Logger logger = LoggerFactory.getLogger(NewsController.class);

    @Value("${system.official.black-flag:false}")
    private boolean blackFlag;

    @Autowired
    private IOfficialAccountService iOfficialAccountService;

    @Autowired
    private IOfficialFollowService iOfficialFollowService;

    @Autowired
    private IArticleService iArticleService;

    @Autowired
    private IArticleCatService iArticleCatService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IArticleCommentService iArticleCommentService;

    @Autowired
    private IOfficialArticleCollectService iOfficialArticleCollectService;

    @Autowired
    private IOfficialArticleLikeService iOfficialArticleLikeService;

    @Autowired
    private EArticleService eArticleService;

    @Autowired
    private WangyiCheckUtil wangyiCheckUtil;

    @Resource
    private IOfficialOperLogService officialOperLogService;

    @Resource
    private IArticleCollectionRefService iArticleCollectionRefService;

    @Resource
    private RestSiteAppUtil restSiteAppUtil;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private ISysDictTypeService dictTypeService;


    /**
     * 获取分类资讯接口
     */
    @Anonymous
    @GetMapping("/v1.0/article/categories")
    public ResultData categories(@RequestParam(required = false) String categoryType) {
        if (StrUtil.isEmpty(categoryType)) {
            categoryType = "0";
        }
        QueryWrapper<ArticleCat> ew = new QueryWrapper<>();
        ew.eq("category_type", categoryType);
        ew.eq("deleted", 0);
        ew.eq("enabled", 0);
        ew.orderByDesc("sort");
        ResultData resultData = new ResultData();
        resultData.setContent(iArticleCatService.list(ew));
        return resultData;
    }

    /**
     * 获取新闻权限配置
     */
    @Anonymous
    @GetMapping("/v1.0/article/setting")
    public ResultData getSetting() {
        String s = sysConfigService.selectConfigByKey("gdmm.news.setting");
        return new ResultData(JSON.parse(s));
    }

    /**
     * 删除新闻
     *
     * @return ResultData
     */
    @Log(title = "订阅号资讯", businessType = BusinessType.DELETE)
    @PostMapping("/v1.0/article/delete")
    public ResultData deleteArticle(@RequestBody JSONObject jsonObject) {
        if (StrUtil.isNotEmpty(jsonObject.getString("createBy")) && StrUtil.isNotEmpty(jsonObject.getString("id"))) {
            if (ObjUtil.equals(StrUtil.toStringOrNull(SecurityUtils.getUserId()), jsonObject.getString("createBy"))) {
                QueryWrapper<Article> ew = new QueryWrapper<>();
                if (jsonObject.getString("id").contains(",")) {
                    ew.in("id", jsonObject.getString("id").split(","));
                } else {
                    ew.eq("id", jsonObject.getString("id"));
                }
                ew.eq("create_by", jsonObject.getString("createBy"));
                List<Article> articles = iArticleService.list(ew);
                if (CollUtil.isEmpty(articles)) {
                    return new ResultData(-1, "删除失败");
                }
                if (iArticleService.removeByIdsAndEvent(articles.stream().map(Article::getId).collect(Collectors.toList()))) {
                    return new ResultData();
                }
            }
        }
        return new ResultData(-1, "删除失败");
    }

    /**
     * 清空草稿
     *
     * @return ResultData
     */
    @Log(title = "订阅号资讯", businessType = BusinessType.CLEAN)
    @PostMapping("/v1.0/article/clearDraft")
    public ResultData clearDraftArticle(@RequestBody Article article) {
        if (StrUtil.isNotEmpty(article.getCreateBy()) && ObjUtil.equals(StrUtil.toStringOrNull(SecurityUtils.getUserId()), article.getCreateBy())) {
            QueryWrapper<Article> ew = new QueryWrapper<>();
            ew.eq("create_by", article.getCreateBy());
            ew.eq("status", 2);
            List<Article> articles = iArticleService.list(ew);
            if (CollUtil.isEmpty(articles)) {
                return new ResultData(-1, "清空失败");
            }
            if (iArticleService.removeBatchByIds(articles.stream().map(Article::getId).collect(Collectors.toList()))) {
                iArticleCollectionRefService.deleteByArticles(articles);
                return new ResultData();
            }
        }
        return new ResultData(-1, "清空失败");
    }

    /**
     * 更新是否可以评论
     *
     * @return ResultData
     */
    @PostMapping("/v1.0/article/setIsComment")
    public ResultData newArticleEnableComment(@RequestBody Article article) {
        JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));
        if (!"1".equals(config.getString("isComment"))) {
            return new ResultData(-1, "平台评论已关闭，禁止操作");
        }
        Article articleUpdate = new Article();
        articleUpdate.setIsComment(article.getIsComment());
        articleUpdate.setId(article.getId());
        //给默认数据
        return new ResultData(iArticleService.updateById(articleUpdate));
    }

    /**
     * 新增新闻
     *
     * @return ResultData
     */
    @Log(title = "订阅号资讯", businessType = BusinessType.UPDATE)
    @PostMapping("/v1.0/article/save")
    public ResultData addArticle(HttpServletRequest request, @RequestBody Article article) {
        ResultData resultData = new ResultData();

        //获取IP地址
        String ip = IpUtils.getIpAddr(request);
        article.setIp(ip);
        article.setRegion(AddressUtils.getRealAddressByIP(ip));

        //验证订阅号
        if (null == article.getOfficialAccountId()) {
            resultData.setMessage("请选择订阅号");
            resultData.setCode(-1);
        }
        if (Objects.equals(article.getCreateBy(), StrUtil.toStringOrNull(SecurityUtils.getUserId()))) {
            OfficialAccount officialAccount = iOfficialAccountService.getById(article.getOfficialAccountId());
            if (officialAccount.getEnabled().equals(0)) {//如果订阅号被停用
                return new ResultData(2056, "该账号已被停用，请联系客服处理");
            }
        } else {
            logger.error("用户信息不匹配，用户信息：{}，文章信息：{}", SecurityUtils.getLoginUser(), article.getTitle());
            return new ResultData(2056, "该账号已被停用，请联系客服处理");
        }

        article.setAuthor(Optional.of(SecurityUtils.getLoginUser())
                .map(LoginUser::getNickName)
                .orElse("匿名"));

        //验证数据
        if (APISystemConfig.wangyiCheck) {
            String checkStr = (StrUtil.isEmpty(article.getTitle()) ? "" : article.getTitle())
                    + (StrUtil.isEmpty(article.getContent()) ? "" : article.getContent());
            wangyiCheckUtil.checkText(3001, null, checkStr);
            wangyiCheckUtil.checkImages(3001, article.getImgUrl());
        }

        //给默认数据
        if (ObjUtil.isNotNull(article.getType())) {
            if (ArticleConstant.ARTICLE_TYPE_COLLECT.equals(article.getType())) {
                article.setArticleCatId(ArticleConstant.ARTICLE_CAT_COLLECT);
            } else if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(article.getType())) {
                article.setArticleCatId(ArticleConstant.ARTICLE_CAT_VIDEO);
            } else {
                article.setArticleCatId(ArticleConstant.ARTICLE_CAT_NORMAL);
            }
        } else {
            if (ArticleConstant.ARTICLE_CAT_COLLECT.equals(article.getArticleCatId())) {
                article.setType(ArticleConstant.ARTICLE_TYPE_COLLECT);
            } else if (ArticleConstant.ARTICLE_CAT_VIDEO.equals(article.getArticleCatId())) {
                article.setType(ArticleConstant.ARTICLE_TYPE_VIDEO);
            } else {
                article.setType(ArticleConstant.ARTICLE_TYPE_NORMAL);
            }
        }

        article.setEnabled(0);
        article.setCategoryType("1");
        if (StrUtil.isEmpty(article.getImgUrl())) {
            article.setDisplayMode(0);
        } else {
            article.setDisplayMode(2);
        }
        article.setSort(0L);
        if (!ArticleConstant.ARTICLE_STATUS_DRAFT.equals(article.getStatus())) {
            article.setStatus(ArticleConstant.ARTICLE_STATUS_PENDING);
        } else {
            resultData.setMessage("保存成功");
        }
        //根据是否需要审核设置文章状态
        JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));
        if (ArticleConstant.ARTICLE_STATUS_PENDING.equals(article.getStatus())) {
            if ("1".equals(config.getString("isAuditNews"))) {
                article.setStatus(ArticleConstant.ARTICLE_STATUS_PENDING);
                resultData.setMessage("发布成功，请等待平台审核");
            } else {
                article.setStatus(ArticleConstant.ARTICLE_STATUS_APPROVE);
                resultData.setMessage("发布成功");
            }
        }

        if (StrUtil.isNotEmpty(article.getContent())) {
            String str = EscapeUtil.clean(article.getContent()).replaceAll("&nbsp;", "");
            article.setRemark(str.substring(0, Math.min(str.length(), 1000)));
        } else {
            article.setRemark("");
        }
        article.setCreateTime(new Date());//覆盖客户端给的创建时间
        if (iArticleService.saveAndEvent(article)) {
            if (ArticleConstant.ARTICLE_TYPE_COLLECT.equals(article.getType())) {//合集处理
                List<ArticleCollectionRef> articleCollectionRefs = new ArrayList<>();
                if (StrUtil.isNotEmpty(article.getCollectionIds())) {
                    Integer num = 0;
                    for (String s : article.getCollectionIds().split(",")) {
                        num++;
                        ArticleCollectionRef articleCollectionRef = new ArticleCollectionRef();
                        articleCollectionRef.setArticleId(article.getId());
                        articleCollectionRef.setSubArticleId(Long.valueOf(s));
                        articleCollectionRef.setSort(num);
                        articleCollectionRefs.add(articleCollectionRef);
                    }
                    iArticleCollectionRefService.saveBatch(articleCollectionRefs);
                }
            }
        }

        return resultData;
    }

    /**
     * 修改新闻
     *
     * @return ResultData
     */
    @Log(title = "订阅号资讯", businessType = BusinessType.UPDATE)
    @PostMapping("/v1.0/article/update")
    public ResultData updateArticle(HttpServletRequest request, @RequestBody Article article) {
        //验证订阅号
        if (null == article.getOfficialAccountId()) {
            return new ResultData(-1, "请选择订阅号");
        }
        if (Objects.equals(article.getCreateBy(), StrUtil.toStringOrNull(SecurityUtils.getUserId()))) {
            OfficialAccount officialAccount = iOfficialAccountService.getById(article.getOfficialAccountId());
            if (officialAccount.getEnabled().equals(0)) {//如果订阅号被停用
                return new ResultData(2056, "该账号已被停用，请联系客服处理");
            }
        } else {
            logger.error("用户信息不匹配，用户信息：{}，文章信息：{}", SecurityUtils.getLoginUser(), article.getTitle());
            return new ResultData(2056, "该账号已被停用，请联系客服处理");
        }

        Article articleOld = iArticleService.getById(article.getId());
        if (null == articleOld) {
            return new ResultData(-1, "新闻不存在");
        }

        ResultData resultData = new ResultData();
        if (APISystemConfig.wangyiCheck) {
            String checkStr = (StrUtil.isEmpty(article.getTitle()) ? "" : article.getTitle())
                    + (StrUtil.isEmpty(article.getContent()) ? "" : article.getContent());
            wangyiCheckUtil.checkText(3001, null, checkStr);
            wangyiCheckUtil.checkImages(3001, article.getImgUrl());
        }

        //获取IP地址
        String ip = IpUtils.getIpAddr(request);
        article.setIp(ip);
        article.setRegion(AddressUtils.getRealAddressByIP(ip));

        if (!ArticleConstant.ARTICLE_STATUS_DRAFT.equals(article.getStatus())) {
            article.setStatus(ArticleConstant.ARTICLE_STATUS_PENDING);
        } else {
            resultData.setMessage("保存成功");
        }
        if (StrUtil.isNotEmpty(article.getContent())) {
            String str = EscapeUtil.clean(article.getContent()).replaceAll("&nbsp;", "");
            article.setRemark(str.substring(0, Math.min(str.length(), 1000)));
        } else {
            article.setRemark("");
        }

        article.setType(articleOld.getType());

        //根据配置设置状态
        JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));
        if (ArticleConstant.ARTICLE_STATUS_PENDING.equals(article.getStatus())) {
            if ("1".equals(config.getString("isAuditNews"))) {
                article.setStatus(ArticleConstant.ARTICLE_STATUS_PENDING);
                resultData.setMessage("发布成功，请等待平台审核");
            } else {
                article.setStatus(ArticleConstant.ARTICLE_STATUS_APPROVE);
                resultData.setMessage("发布成功");
            }
        }

        if (null != article.getImgUrl()) {
            if (StrUtil.isEmpty(article.getImgUrl())) {
                article.setDisplayMode(0);
            } else {
                article.setDisplayMode(2);
            }
        }
        article.setUpdateTime(new Date());
        article.setCreateTime(null);//创建时间不更新
        //根据更新状态更新和删除数据
        if (iArticleService.updateByIdAndEvent(article)) {
            if (ArticleConstant.ARTICLE_TYPE_COLLECT.equals(article.getType())) {//合集处理
                List<ArticleCollectionRef> articleCollectionRefs = new ArrayList<>();
                if (StrUtil.isNotEmpty(article.getCollectionIds())) {
                    Integer num = 0;
                    for (String s : article.getCollectionIds().split(",")) {
                        num++;
                        ArticleCollectionRef articleCollectionRef = new ArticleCollectionRef();
                        articleCollectionRef.setArticleId(article.getId());
                        articleCollectionRef.setSubArticleId(Long.valueOf(s));
                        articleCollectionRef.setSort(num);
                        articleCollectionRefs.add(articleCollectionRef);
                    }
                    iArticleCollectionRefService.remove(new QueryWrapper<ArticleCollectionRef>().eq("article_id", article.getId()));
                    iArticleCollectionRefService.saveBatch(articleCollectionRefs);
                }
            }
        }
        resultData.setContent(true);
        return resultData;

    }

    /**
     * 置顶新闻 （增加置顶字段、置顶时间）
     *
     * @return ResultData
     */
    @PostMapping("/v1.0/article/top")
    public ResultData topArticle(@RequestBody OfficialArticleCollect article) {
        return new ResultData(iArticleService.lambdaUpdate().set(Article::getIsTop, 1)
                .eq(Article::getId, article.getArticleId())
                .eq(Article::getCreateBy, StrUtil.toStringOrNull(SecurityUtils.getUserId()))
                .update()
        );
    }

    @PostMapping("/v1.0/article/unTop")
    public ResultData unTopArticle(@RequestBody OfficialArticleCollect article) {
        return new ResultData(iArticleService.lambdaUpdate().set(Article::getIsTop, 0)
                .eq(Article::getId, article.getArticleId())
                .eq(Article::getCreateBy, StrUtil.toStringOrNull(SecurityUtils.getUserId()))
                .update()
        );
    }

    /**
     * 获取文章最新数量状态
     *
     * @param cUId
     * @param articleId
     * @return
     */
    @Anonymous
    @GetMapping("/v1.0/article/getLastState")
    public ResultData getLastNum(@RequestParam(value = "cUId", required = false) String cUId, @RequestParam(value = "articleId") Long articleId) {
        QueryWrapper<Article> ew = new QueryWrapper<>();
        ew.select("like_num,collect_num,official_account_id,is_comment,comment_num,viewed,init_view,init_like_num");
        ew.eq("id", articleId);
        Article article = iArticleService.getOne(ew);
        article.likeNumAddInit();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("likeNum", article.getLikeNum());
        resultMap.put("collectionNum", article.getCollectNum());
        resultMap.put("commentNum", article.getCommentNum());
        resultMap.put("viewed", iArticleService.sumView(article.getViewed(), article.getInitView()));
        resultMap.put("isLike", 0);
        resultMap.put("isCollect", 0);
        resultMap.put("isOfficialFollow", 0);
        resultMap.put("isComment", 0);
        if (StrUtil.isNotEmpty(cUId)) {
            //是否点赞
            QueryWrapper<OfficialArticleLike> ewLike = new QueryWrapper<>();
            ewLike.eq("article_id", articleId);
            ewLike.eq("c_user_id", cUId);
            OfficialArticleLike officialArticleLike = iOfficialArticleLikeService.getOne(ewLike);
            if (null != officialArticleLike) {
                resultMap.put("isLike", 1);
            }

            //是否收藏
            QueryWrapper<OfficialArticleCollect> ewCollect = new QueryWrapper<>();
            ewCollect.eq("article_id", articleId);
            ewCollect.eq("c_user_id", cUId);
            OfficialArticleCollect officialArticleCollect = iOfficialArticleCollectService.getOne(ewCollect);
            if (null != officialArticleCollect) {
                resultMap.put("isCollect", 1);
            }

            //是否关注对应的订阅号
            QueryWrapper<OfficialFollow> ewFollow = new QueryWrapper<>();
            ewFollow.eq("official_account_id", article.getOfficialAccountId());
            ewFollow.eq("c_user_id", cUId);
            OfficialFollow officialFollow = iOfficialFollowService.getOne(ewFollow);
            if (null != officialFollow) {
                resultMap.put("isOfficialFollow", 1);
            }

            //是否能够评论
            JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));
            //评论总设置和单条设置有一个为关闭评论，整体关闭评论
            if ("1".equals(config.getString("isComment")) && Long.valueOf(1L).equals(article.getIsComment())) {
                resultMap.put("isComment", 1L);
            }
        }

        return new ResultData(resultMap);
    }

    /**
     * 新闻列表接口（搜索）
     */
    @Anonymous
    @GetMapping("/v1.0/article/search")
    public ResultData search10(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize, @RequestParam(value = "keywords", required = false) String keywords, @RequestParam(value = "customerId", required = false) Long customerId) {
        sensitiveInner(keywords);

        if (APISystemConfig.wangyiCheck && StrUtil.isNotBlank(keywords)) {
            wangyiCheckUtil.checkText(3001, "searchKey", keywords);
        }
        IPage<Article> list = eArticleService.list(pageNum, pageSize, keywords, customerId);
        ResultData resultData = new ResultData();
        resultData.addContentData("objs", list.getRecords());
        resultData.addContentData("pageNo", list.getCurrent());
        resultData.addContentData("pageSize", list.getSize());
        resultData.addContentData("totalCount", list.getTotal());
        resultData.addContentData("totalPages", list.getPages());
        return resultData;
    }

    private void sensitiveInner(String keywords) {
        if (StrUtil.isNotBlank(keywords)) {
            List<SysDictData> officialSensitives = dictTypeService.selectDictDataByType("official_sensitive");
            if (CollUtil.isNotEmpty(officialSensitives)) {
                WordTree tree = new WordTree();
                officialSensitives.stream().map(SysDictData::getDictValue).forEach(tree::addWord);
                List<String> matchAllTitle = tree.matchAll(keywords, -1, false, false);
                if (CollUtil.isNotEmpty(matchAllTitle)) {
                    throw new SensitiveException(198, "内容中含有违规信息，提交失败。", matchAllTitle.toString());
                }
            }
        }
    }

    /**
     * 新闻列表接口（搜索）
     */
    @Anonymous
    @GetMapping("/v1.1/article/search")
    public ResultData search11(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "3") int pageSize,
                               @RequestParam(value = "keywords", required = false) String keywords,
                               @RequestParam(value = "customerId", required = false) Long customerId,
                               @RequestParam(value = "mode", required = false) String mode) {
        sensitiveInner(keywords);

        if (APISystemConfig.wangyiCheck && StrUtil.isNotBlank(keywords)) {
            wangyiCheckUtil.checkText(3001, "searchKey", keywords);
        }

        if (StrUtil.isBlank(mode)) {
            Page<Article> hotPage = search(pageNum, pageSize, keywords, customerId, "hot");
            Page<Article> hotVideo = search(pageNum, pageSize, keywords, customerId, "video");
            Page<Article> hotSquare = search(pageNum, pageSize, keywords, customerId, "square");

            ResultData resultData = new ResultData();
            resultData.addContentData("hot", hotPage.getRecords());
            resultData.addContentData("video", hotVideo.getRecords());
            resultData.addContentData("square", hotSquare.getRecords());
            return resultData;
        }

        Page<Article> page = search(pageNum, pageSize, keywords, customerId, mode);

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", page.getRecords());
        resultData.addContentData("pageNo", page.getCurrent());
        resultData.addContentData("pageSize", page.getSize());
        resultData.addContentData("totalCount", page.getTotal());
        resultData.addContentData("totalPages", page.getPages());
        return resultData;
    }

    private Page<Article> search(int pageNum, int pageSize, String keywords, Long customerId, String mode) {

        List<Long> blackUids = List.of();
        if (null != customerId && blackFlag) {
            blackUids = restSiteAppUtil.getBlackList(customerId);
        }
        LambdaQueryWrapper<Article> ewArticleQuery = new LambdaQueryWrapper<>();
        ewArticleQuery.orderByDesc(Article::getCreateTime);
        ewArticleQuery.eq(Article::getStatus, ArticleConstant.ARTICLE_STATUS_APPROVE);
        ewArticleQuery.notIn(CollUtil.isNotEmpty(blackUids), Article::getCreateBy, blackUids);
        ewArticleQuery.and(StrUtil.isNotBlank(keywords), m -> m.like(Article::getTitle, keywords).or().like(Article::getRemark, keywords));
        //获取默认订阅号；
        if (ObjUtil.equals("hot", mode)) {//（默认1：热门推荐，2:普通模式,3 视频模式；4:广场模式）
            //获取订阅的订阅号；
            List<Long> officialFollows;
            if (null != customerId) {
                officialFollows = iOfficialFollowService.lambdaQuery()
                        .select(OfficialFollow::getOfficialAccountId)
                        .eq(OfficialFollow::getcUserId, customerId).list().stream().map(OfficialFollow::getOfficialAccountId).collect(Collectors.toList());
            } else {
                officialFollows = null;
            }
            List<Long> collect = iOfficialAccountService.lambdaQuery()
                    .notIn(CollUtil.isNotEmpty(blackUids), OfficialAccount::getCreateBy, blackUids)
                    .eq(OfficialAccount::getDeleted, 0)
                    .and(m -> m.eq(OfficialAccount::getDefaulted, 1)
                            .or(CollUtil.isNotEmpty(officialFollows), n -> n.in(OfficialAccount::getId, officialFollows)))
                    .list().stream().map(OfficialAccount::getId).collect(Collectors.toList());

            if (CollUtil.isEmpty(collect)) {
                return new Page<>(pageNum, pageSize);
            }
            ewArticleQuery.in(CollUtil.isNotEmpty(collect), Article::getOfficialAccountId, collect);
            //首页数据不呈现集合数据
            ewArticleQuery.in(Article::getType, Arrays.asList(ArticleConstant.ARTICLE_TYPE_NORMAL, ArticleConstant.ARTICLE_TYPE_VIDEO, ArticleConstant.ARTICLE_TYPE_NORMAL_SPLIT));
        } else if (ObjUtil.equals("video", mode)) {
            ewArticleQuery.eq(Article::getArticleCatId, ArticleConstant.ARTICLE_CAT_VIDEO);
        } else if (ObjUtil.equals("square", mode)) {
            ewArticleQuery.eq(Article::getArticleCatId, ArticleConstant.ARTICLE_CAT_NORMAL);
        }

        Page<Article> page = iArticleService.page(new Page<Article>(pageNum, pageSize), ewArticleQuery);

        if (CollUtil.isNotEmpty(page.getRecords())) {
            LambdaQueryWrapper<OfficialAccount> oaQuery = new LambdaQueryWrapper<>();
            oaQuery.in(OfficialAccount::getId, page.getRecords().stream().map(Article::getOfficialAccountId).collect(Collectors.toList()));

            List<OfficialAccount> officialAccountList = iOfficialAccountService.list(oaQuery);

            for (Article x : page.getRecords()) {
                Optional<OfficialAccount> temp = officialAccountList.stream().filter(m -> Objects.equals(m.getId(), x.getOfficialAccountId())).findAny();
                if (temp.isPresent()) {
                    x.setSource(temp.get().getName());
                    x.setSourceImgUrl(temp.get().getLogoImage());
                }
                x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                x.likeNumAddInit();
            }
        }
        return page;
    }


    /**
     * 新闻列表接口（首页）
     */
    @Anonymous
    @GetMapping("/v1.0/article/list")
    public ResultData newsList(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize, @RequestParam(value = "customerId", required = false) Long customerId) {

        //获取默认订阅号；
        QueryWrapper<OfficialAccount> officialAccountQueryWrapper = new QueryWrapper<>();
        officialAccountQueryWrapper.eq("defaulted", 1);
        officialAccountQueryWrapper.eq("category_type", 0);
        officialAccountQueryWrapper.eq("deleted", 0);
        List<OfficialAccount> officialAccounts = iOfficialAccountService.list(officialAccountQueryWrapper);
        List<Long> collect = officialAccounts.stream().map(OfficialAccount::getId).collect(Collectors.toList());
        //获取订阅的订阅号；
        if (null != customerId) {
            QueryWrapper<OfficialFollow> followEw = new QueryWrapper<>();
            followEw.eq("c_user_id", customerId);
            List<OfficialFollow> officialFollows = iOfficialFollowService.list(followEw);
            //获取要查询的订阅号
            collect.addAll(officialFollows.stream().map(OfficialFollow::getOfficialAccountId).collect(Collectors.toList()));
        }
        if (CollUtil.isEmpty(collect)) {
            return new ResultData();
        }
        //构建查询条件
        QueryWrapper<Article> ew = new QueryWrapper<>();
        //审核完成状态
        ew.eq("status", ArticleConstant.ARTICLE_STATUS_APPROVE);
        ew.eq("category_type", 0);
        //开启呈现
        ew.eq("enabled", 0);
        //首页数据不呈现集合数据
        ew.in("article_type", Arrays.asList(ArticleConstant.ARTICLE_TYPE_NORMAL, ArticleConstant.ARTICLE_TYPE_VIDEO));
        //首页默认呈现1、推荐状态的；2、默认订阅号的；3、已订阅订阅号的
        //ew.and().eq("recommended", 1).or().in("official_account_id", collect);
        ew.in("official_account_id", collect);
        //创建时间倒叙
        ew.orderByDesc("create_time");

        IPage<Article> page = new Page<>(pageNum, pageSize, false);
        IPage<Article> pages = iArticleService.page(page, ew);

        //补充订阅号信息
        if (!pages.getRecords().isEmpty()) {
            List<Long> articleIds = pages.getRecords().stream().map(Article::getId).collect(Collectors.toList());
            QueryWrapper<OfficialArticleCollect> ewCollectQuery = new QueryWrapper<>();
            //审核完成状态
            ewCollectQuery.eq("c_user_id", customerId);
            ewCollectQuery.in("article_id", articleIds);
            List<OfficialArticleCollect> officialArticleCollects = iOfficialArticleCollectService.list(ewCollectQuery);
            QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
            //审核完成状态
            ewLikeQuery.eq("c_user_id", customerId);
            ewLikeQuery.in("article_id", articleIds);
            List<OfficialArticleLike> officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);

            List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(collect, null);
            List<ArticleCollectionRef> collectInfoByArticles = iArticleCollectionRefService.getCollectInfoByArticles(pages.getRecords());
            JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));

            for (Article x : pages.getRecords()) {
                if (StrUtil.isNotBlank(OfficialConfig.SHARE_IMG_URL)) {
                    x.setShareImgUrl(OfficialConfig.SHARE_IMG_URL);
                } else if (StrUtil.isNotBlank(x.getImgUrl())) {
                    x.setShareImgUrl(x.getImgUrl().contains(",") ? x.getImgUrl().split(",")[0] : x.getImgUrl());
                }
                //设定集合信息
                if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())) {
                    collectInfoByArticles.stream().filter(y -> y.getSubArticleId().equals(x.getId())).findFirst().ifPresent(y -> {
                        x.setCollectionId(y.getArticleId());
                        x.setCollectionTitle(y.getCollectionTitle());
                    });
                }

                //评论总设置和单条设置有一个为关闭评论，整体关闭评论
                if (!"1".equals(config.getString("isComment"))) {
                    x.setIsComment(0L);
                }

                x.setContent(null);
                Optional<OfficialAccountDTO> any = officialAccountList.stream().filter(m -> Objects.equals(m.getId(), x.getOfficialAccountId())).findAny();
                if (any.isPresent()) {
                    x.setSource(any.get().getName());
                    x.setSourceImgUrl(any.get().getLogoImage());
                    x.setOfficialCateName(any.get().getCategoryName());
                    x.setOfficialCreateBy(any.get().getCreateBy());
                }
                x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                Optional<OfficialArticleCollect> anyCollect = officialArticleCollects.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                if (anyCollect.isPresent()) {
                    x.setIsCollect(1);
                }
                if (anyLike.isPresent()) {
                    x.setIsLike(1);
                }
                x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                x.likeNumAddInit();
            }
        }

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }

    /**
     * 首页
     *
     * @param pageNum
     * @param pageSize
     * @param customerId
     * @return
     */
    @Anonymous
    @GetMapping("/v2.0/article/list")
    public ResultData newsListV2(@RequestParam(value = "pageNum") int pageNum,
                                 @RequestParam(value = "pageSize") int pageSize,
                                 @RequestParam(value = "customerId", required = false) Long customerId,
                                 @RequestParam(value = "mode", required = false, defaultValue = "hot") String mode) {

        List<Long> blackUids = List.of();
        if (null != customerId && blackFlag) {
            blackUids = restSiteAppUtil.getBlackList(customerId);
        }

        IPage<Article> page = new Page<>(pageNum, pageSize, false);
        LambdaQueryWrapper<Article> ew = Wrappers.lambdaQuery();
        //获取默认订阅号；
        if (ObjUtil.equals("hot", mode)) {//（默认1：热门推荐，2:普通模式,3 视频模式；4:广场模式）
            QueryWrapper<OfficialAccount> officialAccountQueryWrapper = new QueryWrapper<>();
            officialAccountQueryWrapper.eq("defaulted", 1);
            officialAccountQueryWrapper.eq("deleted", 0);
            List<OfficialAccount> officialAccounts = iOfficialAccountService.list(officialAccountQueryWrapper);
            List<Long> collect = officialAccounts.stream().map(OfficialAccount::getId).collect(Collectors.toList());
            //获取订阅的订阅号；
            if (null != customerId) {
                QueryWrapper<OfficialFollow> followEw = new QueryWrapper<>();
                followEw.eq("c_user_id", customerId);
                List<OfficialFollow> officialFollows = iOfficialFollowService.list(followEw);
                //获取要查询的订阅号
                collect.addAll(officialFollows.stream().map(OfficialFollow::getOfficialAccountId).collect(Collectors.toList()));
            }
            if (CollUtil.isEmpty(collect)) {
                return new ResultData();
            }
            ew.in(CollUtil.isNotEmpty(collect), Article::getOfficialAccountId, collect);
            //首页数据不呈现集合数据
            ew.in(Article::getType, Arrays.asList(ArticleConstant.ARTICLE_TYPE_NORMAL, ArticleConstant.ARTICLE_TYPE_VIDEO, ArticleConstant.ARTICLE_TYPE_NORMAL_SPLIT));
        } else if (ObjUtil.equals("video", mode)) {
            ew.eq(Article::getArticleCatId, ArticleConstant.ARTICLE_CAT_VIDEO);
        } else if (ObjUtil.equals("square", mode)) {
            ew.eq(Article::getArticleCatId, ArticleConstant.ARTICLE_CAT_NORMAL);
        }

        ew.notIn(CollUtil.isNotEmpty(blackUids), Article::getCreateBy, blackUids);

        //审核完成状态
        ew.eq(Article::getStatus, ArticleConstant.ARTICLE_STATUS_APPROVE);
        //开启呈现
        ew.eq(Article::getEnabled, 0);

        //创建时间倒叙
        ew.orderByDesc(Article::getSort);
        ew.orderByDesc(Article::getCreateTime);

        IPage<Article> pages = iArticleService.page(page, ew);

        //补充订阅号信息
        if (!pages.getRecords().isEmpty()) {
            List<Long> articleIds = pages.getRecords().stream().map(Article::getId).collect(Collectors.toList());
            QueryWrapper<OfficialArticleCollect> ewCollectQuery = new QueryWrapper<>();
            //审核完成状态
            ewCollectQuery.eq("c_user_id", customerId);
            ewCollectQuery.in("article_id", articleIds);
            List<OfficialArticleCollect> officialArticleCollects = iOfficialArticleCollectService.list(ewCollectQuery);
            QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
            //审核完成状态
            ewLikeQuery.eq("c_user_id", customerId);
            ewLikeQuery.in("article_id", articleIds);
            List<OfficialArticleLike> officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);

            List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(
                    pages.getRecords().stream().map(Article::getOfficialAccountId).collect(Collectors.toList()), null);
            List<ArticleCollectionRef> collectInfoByArticles = iArticleCollectionRefService.getCollectInfoByArticles(pages.getRecords());
            JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));

            for (Article x : pages.getRecords()) {
                if (StrUtil.isNotBlank(OfficialConfig.SHARE_IMG_URL)) {
                    x.setShareImgUrl(OfficialConfig.SHARE_IMG_URL);
                } else if (StrUtil.isNotBlank(x.getImgUrl())) {
                    x.setShareImgUrl(x.getImgUrl().contains(",") ? x.getImgUrl().split(",")[0] : x.getImgUrl());
                }
                //设定集合信息
                if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())) {
                    collectInfoByArticles.stream().filter(y -> y.getSubArticleId().equals(x.getId())).findFirst().ifPresent(y -> {
                        x.setCollectionId(y.getArticleId());
                        x.setCollectionTitle(y.getCollectionTitle());
                    });
                }

                //评论总设置和单条设置有一个为关闭评论，整体关闭评论
                if (!"1".equals(config.getString("isComment"))) {
                    x.setIsComment(0L);
                }

                x.setContent(null);
                Optional<OfficialAccountDTO> any = officialAccountList.stream().filter(m -> Objects.equals(m.getId(), x.getOfficialAccountId())).findAny();
                if (any.isPresent()) {
                    x.setSource(any.get().getName());
                    x.setSourceImgUrl(any.get().getLogoImage());
                    x.setOfficialCateName(any.get().getCategoryName());
                    x.setOfficialCreateBy(any.get().getCreateBy());
                }
                x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                Optional<OfficialArticleCollect> anyCollect = officialArticleCollects.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                if (anyCollect.isPresent()) {
                    x.setIsCollect(1);
                }
                if (anyLike.isPresent()) {
                    x.setIsLike(1);
                }
                x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                x.likeNumAddInit();
            }
        }

        ResultData resultData = new ResultData();
        resultData.addContentData("objs", pages.getRecords());
        resultData.addContentData("pageNo", pages.getCurrent());
        resultData.addContentData("pageSize", pages.getSize());
        resultData.addContentData("totalCount", pages.getTotal());
        resultData.addContentData("totalPages", pages.getPages());
        return resultData;
    }

    @Anonymous
    @GetMapping("/v1.0/article/detail")
    @JsonView(Views.API.class)
    public ResultData articleDetail(@RequestParam(value = "articleId") Long articleId,
                                    @RequestParam(value = "customerId", required = false) String customerId,
                                    @RequestParam(value = "commentSort", required = false) Integer commentSort,
                                    @RequestParam(value = "mode", defaultValue = "0") Integer mode) {//0 默认，1 预览

        Article article = iArticleService.getById(articleId);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            customerId = String.valueOf(loginUser.getUserId());
        }
        if (null != article && (ObjUtil.equals(1, mode) ||
                (ObjUtil.equals(article.getEnabled(), 0) && ObjUtil.equals(article.getStatus(), 0))
                || (ObjUtil.isNotNull(loginUser) && StrUtil.equals(StrUtil.toString(loginUser.getUserId()), article.getCreateBy()))
        )) {
            article.setIsCollect(0);
            article.setIsLike(0);
            article.setIsOfficialFollow(0);
            if (StrUtil.isNotEmpty(customerId)) {
                QueryWrapper<OfficialArticleCollect> ewCollectQuery = new QueryWrapper<>();
                ewCollectQuery.eq("c_user_id", customerId);
                ewCollectQuery.eq("article_id", article.getId());
                List<OfficialArticleCollect> officialArticleCollects = iOfficialArticleCollectService.list(ewCollectQuery);
                QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
                ewLikeQuery.eq("c_user_id", customerId);
                ewLikeQuery.eq("article_id", article.getId());
                List<OfficialArticleLike> officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);

                Optional<OfficialArticleCollect> anyCollect = officialArticleCollects.stream().findAny();
                Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().findAny();
                if (anyCollect.isPresent()) {
                    article.setIsCollect(1);
                }
                if (anyLike.isPresent()) {
                    article.setIsLike(1);
                }
            }
            //设置订阅号信息
            List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(Arrays.asList(article.getOfficialAccountId()), customerId);
            article.setSourceImgUrl(officialAccountList.get(0).getLogoImage());
            article.setSource(officialAccountList.get(0).getName());
            article.setOfficialCateName(officialAccountList.get(0).getCategoryName());
            article.setOfficialCreateBy(officialAccountList.get(0).getCreateBy());
            article.setIsOfficialFollow(officialAccountList.get(0).getFollowed());
            if (ObjUtil.equals(article.getType(), ArticleConstant.ARTICLE_TYPE_VIDEO) || ObjUtil.equals(article.getType(), ArticleConstant.ARTICLE_TYPE_COLLECT)) {
                //视频和合集只显示第一张图片
                int tempNum = article.getImgUrl().indexOf(",");
                if (tempNum > 0) {
                    article.setImgUrl(article.getImgUrl().substring(0, tempNum));
                }
            }
            article.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + article.getId());

            //查询属于集合，然后关联呈现集合标题
            if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(article.getType())) {
                iArticleCollectionRefService.getOne(new QueryWrapper<ArticleCollectionRef>().eq("sub_article_id", articleId));
                ArticleCollectionRef articleCollectionRef = iArticleCollectionRefService.getOne(new QueryWrapper<ArticleCollectionRef>().eq("sub_article_id", articleId));
                if (null != articleCollectionRef) {
                    Article articleParent = iArticleService.getById(articleCollectionRef.getArticleId());
                    if (ArticleConstant.ARTICLE_STATUS_APPROVE.equals(articleParent.getStatus())) {
                        article.setCollectionTitle(articleParent.getTitle());
                        article.setCollectionId(articleParent.getId());
                    }
                }
            } else if (ArticleConstant.ARTICLE_TYPE_COLLECT.equals(article.getType())) {
                article.setViewed(iArticleCollectionRefService.getCollectViewedByArticleId(article.getId()));
            }
            article.setViewed(iArticleService.sumView(article.getViewed(), article.getInitView()));
            article.likeNumAddInit();
            JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));
            if (!ArticleConstant.ARTICLE_TYPE_COLLECT.equals(article.getType())) {
                //评论总设置和单条设置有一个为关闭评论，整体关闭评论
                if (!"1".equals(config.getString("isComment"))) {
                    article.setIsComment(0L);
                }
            }

            //关联新闻数据
            if ("1".equals(config.getString("isShowNews")) && "0".equals(article.getCategoryType())) {//PGC 才呈现相关新闻
                QueryWrapper<Article> ew = new QueryWrapper<>();
                //审核完成状态
                ew.eq("status", ArticleConstant.ARTICLE_STATUS_APPROVE);
                //开启呈现
                ew.eq("enabled", 0);
                ew.eq("official_account_id", article.getOfficialAccountId());
                ew.eq("article_cat_id", article.getArticleCatId());
                ew.eq("display_mode", 1);
                ew.ne("id", article.getId());//关联新闻不=当前新闻
                ew.orderByDesc("create_time");
                IPage<Article> pages = iArticleService.page(new Page(1, 3), ew);
                if (CollUtil.isNotEmpty(pages.getRecords())) {
                    pages.getRecords().forEach(x -> {
                        x.setContent(null);
                        x.setSource(officialAccountList.get(0).getName());
                        x.setSourceImgUrl(officialAccountList.get(0).getLogoImage());
                    });
                }
                article.setRelatedNewsList(pages.getRecords());
            }

            //获取评论
            if (article.getIsComment() == 1) {
                QueryWrapper<ArticleComment> ewComment = new QueryWrapper<>();
                ewComment.eq("article_id", article.getId());
                ewComment.eq("status", 0);
                ewComment.orderBy(true, Integer.valueOf(1).equals(commentSort), "create_time");
                List<ArticleComment> articleComments = iArticleCommentService.list(ewComment);
                article.setCommentNum((long) articleComments.size());
                if (article.getCommentNum() > 0 && StrUtil.isNotBlank(customerId)) {
                    officialCommentLikeService.lambdaQuery().eq(OfficialCommentLike::getUid, customerId)
                            .in(OfficialCommentLike::getCommentId, articleComments.stream().map(ArticleComment::getId).collect(Collectors.toList())).list().forEach(like -> {
                                articleComments.stream().filter(comment -> comment.getId().equals(like.getCommentId())).findAny().ifPresent(comment -> {
                                    comment.setIsLike(1);
                                });
                            });
                }

                List<ArticleComment> tree = new ArraysToTreeUtil<ArticleComment>().code(ArticleComment::getId).parent(ArticleComment::getParentId).children(comment -> {
                    if (comment.getCommentList() == null) {
                        comment.setCommentList(new ArrayList<>());
                    }
                    return comment.getCommentList();
                }).tree(articleComments);
                article.setCommentList(tree);
            }

            if (StrUtil.isNotBlank(OfficialConfig.SHARE_IMG_URL)) {
                article.setShareImgUrl(OfficialConfig.SHARE_IMG_URL);
            } else if (StrUtil.isNotBlank(article.getImgUrl())) {
                article.setShareImgUrl((article.getImgUrl().contains(",") ? article.getImgUrl().split(",")[0] : article.getImgUrl()));
            }
        } else {
            article = null;
        }

        //获取关联资讯
        ResultData resultData = new ResultData();
        resultData.setContent(article);
        return resultData;
    }

    @Anonymous
    @GetMapping("/v1.0/article/commentList")
    @JsonView(Views.API.class)
    public ResultData articleCommentList(@RequestParam(value = "articleId") Long articleId, @RequestParam(value = "commentSort", required = false) Integer commentSort) {

        QueryWrapper<ArticleComment> ewComment = new QueryWrapper<>();
        ewComment.eq("article_id", articleId);
        ewComment.eq("status", 0);
        ewComment.orderBy(true, Integer.valueOf(1).equals(commentSort), "create_time");
        List<ArticleComment> articleComments = iArticleCommentService.list(ewComment);
        List<ArticleComment> tree = new ArraysToTreeUtil<ArticleComment>().code(ArticleComment::getId).parent(ArticleComment::getParentId).children(comment -> {
            if (comment.getCommentList() == null) {
                comment.setCommentList(new ArrayList<>());
            }
            return comment.getCommentList();
        }).tree(articleComments);
        //获取关联资讯
        ResultData resultData = new ResultData();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("objs", tree);
        resultMap.put("totalCount", articleComments.size());
        resultData.setContent(resultMap);
        return resultData;
    }

    @Resource
    private IOfficialArticleShareService iOfficialArticleShareService;

    @Anonymous
    @PostMapping("/v1.0/article/view/{id}")
    public ResultData articleView(@PathVariable Long id) {
        iArticleService.updateView(id);
        return new ResultData();
    }

    @Anonymous
    @PostMapping("/v1.0/article/share")
    public ResultData articleShare(@RequestBody OfficialArticleShare officialArticleShare) {
        return new ResultData(iOfficialArticleShareService.saveAndEvent(officialArticleShare));
    }

    /**
     * 收藏文章
     */
    @PostMapping("/v1.0/article/collect")
    public ResultData collectArticle(@RequestBody OfficialArticleCollect officialArticleCollect) {
        try {
            officialArticleCollect.setcUserId(StrUtil.toStringOrNull(SecurityUtils.getUserId()));
            if (iOfficialArticleCollectService.save(officialArticleCollect)) {
                officialArticleCollect.setArticleIds(String.valueOf(officialArticleCollect.getArticleId()));
                iArticleService.updateCollect(officialArticleCollect.getArticleIds(), 1);

                officialOperLogService.saveLog(officialArticleCollect.getcUserId(), officialArticleCollect.getArticleId(), null, 3);

                return new ResultData();
            } else {
                return new ResultData(-1, "关注失败");
            }
        } catch (DuplicateKeyException duplicateKeyException) {
            return new ResultData(-1, "请不要重复收藏");
        }
    }

    /**
     * 取消收藏文章
     */
    @PostMapping("/v1.0/article/unCollect")
    public ResultData unCollectArticle(@RequestBody OfficialArticleCollect officialArticleCollect) {
        officialArticleCollect.setcUserId(StrUtil.toStringOrNull(SecurityUtils.getUserId()));
        if (ObjUtil.isNotNull(officialArticleCollect.getArticleId()) && StrUtil.isEmpty(officialArticleCollect.getArticleIds())) {
            officialArticleCollect.setArticleIds(String.valueOf(officialArticleCollect.getArticleId()));
        }

        QueryWrapper<OfficialArticleCollect> ewCollect = new QueryWrapper<>();
        ewCollect.in("article_id", officialArticleCollect.getArticleIds().split(","));
        ewCollect.eq("c_user_id", officialArticleCollect.getcUserId());

        officialOperLogService.saveLog(officialArticleCollect.getcUserId(), officialArticleCollect.getArticleId(), null, 4);

        if (iOfficialArticleCollectService.remove(ewCollect)) {
            //articleId 可以传递多个
            iArticleService.updateCollect(officialArticleCollect.getArticleIds(), -1);
            return new ResultData();

        } else {

            return new ResultData(-1, "取消失败");

        }

    }


    /**
     * 点赞文章
     */
    @PostMapping("/v1.0/article/like")
    public ResultData likeArticle(@RequestBody OfficialArticleLike officialArticleLike) {
        try {
            iOfficialArticleLikeService.like(officialArticleLike);
            return new ResultData();
        } catch (DuplicateKeyException duplicateKeyException) {
            return new ResultData(-1, "点赞失败");
        }
    }

    /**
     * 取消点赞文章
     */
    @PostMapping("/v1.0/article/unLike")
    public ResultData unLikeArticle(@RequestBody OfficialArticleLike officialArticleLike) {
        try {
            officialArticleLike.setcUserId(StrUtil.toStringOrNull(SecurityUtils.getUserId()));
            Map<String, Object> deleteMap = new HashMap<>();
            deleteMap.put("article_id", officialArticleLike.getArticleId());
            deleteMap.put("c_user_id", officialArticleLike.getcUserId());
            iOfficialArticleLikeService.removeByMap(deleteMap);
            Article article = iArticleService.getById(officialArticleLike.getArticleId());
            iArticleService.updateLikeNum(officialArticleLike.getArticleId(), -1);
            iArticleService.updateOfficialLike(article.getOfficialAccountId(), -1);
            officialOperLogService.saveLog(officialArticleLike.getcUserId(), officialArticleLike.getArticleId(), null, 6);
            return new ResultData();
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResultData(-1, "取消点赞失败");
        }
    }

    @Resource
    private IOfficialCommentLikeService officialCommentLikeService;

    /**
     * 点赞评论
     */
    @PostMapping("/v1.0/official/commentLike")
    public ResultData commentLike(@RequestBody @Valid OfficialCommentLike officialCommentLike) {
        try {
            officialCommentLikeService.like(officialCommentLike);
            return new ResultData();
        } catch (DuplicateKeyException duplicateKeyException) {
            return new ResultData(-1, "点赞失败");
        }
    }

    /**
     * 取消点赞评论
     */
    @PostMapping("/v1.0/official/commentUnLike")
    public ResultData commentUnLike(@RequestBody OfficialCommentLike officialCommentLike) {
        try {
            officialCommentLike.setUid(StrUtil.toStringOrNull(SecurityUtils.getUserId()));

            officialCommentLikeService.lambdaUpdate().eq(OfficialCommentLike::getCommentId, officialCommentLike.getCommentId())
                    .eq(OfficialCommentLike::getUid, officialCommentLike.getUid()).remove();

            iArticleCommentService.lambdaUpdate().eq(ArticleComment::getId, officialCommentLike.getCommentId())
                    .setDecrBy(ArticleComment::getLikeNum, 1).update();

            officialOperLogService.saveLog(officialCommentLike.getUid(), officialCommentLike.getCommentId(), null, 8);
            return new ResultData();
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResultData(-1, "取消点赞失败");
        }
    }

    /**
     * 新增评论接口
     */
    @Log(title = "订阅号评论", businessType = BusinessType.UPDATE)
    @PostMapping("/v1.0/article/comment")
    public ResultData addComment(HttpServletRequest request, @RequestBody ArticleComment articleComment) {
        articleComment.setCreateBy(StrUtil.toStringOrNull(SecurityUtils.getUserId()));

        Article article = iArticleService.getById(articleComment.getArticleId());
        if (null == article) {
            return new ResultData(-1, "新闻不存在");
        }

        ResultData resultData = new ResultData();
        if (APISystemConfig.wangyiCheck) {
            wangyiCheckUtil.checkText(3001, null, articleComment.getContent());
            wangyiCheckUtil.checkImages(3001, articleComment.getImgUrl());
        }

        articleComment.setArticleTitle(article.getTitle());
        articleComment.setOfficialAccountId(article.getOfficialAccountId());
        //根据是否有父ID，设置类型
        if (null != articleComment.getParentId() && articleComment.getParentId() != 0L) {
            articleComment.setType(2);
        } else {
            articleComment.setType(1);
        }
        articleComment.setCreateTime(new Date());
        JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));
        if ("1".equals(config.getString("isExamine"))) {
            articleComment.setStatus(1);
        } else {
            articleComment.setStatus(0);
        }

        //获取IP地址
        String ip = IpUtils.getIpAddr(request);
        articleComment.setIp(ip);
        articleComment.setRegion(AddressUtils.getRealAddressByIP(ip));

        if (iArticleCommentService.save(articleComment)) {
            if (articleComment.getStatus().equals(0)) {
                articleComment.setTotalNum(article.getCommentNum() + 1L);
                iArticleService.updateCommentNum(article.getId(), 1);
            } else {
                articleComment.setTotalNum(article.getCommentNum());
            }
            applicationEventPublisher.publishEvent(new OfCommentEvent(this, articleComment,
                    EventAction.builder().operator(SecurityUtils.getLoginUser()).type(BusinessType.INSERT).build()));
        } else {
            return new ResultData(-1, "评论失败");
        }
        return new ResultData(articleComment);
    }

    /**
     * 删除评论
     */
    @Log(title = "订阅号评论", businessType = BusinessType.DELETE)
    @PostMapping("/v1.0/article/delComment")
    public ResultData delComment(@RequestBody ArticleComment articleComment) {

        if (StrUtil.isNotEmpty(articleComment.getIds()) && null != articleComment.getArticleId()) {
            //3个人删除、2管理员删除
            Integer status = 3;
            String updateBy = articleComment.getUid();
            if (StrUtil.isEmpty(articleComment.getUid())) {//作者删除
                status = 2;
                Article article = iArticleService.getById(articleComment.getArticleId());
                updateBy = article.getCreateBy();
            }
            if (ObjUtil.notEqual(updateBy, StrUtil.toStringOrNull(SecurityUtils.getUserId()))) {
                return new ResultData(-1, "无权限删除");
            }

            List<ArticleComment> articleComments = new ArrayList<>();
            for (String s : articleComment.getIds().split(",")) {
                ArticleComment articleCommentUpdate = new ArticleComment();
                articleCommentUpdate.setId(Long.valueOf(s));
                articleCommentUpdate.setStatus(status);
                articleCommentUpdate.setUpdateBy(updateBy);
                articleComments.add(articleCommentUpdate);
            }

            //查询子评论
            List<Long> collect = articleComments.stream().map(ArticleComment::getId).collect(Collectors.toList());
            QueryWrapper<ArticleComment> ewCollect = new QueryWrapper<>();
            ewCollect.in("parent_id", collect);
            ewCollect.in("status", Arrays.asList(0, 1));
            List<ArticleComment> articleCommentsChildren = iArticleCommentService.list(ewCollect);

            for (ArticleComment temp : articleCommentsChildren) {
                temp.setStatus(status);
                temp.setUpdateBy(updateBy);
            }
            articleComments.addAll(articleCommentsChildren);

            if (iArticleCommentService.updateBatchById(articleComments)) {
                QueryWrapper<ArticleComment> ewCollectCount = new QueryWrapper<>();
                ewCollectCount.eq("status", 0);
                ewCollectCount.eq("article_id", articleComment.getArticleId());
                long i = iArticleCommentService.count(ewCollectCount);

                Article article = new Article();
                article.setId(articleComment.getArticleId());
                article.setCommentNum(i);
                iArticleService.updateById(article);
                return new ResultData(i);
            }
        }
        return new ResultData(-1, "删除失败");
    }

    /**
     * 根据订阅号是否推荐，类型来查询新闻列表
     *
     * @param pageNum
     * @param pageSize
     * @param customerId
     * @param recommended
     * @param officialCategoryId
     * @return
     */
    @Anonymous
    @GetMapping("/v1.0/article/listByOfficial")
    public ResultData listByOfficial(@RequestParam(value = "pageNum") int pageNum,
                                     @RequestParam(value = "pageSize") int pageSize,
                                     @RequestParam(value = "customerId", required = false) Long customerId,
                                     @RequestParam(value = "recommended", required = false, defaultValue = "0") Integer recommended,
                                     @RequestParam(value = "officialCategoryId", required = false) Long officialCategoryId) {
        //获取默认订阅号；
        QueryWrapper<OfficialAccount> officialAccountQueryWrapper = new QueryWrapper<>();

        if (null != officialCategoryId) {
            officialAccountQueryWrapper.eq("category_id", officialCategoryId);
        } else {
            recommended = 1;//如果分类不给，默认推荐
        }

        if (recommended == 1) {
            officialAccountQueryWrapper.eq("recommended", 1);
        }
        officialAccountQueryWrapper.eq("deleted", 0);
        List<OfficialAccount> officialAccounts = iOfficialAccountService.list(officialAccountQueryWrapper);
        if (CollUtil.isNotEmpty(officialAccounts)) {
            List<Long> collect = officialAccounts.stream().map(OfficialAccount::getId).collect(Collectors.toList());

            //构建查询条件
            QueryWrapper<Article> ew = new QueryWrapper<>();
            //审核完成状态
            ew.eq("status", ArticleConstant.ARTICLE_STATUS_APPROVE);
            //开启呈现
            ew.eq("enabled", 0);
            //首页数据不呈现集合数据
            ew.in("type", Arrays.asList(ArticleConstant.ARTICLE_TYPE_NORMAL, ArticleConstant.ARTICLE_TYPE_VIDEO));

            ew.in("official_account_id", collect);
            //创建时间倒叙
            ew.orderByDesc("create_time");

            IPage<Article> page = new Page(pageNum, pageSize, false);
            IPage<Article> pages = iArticleService.page(page, ew);

            //补充订阅号信息
            if (!pages.getRecords().isEmpty()) {
                List<Long> articleIds = pages.getRecords().stream().map(Article::getId).collect(Collectors.toList());
                QueryWrapper<OfficialArticleCollect> ewCollectQuery = new QueryWrapper<>();
                List<OfficialArticleCollect> officialArticleCollects = null;
                List<OfficialArticleLike> officialArticleLikes = null;
                if (customerId != null) {
                    //审核完成状态
                    ewCollectQuery.eq("c_user_id", customerId);
                    ewCollectQuery.in("article_id", articleIds);
                    officialArticleCollects = iOfficialArticleCollectService.list(ewCollectQuery);
                    QueryWrapper<OfficialArticleLike> ewLikeQuery = new QueryWrapper<>();
                    //审核完成状态
                    ewLikeQuery.eq("c_user_id", customerId);
                    ewLikeQuery.in("article_id", articleIds);
                    officialArticleLikes = iOfficialArticleLikeService.list(ewLikeQuery);
                }
                List<OfficialAccountDTO> officialAccountList = iOfficialAccountService.selectAccountById(collect, null);
                List<ArticleCollectionRef> collectInfoByArticles = iArticleCollectionRefService.getCollectInfoByArticles(pages.getRecords());
                JSONObject config = JSON.parseObject(sysConfigService.selectConfigByKey("gdmm.news.setting"));

                for (Article x : pages.getRecords()) {
                    if (StrUtil.isNotBlank(OfficialConfig.SHARE_IMG_URL)) {
                        x.setShareImgUrl(OfficialConfig.SHARE_IMG_URL);
                    } else if (StrUtil.isNotBlank(x.getImgUrl())) {
                        x.setShareImgUrl(x.getImgUrl().contains(",") ? x.getImgUrl().split(",")[0] : x.getImgUrl());
                    }
                    //设定集合信息
                    if (ArticleConstant.ARTICLE_TYPE_VIDEO.equals(x.getType())) {
                        collectInfoByArticles.stream().filter(y -> y.getSubArticleId().equals(x.getId())).findFirst().ifPresent(y -> {
                            x.setCollectionId(y.getArticleId());
                            x.setCollectionTitle(y.getCollectionTitle());
                        });
                    }

                    //评论总设置和单条设置有一个为关闭评论，整体关闭评论
                    if (!"1".equals(config.getString("isComment"))) {
                        x.setIsComment(0L);
                    }

                    x.setContent(null);
                    Optional<OfficialAccountDTO> any = officialAccountList.stream().filter(m -> Objects.equals(m.getId(), x.getOfficialAccountId())).findAny();
                    if (any.isPresent()) {
                        x.setSource(any.get().getName());
                        x.setSourceImgUrl(any.get().getLogoImage());
                        x.setOfficialCateName(any.get().getCategoryName());
                        x.setOfficialCreateBy(any.get().getCreateBy());
                    }
                    x.setShareUrl(APISystemConfig.apiUrl + "/api-2c/page/article/" + x.getId());
                    if (officialArticleCollects != null) {
                        Optional<OfficialArticleCollect> anyCollect = officialArticleCollects.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                        if (anyCollect.isPresent()) {
                            x.setIsCollect(1);
                        }
                    }
                    if (officialArticleLikes != null) {
                        Optional<OfficialArticleLike> anyLike = officialArticleLikes.stream().filter(y -> y.getArticleId().equals(x.getId())).findAny();
                        if (anyLike.isPresent()) {
                            x.setIsLike(1);
                        }
                    }
                    x.setViewed(iArticleService.sumView(x.getViewed(), x.getInitView()));
                    x.likeNumAddInit();
                }
            }

            ResultData resultData = new ResultData();
            resultData.addContentData("objs", pages.getRecords());
            resultData.addContentData("pageNo", pages.getCurrent());
            resultData.addContentData("pageSize", pages.getSize());
            resultData.addContentData("totalCount", pages.getTotal());
            resultData.addContentData("totalPages", pages.getPages());
            return resultData;
        } else {
            ResultData resultData = new ResultData();
            resultData.addContentData("objs", 0);
            resultData.addContentData("pageNo", pageNum);
            resultData.addContentData("pageSize", pageSize);
            resultData.addContentData("totalCount", 0);
            resultData.addContentData("totalPages", 0);
            return resultData;
        }
    }
}
