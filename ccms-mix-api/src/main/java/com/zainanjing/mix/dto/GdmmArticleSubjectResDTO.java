package com.zainanjing.mix.dto;

import com.zainanjing.article.domain.GdmmArticleSubject;
import lombok.Data;

@Data
public class GdmmArticleSubjectResDTO extends GdmmArticleSubject {

    private String sourceName;
    private String sourceImgUrl;
    private Integer isSource;

    public GdmmArticleSubjectResDTO(GdmmArticleSubject gdmmArticleSubject) {
        this.setId(gdmmArticleSubject.getId());
        this.setTitle(gdmmArticleSubject.getTitle());
        this.setRemark(gdmmArticleSubject.getRemark());
        this.setType(gdmmArticleSubject.getType());
        this.setImgUrl(gdmmArticleSubject.getImgUrl());
        this.setEnabled(gdmmArticleSubject.getEnabled());
        this.setSourceId(gdmmArticleSubject.getSourceId());
        this.setRecommendHome(gdmmArticleSubject.getRecommendHome());
        this.setSort(gdmmArticleSubject.getSort());
        this.setCreateTime(gdmmArticleSubject.getCreateTime());
        this.setUpdateTime(gdmmArticleSubject.getUpdateTime());
        this.setManagerId(gdmmArticleSubject.getManagerId());
        this.setStatus(gdmmArticleSubject.getStatus());
        this.setCardId(gdmmArticleSubject.getCardId());
    }

}
