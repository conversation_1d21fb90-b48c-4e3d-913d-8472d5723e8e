package com.zainanjing.mix.dto;

import com.zainanjing.article.domain.GdmmArticle;
import lombok.Data;

@Data
public class GdmmArticleResDTO extends GdmmArticle {

    private String sourceName;

    private String sourceImgUrl;//来源图片

    private String labelImgUrl;//标签图片

    private Integer isExamine;//评论是否审核

    private Integer isClickNum;//新闻是否显示阅读量

    private Integer isShowNews; //是否显示相关新闻

    private Integer isLabel;//是否显示标签

    private Integer isSource;//是否显示来源

    private String shareImgUrl;

    public GdmmArticleResDTO(GdmmArticle gdmmArticle) {
        this.setId(gdmmArticle.getId());
        this.setArticleCatId(gdmmArticle.getArticleCatId());
        this.setType(gdmmArticle.getType());
        this.setTitle(gdmmArticle.getTitle());
        this.setContent(gdmmArticle.getContent());
        this.setQrCodeUrl(gdmmArticle.getQrCodeUrl());
        this.setAuthor(gdmmArticle.getAuthor());
        this.setAuthorEmail(gdmmArticle.getAuthorEmail());
        this.setKeywords(gdmmArticle.getKeywords());
        this.setArticleType(gdmmArticle.getArticleType());
        this.setStatus(gdmmArticle.getStatus());
        this.setEnabled(gdmmArticle.getEnabled());
        this.setIsHot(gdmmArticle.getIsHot());
        this.setRemark(gdmmArticle.getRemark());
        this.setImgUrl(gdmmArticle.getImgUrl());
        this.setAudioUrl(gdmmArticle.getAudioUrl());
        this.setVideoUrl(gdmmArticle.getVideoUrl());
        this.setVideoSecond(gdmmArticle.getVideoSecond());
        this.setVideoImgUrl(gdmmArticle.getVideoImgUrl());
        this.setIdForChinaSearch(gdmmArticle.getIdForChinaSearch());
        this.setTimeForChinaSearch(gdmmArticle.getTimeForChinaSearch());
        this.setArticleDetailUrl(gdmmArticle.getArticleDetailUrl());
        this.setIdForThird(gdmmArticle.getIdForThird());
        this.setViewed(gdmmArticle.getViewed());
        this.setSort(gdmmArticle.getSort());
        this.setIsHighLight(gdmmArticle.getIsHighLight());
        this.setCreateTime(gdmmArticle.getCreateTime());
        this.setDisplayMode(gdmmArticle.getDisplayMode());
        this.setSourceId(gdmmArticle.getSourceId());
        this.setSourceNameForThird(gdmmArticle.getSourceNameForThird());
        this.setLabelId(gdmmArticle.getLabelId());
        this.setIsNotice(gdmmArticle.getIsNotice());
        this.setIsComment(gdmmArticle.getIsComment());
        this.setEditor(gdmmArticle.getEditor());
        this.setGuilinType(gdmmArticle.getGuilinType());
        this.setGuilinId(gdmmArticle.getGuilinId());
        this.setGuilinJson(gdmmArticle.getGuilinJson());
        this.setReporter(gdmmArticle.getReporter());
        this.setSubjectArticleId(gdmmArticle.getSubjectArticleId());
        this.setSubjectCatId(gdmmArticle.getSubjectCatId());
        this.setSubjectId(gdmmArticle.getSubjectId());
        this.setRecommendHome(gdmmArticle.getRecommendHome());
        this.setInitView(gdmmArticle.getInitView());
        this.setViewTimes(gdmmArticle.getViewTimes());
        this.setManagerId(gdmmArticle.getManagerId());
        this.setScore(gdmmArticle.getScore());
        this.setOpenType(gdmmArticle.getOpenType());
        this.setLinkUrl(gdmmArticle.getLinkUrl());
        this.setCardId(gdmmArticle.getCardId());
        this.setPublishStatus(gdmmArticle.getPublishStatus());
        this.setPublishTime(gdmmArticle.getPublishTime());
        this.setFontSize(gdmmArticle.getFontSize());
        this.setCatName(gdmmArticle.getCatName());
        this.setSource(gdmmArticle.getSource());
        this.setLabel(gdmmArticle.getLabel());
        this.setCommentNum(gdmmArticle.getCommentNum());
        this.setCreateTimeS(gdmmArticle.getCreateTimeS());
    }
}
