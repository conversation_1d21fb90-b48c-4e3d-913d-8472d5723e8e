token:
  expire: 604800 #令牌过期时间，单位(s)

#mybatis
mybatis-plus:
  mapper-locations: classpath:/mapper/official/*Mapper.xml,classpath:/mapper/system/*Mapper.xml,classpath*:/mapper/bc/*Mapper.xml
  typeAliasesPackage: com.zainanjing.convenience.**.model.**,com.ruoyi.**.domain.entity
  global-config:
    db-config:
      id-type: auto
      db-column-underline: true
      field-strategy: not_null
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

spring:
  threads:
    virtual:
      enabled: true
  # 默认的profile为dev，其他环境通过指定启动参数使用不同的profile，比如：
  #   测试环境：java -jar my-spring-boot.jar --spring.profiles.active=test
  #   生产环境：java -jar my-spring-boot.jar --spring.profiles.active=prod
  profiles:
    active: test
  #关闭banner
  main:
    banner-mode: "off"
  #thymeleaf config
  thymeleaf:
    mode: HTML
    encoding: UTF-8
    content-type: text/html
    cache: false
  servlet:
    multipart:
      location: /data/temp
  http:
    multipart:
      maxFileSize: 50Mb #上传文件大小
      maxRequestSize: 50Mb
  jackson:
    mapper:
      default-view-inclusion: true
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: true
  #配置虚拟目录
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/, classpath:/static/, classpath:/public/, file:${file-upload.store-dir}
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 200
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 9000000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: false
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: druidBEsjvnE3
        login-password: 52fQe7UuyKMXjSfL
      filter:
        stat:
          enabled: false
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true


sms:
  template:
    commonCaptchaTemplate: SMS_178456933 #通用验证码模板
    forgetPwdCaptchaTemplate: SMS_178456933 #忘记密码验证码短信模板
    registerCaptchaTemplate: SMS_178461944 #注册验证码短信模板
    receiptCaptchaTemplate: SMS_261135317 #回执验证码短信模板
    notifyMerchantTemplate: SMS_253535016 #5分钟通知商户接单短信模板
    storeCloseTemplate: SMS_205433830
  signName: 绵阳 # 城市配置

# 推送配置
push:
  cMasterSecret: 78f55979b4275632e1f44acc
  cAppKey: 3ac5be7d2a576ecda8bf5451

#控制缓存实现方式
cache:
  framework: redis
  ttl: #设置缓存时间秒
    passwordRetryCache: 600

system:
  city: mianyang
  web:
    searchable: false

logging:
  level:
    ROOT: INFO
    com.zainanjing: DEBUG
    com.ruoyi: DEBUG

city-enums:
  LOCATION: ${system.city}
  LOCATION_ZAI: 合意
  LOCATION_APP: ${system.city}
  CITY_ADDRESS_NAME: 合肥
  CITY_NAME_FOR_BUSINESS: 合肥2.0
  BUSINESS_URL: https://nnisei${system.city}new.zainanjing365.com/businessApp
  APP_SCHEMA: gdmm-heyi