#开发环境配置
spring:
  servlet:
    multipart:
      location: /Users/<USER>/Desktop/temp
  #data source config
  datasource:
    druid:
      master:
        url: ************************************************************************************************
        username: bmuser
        password: bmuserA789F
      gdmm:
        enabled: true
        url: ************************************_${system.city}?useUnicode=true&characterEncoding=utf-8&useSSL=false
        username: bmuser
        password: bmuserA789F
  #redis
  data:
    redis:
      database: 1
      host: *************
      port: 6379
      password: K4UMDap0
      timeout: 30000
      pool:
        maxActive: 24
        maxWait: -1
        maxIdle: 10
        minIdle: 0
  jms:
    pub-sub-domain: false
  activemq:
    broker-url: tcp://*************:61616
    user: admin
    password: h9eaXcB#
    pool:
      enabled: true
      max-connections: 100
      idle-timeout: 30000
  elasticsearch:
    rest:
      uris: http://*************:9200
      password: 6jDhjr^GQGqv
      username: elastic


server:
  port: 2002

# 推送配置
push:
  production: false

system:
  tenants:
    flag: false
  api-url: https://bmservicebaodingt.zainanjing365.com
  admin-url: https://bmservicebaodingt.zainanjing365.com
  sms-flag: true
  user-center-url: https://ucenterhefeit.zainanjing365.com/usercenter/
  site-app-url: https://orientalhefeit.zainanjing365.com/siteapp/
  site-app-token: 0Q8rmH7nMKYOHXIU1
  site-app-uid: 2825392
  wap-inter:
    app-id: 110
    app-key: 1692866d6363c0bd792dc7e7346d81aa
  share:
    weixin-secret: a96fb1ad55b2989a9ea6ff0ab277b466
    weixin-appid: wx8781066f0edcdbc3
sms:
  binli:
    account: "010005"
    password: 8V2JNVdR
    url: http://*************:7891/api/v1/send

wangyi:
  check-url: https://imagecensoringt.zainanjing365.com
  check-client: client1
  check-key: dcc7c193b6ef484ab47338b08d6e1e6d

ali-oss:
  url: https://zainanjingshoptest.oss-cn-hangzhou.aliyuncs.com

ruoyi:
  # 名称
  name: 融媒体管理平台
  allowedOrigins:
    - https://*.zainanjing365.com
    - http://*.znjdev.com:1234

city-enums:
  LOCATION: ${system.city}
  LOCATION_ZAI: 麻辣生活
  LOCATION_APP: ${system.city}
  CITY_ADDRESS_NAME: 绵阳
  CITY_NAME_FOR_BUSINESS: 绵阳2.0
  BUSINESS_URL: https://nnisei${system.city}new.zainanjing365.com/businessApp
  APP_SCHEMA: gdmm-malashenghuo