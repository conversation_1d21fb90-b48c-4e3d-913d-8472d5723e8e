package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcActivity;
import com.zainanjing.zijin.service.IBcActivityService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 活动Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bc/activity")
public class BcActivityController extends BaseController {
    @Autowired
    private IBcActivityService bcActivityService;

    /**
     * 查询活动列表
     */
    @PreAuthorize("@ss.hasPermi('bc:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcActivity> page = bcActivityService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:activity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcActivityService.getById(id));
    }

    /**
     * 新增活动
     */
    @PreAuthorize("@ss.hasPermi('bc:activity:add')")
    @Log(title = "活动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcActivity bcActivity) {
        return toAjax(bcActivityService.save(bcActivity));
    }

    /**
     * 修改活动
     */
    @PreAuthorize("@ss.hasPermi('bc:activity:edit')")
    @Log(title = "活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcActivity bcActivity) {
        return toAjax(bcActivityService.updateById(bcActivity));
    }

    /**
     * 删除活动
     */
    @PreAuthorize("@ss.hasPermi('bc:activity:remove')")
    @Log(title = "活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcActivityService.lambdaUpdate().in(BcActivity::getId, ids).set(BcActivity::getIsDel, 1).update());
    }
}
