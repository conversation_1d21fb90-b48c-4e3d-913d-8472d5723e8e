package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.BcAdv;
import com.zainanjing.zijin.service.IBcAdvService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 广告数据Controller
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@RequestMapping("/bc/adv")
public class BcAdvController extends BaseController {
    @Autowired
    private IBcAdvService bcAdvService;

    /**
     * 查询广告数据列表
     */
    @PreAuthorize("@ss.hasPermi('bc:adv:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcAdv> page = bcAdvService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取广告数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:adv:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcAdvService.getById(id));
    }

    /**
     * 新增广告数据
     */
    @PreAuthorize("@ss.hasPermi('bc:adv:add')")
    @Log(title = "广告数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcAdv bcAdv) {
        bcAdv.setCreateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        bcAdv.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        if (ZijinConstants.CONSTANTS_1.equals(String.valueOf(bcAdv.getLinkType()))) {
            if (null != bcAdv.getResourceParamIds()) {
                bcAdv.setLinkUrl(bcAdv.getResourceParamIds().concat("-")
                        .concat(String.valueOf(bcAdv.getResourceId())));
            }
        }
        return toAjax(bcAdvService.save(bcAdv));
    }

    /**
     * 修改广告数据
     */
    @PreAuthorize("@ss.hasPermi('bc:adv:edit')")
    @Log(title = "广告数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcAdv bcAdv) {
        bcAdv.setUpdateTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        if (null != bcAdv.getResourceParamIds()) {
            bcAdv.setLinkUrl(bcAdv.getResourceParamIds().concat("-")
                    .concat(String.valueOf(bcAdv.getResourceId())));
        }
        return toAjax(bcAdvService.updateById(bcAdv));
    }

    /**
     * 删除广告数据
     */
    @PreAuthorize("@ss.hasPermi('bc:adv:remove')")
    @Log(title = "广告数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcAdvService.removeByIds(ids));
    }
}
