package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcTopic;
import com.zainanjing.zijin.service.IBcTopicService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 广播话题Controller
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/bc/topic")
public class BcTopicController extends BaseController {
    @Autowired
    private IBcTopicService bcTopicService;

    /**
     * 查询广播话题列表
     */
    @PreAuthorize("@ss.hasPermi('bc:topic:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcTopic> page = bcTopicService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取广播话题详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:topic:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcTopicService.getById(id));
    }

    /**
     * 新增广播话题
     */
    @PreAuthorize("@ss.hasPermi('bc:topic:add')")
    @Log(title = "广播话题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcTopic bcTopic) {
        return toAjax(bcTopicService.saveMore(bcTopic));
    }

    /**
     * 修改广播话题
     */
    @PreAuthorize("@ss.hasPermi('bc:topic:edit')")
    @Log(title = "广播话题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcTopic bcTopic) {
        bcTopic.setUpdateTime(new Date());
        return toAjax(bcTopicService.updateById(bcTopic));
    }

    /**
     * 删除广播话题
     */
    @PreAuthorize("@ss.hasPermi('bc:topic:remove')")
    @Log(title = "广播话题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcTopicService.removeByIds(ids));
    }
}
