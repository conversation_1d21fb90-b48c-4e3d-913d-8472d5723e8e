package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcChoiceCategory;
import com.zainanjing.zijin.service.IBcChoiceCategoryService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 精选内容分类Controller
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/bc/choiceCategory")
public class BcChoiceCategoryController extends BaseController {
    @Autowired
    private IBcChoiceCategoryService bcChoiceCategoryService;

    /**
     * 查询精选内容分类列表
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCategory:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcChoiceCategory> page = bcChoiceCategoryService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取精选内容分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCategory:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcChoiceCategoryService.getById(id));
    }

    /**
     * 新增精选内容分类
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCategory:add')")
    @Log(title = "精选内容分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcChoiceCategory bcChoiceCategory) {
        bcChoiceCategory.setCreateTime(new Date());
        bcChoiceCategory.setUpdateTime(new Date());
        Optional.ofNullable(bcChoiceCategory.getDescription())
                .ifPresentOrElse(
                        bcChoiceCategory::setDescription,
                        () -> bcChoiceCategory.setDescription("")
                );
        Optional.ofNullable(bcChoiceCategory.getLogo())
                .ifPresentOrElse(
                        bcChoiceCategory::setLogo,
                        () -> bcChoiceCategory.setLogo("")
                );
        Optional.ofNullable(bcChoiceCategory.getShareIcon())
                .ifPresentOrElse(
                        bcChoiceCategory::setShareIcon,
                        () -> bcChoiceCategory.setShareIcon("")
                );
        Optional.ofNullable(bcChoiceCategory.getShareTitle())
                .ifPresentOrElse(
                        bcChoiceCategory::setShareTitle,
                        () -> bcChoiceCategory.setShareTitle("")
                );
        Optional.ofNullable(bcChoiceCategory.getShareDesc())
                .ifPresentOrElse(
                        bcChoiceCategory::setShareDesc,
                        () -> bcChoiceCategory.setShareDesc("")
                );
        return toAjax(bcChoiceCategoryService.save(bcChoiceCategory));
    }

    /**
     * 修改精选内容分类
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCategory:edit')")
    @Log(title = "精选内容分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcChoiceCategory bcChoiceCategory) {
        bcChoiceCategory.setUpdateTime(new Date());
        return toAjax(bcChoiceCategoryService.updateById(bcChoiceCategory));
    }

    /**
     * 删除精选内容分类
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCategory:remove')")
    @Log(title = "精选内容分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcChoiceCategoryService.removeByIds(ids));
    }
}
