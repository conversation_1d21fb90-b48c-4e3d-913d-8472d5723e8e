package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcProgram;
import com.zainanjing.zijin.service.IBcProgramService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 节目名称Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/bc/program")
public class BcProgramController extends BaseController {
    @Autowired
    private IBcProgramService bcProgramService;

    /**
     * 查询节目名称列表
     */
    @PreAuthorize("@ss.hasPermi('bc:program:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcProgram> page = bcProgramService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }


    /**
     * 获取节目名称详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:program:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcProgramService.getById(id));
    }

    /**
     * 新增节目名称
     */
    @PreAuthorize("@ss.hasPermi('bc:program:add')")
    @Log(title = "节目名称", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcProgram bcProgram) {
        return toAjax(bcProgramService.save(bcProgram));
    }

    /**
     * 修改节目名称
     */
    @PreAuthorize("@ss.hasPermi('bc:program:edit')")
    @Log(title = "节目名称", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcProgram bcProgram) {
        return toAjax(bcProgramService.updateById(bcProgram));
    }

    /**
     * 删除节目名称
     */
    @PreAuthorize("@ss.hasPermi('bc:program:remove')")
    @Log(title = "节目名称", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcProgramService.removeByIds(ids));
    }

    /**
     * 根据频率id获取节目列表
     */
    @GetMapping(value = "/queryByFmId/{fmId}")
    public AjaxResult queryByFmId(@PathVariable("fmId") Integer fmId) {
        return AjaxResult.success(bcProgramService.queryByFmId(fmId));
    }
}
