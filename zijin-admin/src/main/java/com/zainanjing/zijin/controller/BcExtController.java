package com.zainanjing.zijin.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.zainanjing.zijin.mapper.BcExtMapper;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/bc/ext")
public class BcExtController {

    @Resource
    private BcExtMapper bcExtMapper;

    /**
     * 获取商品信息
     */
    @RequestMapping("/goods")
    @Anonymous
    public AjaxResult getGoods(@RequestParam("goodsIds") List<Long> goodsIds) {
        return AjaxResult.success(bcExtMapper.selectGoodsByGoodIds(goodsIds));
    }

}
