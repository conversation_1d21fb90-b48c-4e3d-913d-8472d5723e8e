package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcGoods;
import com.zainanjing.zijin.dto.BcGoodsDTO;
import com.zainanjing.zijin.service.IBcGoodsService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 爆品管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bc/goods")
public class BcGoodsController extends BaseController {
    @Autowired
    private IBcGoodsService bcGoodsService;

    /**
     * 查询爆品管理列表
     */
    @PreAuthorize("@ss.hasPermi('bc:goods:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcGoodsDTO> page = bcGoodsService.findBcGoodsList(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取爆品管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:goods:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcGoodsService.getById(id));
    }

    /**
     * 新增爆品管理
     */
    @PreAuthorize("@ss.hasPermi('bc:goods:add')")
    @Log(title = "爆品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcGoods bcGoods) {
        bcGoods.setCreateTime(LocalDateTime.now());
        return toAjax(bcGoodsService.save(bcGoods));
    }

    /**
     * 修改爆品管理
     */
    @PreAuthorize("@ss.hasPermi('bc:goods:edit')")
    @Log(title = "爆品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcGoods bcGoods) {
        bcGoods.setUpdateTime(LocalDateTime.now());
        return toAjax(bcGoodsService.updateById(bcGoods));
    }

    /**
     * 删除爆品管理
     */
    @PreAuthorize("@ss.hasPermi('bc:goods:remove')")
    @Log(title = "爆品管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcGoodsService.lambdaUpdate().in(BcGoods::getId, ids).set(BcGoods::getIsDel, 1).set(BcGoods::getUpdateTime, LocalDateTime.now()).update());
    }
}
