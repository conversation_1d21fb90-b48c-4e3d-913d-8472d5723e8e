package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcProgramTvLiveType;
import com.zainanjing.zijin.service.IBcProgramTvLiveTypeService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电视直播分类Controller
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/bc/programTvLiveType")
public class BcProgramTvLiveTypeController extends BaseController {
    @Autowired
    private IBcProgramTvLiveTypeService bcProgramTvLiveTypeService;

    /**
     * 查询电视直播分类列表
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLiveType:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcProgramTvLiveType> page = bcProgramTvLiveTypeService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取电视直播分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLiveType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcProgramTvLiveTypeService.getById(id));
    }

    /**
     * 新增电视直播分类
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLiveType:add')")
    @Log(title = "电视直播分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcProgramTvLiveType bcProgramTvLiveType) {
        return toAjax(bcProgramTvLiveTypeService.save(bcProgramTvLiveType));
    }

    /**
     * 修改电视直播分类
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLiveType:edit')")
    @Log(title = "电视直播分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcProgramTvLiveType bcProgramTvLiveType) {
        return toAjax(bcProgramTvLiveTypeService.updateById(bcProgramTvLiveType));
    }

    /**
     * 删除电视直播分类
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLiveType:remove')")
    @Log(title = "电视直播分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcProgramTvLiveTypeService.removeByIds(ids));
    }
}
