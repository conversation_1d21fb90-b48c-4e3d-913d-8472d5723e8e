package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcNavigationConfig;
import com.zainanjing.zijin.service.IBcNavigationConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 导航配置Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bc/navigationConfig")
public class BcNavigationConfigController extends BaseController {
    @Autowired
    private IBcNavigationConfigService bcNavigationConfigService;

    /**
     * 查询导航配置列表
     */
    @PreAuthorize("@ss.hasPermi('bc:navigationConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcNavigationConfig> page = bcNavigationConfigService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }


    /**
     * 获取导航配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:navigationConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcNavigationConfigService.getById(id));
    }

    /**
     * 修改导航配置
     */
    @PreAuthorize("@ss.hasPermi('bc:navigationConfig:edit')")
    @Log(title = "导航配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcNavigationConfig bcNavigationConfig) {
        return toAjax(bcNavigationConfigService.updateById(bcNavigationConfig));
    }


}
