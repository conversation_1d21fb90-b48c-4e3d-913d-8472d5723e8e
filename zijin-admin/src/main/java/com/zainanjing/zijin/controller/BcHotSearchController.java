package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcHotSearch;
import com.zainanjing.zijin.service.IBcHotSearchService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 热门搜索Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bc/hotSearch")
public class BcHotSearchController extends BaseController {
    @Resource
    private IBcHotSearchService bcHotSearchService;

    /**
     * 查询热门搜索列表
     */
    @PreAuthorize("@ss.hasPermi('bc:hotSearch:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcHotSearch> page = bcHotSearchService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取热门搜索详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:hotSearch:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcHotSearchService.getById(id));
    }

    /**
     * 新增热门搜索
     */
    @PreAuthorize("@ss.hasPermi('bc:hotSearch:add')")
    @Log(title = "热门搜索", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcHotSearch bcHotSearch) {
        bcHotSearch.setSearchTime(LocalDate.now());
        return toAjax(bcHotSearchService.save(bcHotSearch));
    }


    /**
     * 删除热门搜索
     */
    @PreAuthorize("@ss.hasPermi('bc:hotSearch:remove')")
    @Log(title = "热门搜索", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcHotSearchService.removeByIds(ids));
    }
}
