package com.zainanjing.zijin.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import com.ruoyi.common.core.search.Searchable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcCommentImage;
import com.zainanjing.zijin.service.IBcCommentImageService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评论回复图片Controller
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/bc/commentImage")
public class BcCommentImageController extends BaseController
{
    @Autowired
    private IBcCommentImageService bcCommentImageService;

    /**
     * 查询评论回复图片列表
     */
    @PreAuthorize("@ss.hasPermi('bc:commentImage:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable)
    {
        IPage<BcCommentImage> page = bcCommentImageService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(),page.getTotal());
    }

    /**
     * 获取评论回复图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:commentImage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(bcCommentImageService.getById(id));
    }

    /**
     * 新增评论回复图片
     */
    @PreAuthorize("@ss.hasPermi('bc:commentImage:add')")
    @Log(title = "评论回复图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcCommentImage bcCommentImage)
    {
        return toAjax(bcCommentImageService.save(bcCommentImage));
    }

    /**
     * 修改评论回复图片
     */
    @PreAuthorize("@ss.hasPermi('bc:commentImage:edit')")
    @Log(title = "评论回复图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcCommentImage bcCommentImage)
    {
        return toAjax(bcCommentImageService.updateById(bcCommentImage));
    }

    /**
     * 删除评论回复图片
     */
    @PreAuthorize("@ss.hasPermi('bc:commentImage:remove')")
    @Log(title = "评论回复图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids)
    {
        return toAjax(bcCommentImageService.removeByIds(ids));
    }
}
