package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcForum;
import com.zainanjing.zijin.service.IBcForumService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 互动区数据Controller
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/bc/forum")
public class BcForumController extends BaseController {
    @Autowired
    private IBcForumService bcForumService;

    /**
     * 查询互动区数据列表
     */
    @PreAuthorize("@ss.hasPermi('bc:forum:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcForum> page = bcForumService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取互动区数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:forum:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcForumService.getById(id));
    }

    /**
     * 新增互动区数据
     */
    @PreAuthorize("@ss.hasPermi('bc:forum:add')")
    @Log(title = "互动区数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcForum bcForum) {
        return toAjax(bcForumService.save(bcForum));
    }

    /**
     * 修改互动区数据
     */
    @PreAuthorize("@ss.hasPermi('bc:forum:edit')")
    @Log(title = "互动区数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcForum bcForum) {
        return toAjax(bcForumService.updateById(bcForum));
    }

    /**
     * 删除互动区数据
     */
    @PreAuthorize("@ss.hasPermi('bc:forum:remove')")
    @Log(title = "互动区数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcForumService.removeByIds(ids));
    }
}
