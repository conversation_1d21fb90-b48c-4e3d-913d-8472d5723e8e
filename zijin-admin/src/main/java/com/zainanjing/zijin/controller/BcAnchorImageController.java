package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcAnchorImage;
import com.zainanjing.zijin.service.IBcAnchorImageService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主播相册Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/bc/anchorImage")
public class BcAnchorImageController extends BaseController {
    @Autowired
    private IBcAnchorImageService bcAnchorImageService;

    /**
     * 查询主播相册列表
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorImage:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcAnchorImage> page = bcAnchorImageService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取主播相册详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorImage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcAnchorImageService.getById(id));
    }

    /**
     * 新增主播相册
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorImage:add')")
    @Log(title = "主播相册", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcAnchorImage bcAnchorImage) {
        return toAjax(bcAnchorImageService.save(bcAnchorImage));
    }

    /**
     * 修改主播相册
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorImage:edit')")
    @Log(title = "主播相册", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcAnchorImage bcAnchorImage) {
        return toAjax(bcAnchorImageService.updateById(bcAnchorImage));
    }

    /**
     * 删除主播相册
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorImage:remove')")
    @Log(title = "主播相册", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcAnchorImageService.removeByIds(ids));
    }
}
