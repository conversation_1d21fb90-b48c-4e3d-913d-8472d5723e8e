package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.zijin.domain.BcVerifyConfig;
import com.zainanjing.zijin.service.IBcVerifyConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 发言审核配置Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bc/verifyConfig")
public class BcVerifyConfigController extends BaseController {
    @Autowired
    private IBcVerifyConfigService bcVerifyConfigService;

    /**
     * 查询发言审核配置列表
     */
    @PreAuthorize("@ss.hasPermi('bc:verifyConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcVerifyConfig> page = bcVerifyConfigService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }


    /**
     * 修改发言审核配置
     */
    @PreAuthorize("@ss.hasPermi('bc:verifyConfig:edit')")
    @Log(title = "发言审核配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcVerifyConfig bcVerifyConfig) {
        bcVerifyConfig.setUserId(SecurityUtils.getUserId());
        return toAjax(bcVerifyConfigService.updateVerifyConfig(bcVerifyConfig));
    }

}
