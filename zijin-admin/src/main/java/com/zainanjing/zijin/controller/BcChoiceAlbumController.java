package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.constant.ZijinConstants;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.domain.BcChoice;
import com.zainanjing.zijin.domain.BcChoiceAlbum;
import com.zainanjing.zijin.service.IBcAnchorService;
import com.zainanjing.zijin.service.IBcChoiceAlbumService;
import com.zainanjing.zijin.service.IBcChoiceService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 精选内容专辑Controller
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/bc/choiceAlbum")
public class BcChoiceAlbumController extends BaseController {
    @Autowired
    private IBcChoiceAlbumService bcChoiceAlbumService;
    @Autowired
    private IBcAnchorService bcAnchorService;
    @Autowired
    private IBcChoiceService bcChoiceService;

    /**
     * 查询精选内容专辑列表
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceAlbum:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcChoiceAlbum> page = bcChoiceAlbumService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取精选内容专辑详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceAlbum:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        BcChoiceAlbum bcChoiceAlbum = bcChoiceAlbumService.getById(id);
        //如果关联主播
        if (Integer.valueOf(ZijinConstants.CONSTANTS_1).equals(bcChoiceAlbum.getIsRefAnchor())) {
            BcAnchor bcAnchor = bcAnchorService.getOne(new QueryWrapper<BcAnchor>().eq("user_id", bcChoiceAlbum.getAnchorUid()));
            if (null != bcAnchor) {
                bcChoiceAlbum.setAnchorId(Math.toIntExact(bcAnchor.getId()));
            }
        }
        return AjaxResult.success(bcChoiceAlbum);
    }

    /**
     * 新增精选内容专辑
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceAlbum:add')")
    @Log(title = "精选内容专辑", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcChoiceAlbum bcChoiceAlbum) {
        bcChoiceAlbum.setCreateTime(new Date());
        bcChoiceAlbum.setUpdateTime(new Date());
        return toAjax(bcChoiceAlbumService.save(bcChoiceAlbum));
    }

    /**
     * 修改精选内容专辑
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceAlbum:edit')")
    @Log(title = "精选内容专辑", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcChoiceAlbum bcChoiceAlbum) {
        bcChoiceAlbum.setUpdateTime(new Date());
        //如果关联主播
        if (Integer.valueOf(ZijinConstants.CONSTANTS_1).equals(bcChoiceAlbum.getIsRefAnchor())) {
            BcAnchor bcAnchor = bcAnchorService.getById(bcChoiceAlbum.getAnchorId());
            if (null != bcAnchor) {
                bcChoiceAlbum.setAnchorUid(bcAnchor.getUserId());
            }
        }
        return toAjax(bcChoiceAlbumService.updateById(bcChoiceAlbum));
    }

    /**
     * 删除精选内容专辑
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceAlbum:remove')")
    @Log(title = "精选内容专辑", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        //删除专辑 顺便删除 专辑下的分集
        LambdaUpdateWrapper<BcChoice> bcChoiceLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        bcChoiceLambdaUpdateWrapper.in(BcChoice::getAlbumId, ids).set(BcChoice::getStatus, 2);
        bcChoiceService.update(bcChoiceLambdaUpdateWrapper);
        //专辑
        LambdaUpdateWrapper<BcChoiceAlbum> albumLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        albumLambdaUpdateWrapper.in(BcChoiceAlbum::getId, ids).set(BcChoiceAlbum::getStatus, 2);
        return toAjax(bcChoiceAlbumService.update(albumLambdaUpdateWrapper));
    }
}
