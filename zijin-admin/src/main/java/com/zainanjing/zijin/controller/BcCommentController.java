package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcComment;
import com.zainanjing.zijin.service.IBcCommentService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 评论Controller
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@RestController
@RequestMapping("/bc/comment")
public class BcCommentController extends BaseController {
    @Autowired
    private IBcCommentService bcCommentService;

    /**
     * 查询评论列表
     */
    @PreAuthorize("@ss.hasPermi('bc:comment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcComment> page = bcCommentService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:comment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcCommentService.getMoreById(id));
    }

    /**
     * 新增评论
     */
    @PreAuthorize("@ss.hasPermi('bc:comment:add')")
    @Log(title = "评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcComment bcComment) {
        return toAjax(bcCommentService.save(bcComment));
    }

    /**
     * 修改评论
     */
    @PreAuthorize("@ss.hasPermi('bc:comment:edit')")
    @Log(title = "评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcComment bcComment) {
        return toAjax(bcCommentService.updateById(bcComment));
    }

    /**
     * 删除评论
     */
    @PreAuthorize("@ss.hasPermi('bc:comment:remove')")
    @Log(title = "评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcCommentService.removeByIds(ids));
    }

    /**
     * 评论审核
     */
    @PostMapping("/audit")
    @PreAuthorize("@ss.hasPermi('bc:comment:audit')")
    public AjaxResult audit(String id, Integer status, String remark) {
        BcComment bcComment = new BcComment();
        bcComment.setId(Long.valueOf(id));
        bcComment.setStatus(status);
        bcComment.setRemark(remark);
        return toAjax(bcCommentService.updateById(bcComment));
    }
}
