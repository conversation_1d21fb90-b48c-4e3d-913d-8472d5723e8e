package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcProgramLiveType;
import com.zainanjing.zijin.service.IBcProgramLiveTypeService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 直播分类Controller
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/bc/programLiveType")
public class BcProgramLiveTypeController extends BaseController {
    @Autowired
    private IBcProgramLiveTypeService bcProgramLiveTypeService;

    /**
     * 查询直播分类列表
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveType:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcProgramLiveType> page = bcProgramLiveTypeService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取直播分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcProgramLiveTypeService.getById(id));
    }

    /**
     * 新增直播分类
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveType:add')")
    @Log(title = "直播分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcProgramLiveType bcProgramLiveType) {
        bcProgramLiveType.setCreateTime(new Date());
        return toAjax(bcProgramLiveTypeService.save(bcProgramLiveType));
    }

    /**
     * 修改直播分类
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveType:edit')")
    @Log(title = "直播分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcProgramLiveType bcProgramLiveType) {
        bcProgramLiveType.setUpdateTime(new Date());
        return toAjax(bcProgramLiveTypeService.updateById(bcProgramLiveType));
    }

    /**
     * 删除直播分类
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveType:remove')")
    @Log(title = "直播分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcProgramLiveTypeService.removeByIds(ids));
    }
}
