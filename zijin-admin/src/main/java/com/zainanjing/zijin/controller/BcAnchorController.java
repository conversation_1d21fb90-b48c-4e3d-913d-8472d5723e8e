package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcAnchor;
import com.zainanjing.zijin.service.IBcAnchorService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主播Controller
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/bc/anchor")
public class BcAnchorController extends BaseController {
    @Autowired
    private IBcAnchorService bcAnchorService;

    /**
     * 查询主播列表
     */
    @PreAuthorize("@ss.hasPermi('bc:anchor:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcAnchor> page = bcAnchorService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 查询主播列表 通用
     */
    @PreAuthorize("@ss.hasPermi('bc:anchor:list')")
    @GetMapping("/page")
    public TableDataInfo page(Searchable searchable) {
        IPage<BcAnchor> page = bcAnchorService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取主播详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:anchor:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcAnchorService.getSelectiveAnchor(id));
    }

    /**
     * 新增主播
     */
    @PreAuthorize("@ss.hasPermi('bc:anchor:add')")
    @Log(title = "主播", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcAnchor bcAnchor) {
        return toAjax(bcAnchorService.insertSelective(bcAnchor));
    }

    /**
     * 修改主播
     */
    @PreAuthorize("@ss.hasPermi('bc:anchor:edit')")
    @Log(title = "主播", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcAnchor bcAnchor) {
        return toAjax(bcAnchorService.updateSelective(bcAnchor));
    }

    /**
     * 删除主播
     */
    @PreAuthorize("@ss.hasPermi('bc:anchor:remove')")
    @Log(title = "主播", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcAnchorService.removeByIds(ids));
    }

    /**
     * 根据手机号查询gdmm_users表
     */
    @PreAuthorize("@ss.hasPermi('bc:anchor:queryByPhoneNumber')")
    @GetMapping(value = "/queryByPhoneNumber")
    public AjaxResult queryByPhoneNumber(String phoneNumber) {
        return AjaxResult.success(bcAnchorService.queryByPhoneNumber(phoneNumber));
    }
}
