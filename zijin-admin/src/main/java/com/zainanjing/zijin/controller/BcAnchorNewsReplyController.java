package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcAnchorNewsReply;
import com.zainanjing.zijin.service.IBcAnchorNewsReplyService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主播动态回复Controller
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/bc/anchorNewsReply")
public class BcAnchorNewsReplyController extends BaseController {
    @Autowired
    private IBcAnchorNewsReplyService bcAnchorNewsReplyService;

    /**
     * 查询主播动态回复列表
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNewsReply:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcAnchorNewsReply> page = bcAnchorNewsReplyService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取主播动态回复详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNewsReply:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcAnchorNewsReplyService.getMoreById(id));
    }

    /**
     * 新增主播动态回复
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNewsReply:add')")
    @Log(title = "主播动态回复", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcAnchorNewsReply bcAnchorNewsReply) {
        return toAjax(bcAnchorNewsReplyService.backSave(bcAnchorNewsReply));
    }

    /**
     * 修改主播动态回复
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNewsReply:edit')")
    @Log(title = "主播动态回复", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcAnchorNewsReply bcAnchorNewsReply) {
        return toAjax(bcAnchorNewsReplyService.updateById(bcAnchorNewsReply));
    }

    /**
     * 批量删除
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNewsReply:remove')")
    @Log(title = "主播动态回复", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcAnchorNewsReplyService.removeByIds(ids));
    }

    /**
     * 批量审核
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNewsReply:batchReview')")
    @GetMapping("/batchReview")
    public AjaxResult batchReview(@RequestParam("ids") String ids, @RequestParam("status") Integer status) {
        return toAjax(bcAnchorNewsReplyService.batchReview(ids, status));
    }
}
