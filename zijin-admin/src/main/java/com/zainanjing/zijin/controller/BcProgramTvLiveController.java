package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcProgramTvLive;
import com.zainanjing.zijin.service.IBcProgramTvLiveService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电视视频节目Controller
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/bc/programTvLive")
public class BcProgramTvLiveController extends BaseController {
    @Autowired
    private IBcProgramTvLiveService bcProgramTvLiveService;

    /**
     * 查询电视视频节目列表
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLive:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcProgramTvLive> page = bcProgramTvLiveService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取电视视频节目详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLive:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcProgramTvLiveService.getById(id));
    }

    /**
     * 新增电视视频节目
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLive:add')")
    @Log(title = "电视视频节目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcProgramTvLive bcProgramTvLive) {
        return toAjax(bcProgramTvLiveService.save(bcProgramTvLive));
    }

    /**
     * 修改电视视频节目
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLive:edit')")
    @Log(title = "电视视频节目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcProgramTvLive bcProgramTvLive) {
        return toAjax(bcProgramTvLiveService.updateById(bcProgramTvLive));
    }

    /**
     * 删除电视视频节目
     */
    @PreAuthorize("@ss.hasPermi('bc:programTvLive:remove')")
    @Log(title = "电视视频节目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcProgramTvLiveService.removeByIds(ids));
    }
}
