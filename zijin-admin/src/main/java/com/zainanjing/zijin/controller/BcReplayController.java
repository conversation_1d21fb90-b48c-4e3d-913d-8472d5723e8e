package com.zainanjing.zijin.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import com.ruoyi.common.core.search.Searchable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcReplay;
import com.zainanjing.zijin.service.IBcReplayService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 点播Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/bc/replay")
public class BcReplayController extends BaseController
{
    @Autowired
    private IBcReplayService bcReplayService;

    /**
     * 查询点播列表
     */
    @PreAuthorize("@ss.hasPermi('bc:replay:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable)
    {
        IPage<BcReplay> page = bcReplayService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(),page.getTotal());
    }

    /**
     * 获取点播详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:replay:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(bcReplayService.getById(id));
    }

    /**
     * 新增点播
     */
    @PreAuthorize("@ss.hasPermi('bc:replay:add')")
    @Log(title = "点播", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcReplay bcReplay)
    {
        return toAjax(bcReplayService.save(bcReplay));
    }

    /**
     * 修改点播
     */
    @PreAuthorize("@ss.hasPermi('bc:replay:edit')")
    @Log(title = "点播", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcReplay bcReplay)
    {
        return toAjax(bcReplayService.updateById(bcReplay));
    }

    /**
     * 删除点播
     */
    @PreAuthorize("@ss.hasPermi('bc:replay:remove')")
    @Log(title = "点播", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids)
    {
        return toAjax(bcReplayService.removeByIds(ids));
    }
}
