package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcComment;
import com.zainanjing.zijin.domain.BcPost;
import com.zainanjing.zijin.service.IBcCommentService;
import com.zainanjing.zijin.service.IBcPostService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 帖子数据Controller
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/bc/post")
public class BcPostController extends BaseController {
    @Autowired
    private IBcPostService bcPostService;
    @Autowired
    private IBcCommentService bcCommentService;

    /**
     * 查询帖子数据列表
     */
    @PreAuthorize("@ss.hasPermi('bc:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcPost> page = bcPostService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取帖子数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:post:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcPostService.getMoreById(id));
    }

    /**
     * 新增帖子数据
     */
    @PreAuthorize("@ss.hasPermi('bc:post:add')")
    @Log(title = "帖子数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcPost bcPost) {
        return toAjax(bcPostService.saveMore(bcPost));
    }

    /**
     * 修改帖子数据
     */
    @PreAuthorize("@ss.hasPermi('bc:post:edit')")
    @Log(title = "帖子数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcPost bcPost) {
        return toAjax(bcPostService.updateMore(bcPost));
    }

    /**
     * 删除帖子数据
     */
    @PreAuthorize("@ss.hasPermi('bc:post:remove')")
    @Log(title = "帖子数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcPostService.removeByIds(ids));
    }

    /**
     * 帖子审核
     */
    @PreAuthorize("@ss.hasPermi('bc:post:audit')")
    @PostMapping("/audit")
    public AjaxResult audit(String id, Integer status, String remark) {
        BcPost bcPost = new BcPost();
        bcPost.setId(Long.valueOf(id));
        bcPost.setStatus(status);
        bcPost.setRemark(remark);
        bcPost.setAuditTime(new Date());
        return toAjax(bcPostService.updateById(bcPost));
    }

    /**
     * 推荐帖子
     */
    @GetMapping("/recommend/{ids}")
    public AjaxResult recommend(@PathVariable("ids") List<Long> ids, @RequestParam("type") Integer type) {
        return toAjax(bcPostService.recommend(ids, type));
    }

    /**
     * 回复帖子评论
     */
    @PostMapping("/addComment")
    public AjaxResult addComment(@RequestBody BcComment bcComment) {
        return toAjax(bcCommentService.saveMore(bcComment));
    }
}
