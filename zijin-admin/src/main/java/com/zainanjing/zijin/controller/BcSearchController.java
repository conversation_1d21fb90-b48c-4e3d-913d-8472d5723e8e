package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcSearch;
import com.zainanjing.zijin.service.IBcSearchService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 搜索记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bc/search")
public class BcSearchController extends BaseController {
    @Autowired
    private IBcSearchService bcSearchService;

    /**
     * 查询搜索记录列表
     */
    @PreAuthorize("@ss.hasPermi('bc:search:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcSearch> page = bcSearchService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取搜索记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:search:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcSearchService.getById(id));
    }

    /**
     * 新增搜索记录
     */
    @PreAuthorize("@ss.hasPermi('bc:search:add')")
    @Log(title = "搜索记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcSearch bcSearch) {
        return toAjax(bcSearchService.save(bcSearch));
    }

    /**
     * 修改搜索记录
     */
    @PreAuthorize("@ss.hasPermi('bc:search:edit')")
    @Log(title = "搜索记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcSearch bcSearch) {
        return toAjax(bcSearchService.updateById(bcSearch));
    }

    /**
     * 删除搜索记录
     */
    @PreAuthorize("@ss.hasPermi('bc:search:remove')")
    @Log(title = "搜索记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcSearchService.removeByIds(ids));
    }
}
