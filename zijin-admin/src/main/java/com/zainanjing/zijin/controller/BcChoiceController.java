package com.zainanjing.zijin.controller;

import com.aliyun.oss.model.SortOrder;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.Sort;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcChoice;
import com.zainanjing.zijin.service.IBcChoiceService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 精选内容分集Controller
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/bc/choice")
public class BcChoiceController extends BaseController {
    @Autowired
    private IBcChoiceService bcChoiceService;

    /**
     * 查询精选内容分集列表
     */
    @PreAuthorize("@ss.hasPermi('bc:choice:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        searchable.removeSort();
        searchable.addSearchParam("status", 1);
        searchable.addSort(Sort.Direction.DESC, "sort");
        IPage<BcChoice> page = bcChoiceService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取精选内容分集详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:choice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcChoiceService.getById(id));
    }

    /**
     * 新增精选内容分集
     */
    @PreAuthorize("@ss.hasPermi('bc:choice:add')")
    @Log(title = "精选内容分集", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcChoice bcChoice) {
        bcChoice.setCreateTime(new Date());
        bcChoice.setUpdateTime(new Date());
        Optional.ofNullable(bcChoice.getShareDesc())
                .ifPresentOrElse(
                        bcChoice::setShareDesc,
                        () -> bcChoice.setShareDesc("")
                );
        Optional.ofNullable(bcChoice.getShareTitle())
                .ifPresentOrElse(
                        bcChoice::setShareTitle,
                        () -> bcChoice.setShareTitle("")
                );
        return toAjax(bcChoiceService.save(bcChoice));
    }

    /**
     * 修改精选内容分集
     */
    @PreAuthorize("@ss.hasPermi('bc:choice:edit')")
    @Log(title = "精选内容分集", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcChoice bcChoice) {
        bcChoice.setUpdateTime(new Date());
        return toAjax(bcChoiceService.updateById(bcChoice));
    }

    /**
     * 删除精选内容分集
     */
    @PreAuthorize("@ss.hasPermi('bc:choice:remove')")
    @Log(title = "精选内容分集", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        LambdaUpdateWrapper<BcChoice> bcChoiceLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        bcChoiceLambdaUpdateWrapper.in(BcChoice::getId, ids).set(BcChoice::getStatus, 2);
        return toAjax(bcChoiceService.update(bcChoiceLambdaUpdateWrapper));
    }
}
