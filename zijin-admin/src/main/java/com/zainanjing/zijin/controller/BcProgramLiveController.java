package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcProgramLive;
import com.zainanjing.zijin.service.IBcProgramLiveService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视频节目Controller
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/bc/programLive")
public class BcProgramLiveController extends BaseController {
    @Autowired
    private IBcProgramLiveService bcProgramLiveService;

    /**
     * 查询视频节目列表
     */
    @PreAuthorize("@ss.hasPermi('bc:programLive:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcProgramLive> page = bcProgramLiveService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取视频节目详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:programLive:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcProgramLiveService.getById(id));
    }

    /**
     * 新增视频节目
     */
    @PreAuthorize("@ss.hasPermi('bc:programLive:add')")
    @Log(title = "视频节目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcProgramLive bcProgramLive) {
        return toAjax(bcProgramLiveService.saveAndEvent(bcProgramLive));
    }

    /**
     * 修改视频节目
     */
    @PreAuthorize("@ss.hasPermi('bc:programLive:edit')")
    @Log(title = "视频节目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcProgramLive bcProgramLive) {
        return toAjax(bcProgramLiveService.updateByIdAndEvent(bcProgramLive));
    }

    /**
     * 删除视频节目
     */
    @PreAuthorize("@ss.hasPermi('bc:programLive:remove')")
    @Log(title = "视频节目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcProgramLiveService.removeByIdsAndEvent(ids));
    }

    /**
     * 腾讯云直播回调
     */
    @GetMapping(value = "/tencentCloud/back")
    public AjaxResult tencentCloudCallBack(@RequestParam("streamId") String streamId, @RequestParam("videoUrl") String videoUrl) {
        UpdateWrapper<BcProgramLive> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("stream_id", streamId);
        updateWrapper.set("tecent_video_url", videoUrl);
        //将直播状态改成否
        updateWrapper.set("is_live", 0);
        return AjaxResult.success(bcProgramLiveService.update(updateWrapper));
    }
}
