package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.zijin.domain.BcChoiceComment;
import com.zainanjing.zijin.domain.BcComment;
import com.zainanjing.zijin.service.IBcChoiceCommentService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 精选内容评论Controller
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/bc/choiceComment")
public class BcChoiceCommentController extends BaseController {
    @Autowired
    private IBcChoiceCommentService bcChoiceCommentService;

    /**
     * 查询精选内容评论列表
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceComment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcChoiceComment> page = bcChoiceCommentService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取精选内容评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceComment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcChoiceCommentService.getMoreById(id));
    }

    /**
     * 新增精选内容评论
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceComment:add')")
    @Log(title = "精选内容评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcChoiceComment bcChoiceComment) {
        return toAjax(bcChoiceCommentService.saveMore(bcChoiceComment));
    }

    /**
     * 修改精选内容评论
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceComment:edit')")
    @Log(title = "精选内容评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcChoiceComment bcChoiceComment) {
        return toAjax(bcChoiceCommentService.updateById(bcChoiceComment));
    }

    /**
     * 删除精选内容评论
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceComment:remove')")
    @Log(title = "精选内容评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcChoiceCommentService.removeByIds(ids));
    }

    /**
     * 批量审核
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceComment:batchReview')")
    @GetMapping("/batchReview")
    public AjaxResult batchReview(@RequestParam("ids") String ids, @RequestParam("status") Integer status) {
        return toAjax(bcChoiceCommentService.batchReview(ids, status));
    }

    @PreAuthorize("@ss.hasPermi('bc:choiceComment:audit')")
    @PostMapping("/audit")
    public AjaxResult audit(String id, Integer status,String remark) {
        BcChoiceComment bcComment = new BcChoiceComment();
        bcComment.setId(Long.valueOf(id));
        bcComment.setStatus(status);
        bcComment.setRemark(remark);
        return toAjax(bcChoiceCommentService.updateById(bcComment));
    }
}
