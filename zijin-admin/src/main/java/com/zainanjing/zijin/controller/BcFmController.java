package com.zainanjing.zijin.controller;

import com.ruoyi.common.utils.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcFm;
import com.zainanjing.zijin.service.IBcFmService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 频率信息Controller
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/bc/fm")
public class BcFmController extends BaseController {
    @Autowired
    private IBcFmService bcFmService;

    /**
     * 查询频率信息列表
     */
    @PreAuthorize("@ss.hasPermi('bc:fm:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcFm> page = bcFmService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取频率信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:fm:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcFmService.getById(id));
    }

    /**
     * 新增频率信息
     */
    @PreAuthorize("@ss.hasPermi('bc:fm:add')")
    @Log(title = "频率信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcFm bcFm) {
        bcFm.setCreateTime(new Date());
        return toAjax(bcFmService.save(bcFm));
    }

    /**
     * 修改频率信息
     */
    @PreAuthorize("@ss.hasPermi('bc:fm:edit')")
    @Log(title = "频率信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcFm bcFm) {
        bcFm.setUpdateTime(new Date());
        return toAjax(bcFmService.updateById(bcFm));
    }

    /**
     * 删除频率信息
     */
    @PreAuthorize("@ss.hasPermi('bc:fm:remove')")
    @Log(title = "频率信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcFmService.removeByIds(ids));
    }
}
