package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcAnchorVideo;
import com.zainanjing.zijin.service.IBcAnchorVideoService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主播视频Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/bc/anchorVideo")
public class BcAnchorVideoController extends BaseController {
    @Autowired
    private IBcAnchorVideoService bcAnchorVideoService;

    /**
     * 查询主播视频列表
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorVideo:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcAnchorVideo> page = bcAnchorVideoService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取主播视频详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorVideo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcAnchorVideoService.getById(id));
    }

    /**
     * 新增主播视频
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorVideo:add')")
    @Log(title = "主播视频", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcAnchorVideo bcAnchorVideo) {
        return toAjax(bcAnchorVideoService.save(bcAnchorVideo));
    }

    /**
     * 修改主播视频
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorVideo:edit')")
    @Log(title = "主播视频", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcAnchorVideo bcAnchorVideo) {
        return toAjax(bcAnchorVideoService.updateById(bcAnchorVideo));
    }

    /**
     * 删除主播视频
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorVideo:remove')")
    @Log(title = "主播视频", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcAnchorVideoService.removeByIds(ids));
    }
}
