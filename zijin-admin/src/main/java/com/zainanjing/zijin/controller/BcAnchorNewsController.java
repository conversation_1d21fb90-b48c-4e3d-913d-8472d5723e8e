package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcAnchorNews;
import com.zainanjing.zijin.service.IBcAnchorNewsService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主播动态Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bc/anchorNews")
public class BcAnchorNewsController extends BaseController {
    @Autowired
    private IBcAnchorNewsService bcAnchorNewsService;

    /**
     * 查询主播动态列表
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNews:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcAnchorNews> page = bcAnchorNewsService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取主播动态详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNews:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {

        return AjaxResult.success(bcAnchorNewsService.getMoreById(id));
    }

    /**
     * 新增主播动态
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNews:add')")
    @Log(title = "主播动态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcAnchorNews bcAnchorNews) {
        return toAjax(bcAnchorNewsService.saveAggregateQuery(bcAnchorNews));
    }

    /**
     * 修改主播动态
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNews:edit')")
    @Log(title = "主播动态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcAnchorNews bcAnchorNews) {
        return toAjax(bcAnchorNewsService.updateMoreById(bcAnchorNews));
    }

    /**
     * 删除主播动态
     */
    @PreAuthorize("@ss.hasPermi('bc:anchorNews:remove')")
    @Log(title = "主播动态", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcAnchorNewsService.removeByIds(ids));
    }
}
