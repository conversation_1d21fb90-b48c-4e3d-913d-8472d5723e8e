package com.zainanjing.zijin.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import com.ruoyi.common.core.search.Searchable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcChoiceCommentImg;
import com.zainanjing.zijin.service.IBcChoiceCommentImgService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 精选内容评论图片Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/bc/choiceCommentImg")
public class BcChoiceCommentImgController extends BaseController
{
    @Autowired
    private IBcChoiceCommentImgService bcChoiceCommentImgService;

    /**
     * 查询精选内容评论图片列表
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCommentImg:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable)
    {
        IPage<BcChoiceCommentImg> page = bcChoiceCommentImgService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(),page.getTotal());
    }

    /**
     * 获取精选内容评论图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCommentImg:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(bcChoiceCommentImgService.getById(id));
    }

    /**
     * 新增精选内容评论图片
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCommentImg:add')")
    @Log(title = "精选内容评论图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcChoiceCommentImg bcChoiceCommentImg)
    {
        return toAjax(bcChoiceCommentImgService.save(bcChoiceCommentImg));
    }

    /**
     * 修改精选内容评论图片
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCommentImg:edit')")
    @Log(title = "精选内容评论图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcChoiceCommentImg bcChoiceCommentImg)
    {
        return toAjax(bcChoiceCommentImgService.updateById(bcChoiceCommentImg));
    }

    /**
     * 删除精选内容评论图片
     */
    @PreAuthorize("@ss.hasPermi('bc:choiceCommentImg:remove')")
    @Log(title = "精选内容评论图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids)
    {
        return toAjax(bcChoiceCommentImgService.removeByIds(ids));
    }
}
