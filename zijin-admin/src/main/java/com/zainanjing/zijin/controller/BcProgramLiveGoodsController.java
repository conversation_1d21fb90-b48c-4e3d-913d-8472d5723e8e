package com.zainanjing.zijin.controller;

import com.ruoyi.common.utils.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcProgramLiveGoods;
import com.zainanjing.zijin.service.IBcProgramLiveGoodsService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 直播爆品管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/bc/programLiveGoods")
public class BcProgramLiveGoodsController extends BaseController {
    @Autowired
    private IBcProgramLiveGoodsService bcProgramLiveGoodsService;

    /**
     * 查询直播爆品管理列表
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveGoods:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcProgramLiveGoods> page = bcProgramLiveGoodsService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取直播爆品管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveGoods:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bcProgramLiveGoodsService.getById(id));
    }

    /**
     * 新增直播爆品管理
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveGoods:add')")
    @Log(title = "直播爆品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcProgramLiveGoods bcProgramLiveGoods) {
        bcProgramLiveGoods.setCreateTime(new Date());
        return toAjax(bcProgramLiveGoodsService.save(bcProgramLiveGoods));
    }

    /**
     * 修改直播爆品管理
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveGoods:edit')")
    @Log(title = "直播爆品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcProgramLiveGoods bcProgramLiveGoods) {
        bcProgramLiveGoods.setUpdateTime(new Date());
        return toAjax(bcProgramLiveGoodsService.updateById(bcProgramLiveGoods));
    }

    /**
     * 修改讲解
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveGoods:edit')")
    @Log(title = "直播爆品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateExplain")
    public AjaxResult updateExplain(@RequestBody BcProgramLiveGoods bcProgramLiveGoods) {
        return toAjax(bcProgramLiveGoodsService.updateExplain(bcProgramLiveGoods));
    }

    /**
     * 删除直播爆品管理
     */
    @PreAuthorize("@ss.hasPermi('bc:programLiveGoods:remove')")
    @Log(title = "直播爆品管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(bcProgramLiveGoodsService.lambdaUpdate().set(BcProgramLiveGoods::getIsDel, "1").in(BcProgramLiveGoods::getId, ids).update());
    }
}
