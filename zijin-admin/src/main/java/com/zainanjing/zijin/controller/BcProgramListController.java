package com.zainanjing.zijin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.zainanjing.zijin.domain.BcProgramList;
import com.zainanjing.zijin.service.IBcProgramListService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播节目单Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/bc/programList")
public class BcProgramListController extends BaseController {
    @Autowired
    private IBcProgramListService bcProgramListService;

    /**
     * 查询直播节目单列表
     */
    @PreAuthorize("@ss.hasPermi('bc:programList:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<BcProgramList> page = bcProgramListService.findAggregateQuery(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取直播节目单详细信息
     */
    @PreAuthorize("@ss.hasPermi('bc:programList:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(bcProgramListService.getById(id));
    }

    /**
     * 新增直播节目单
     */
    @PreAuthorize("@ss.hasPermi('bc:programList:add')")
    @Log(title = "直播节目单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BcProgramList bcProgramList) {
        return toAjax(bcProgramListService.save(bcProgramList));
    }

    /**
     * 修改直播节目单
     */
    @PreAuthorize("@ss.hasPermi('bc:programList:edit')")
    @Log(title = "直播节目单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BcProgramList bcProgramList) {
        return toAjax(bcProgramListService.updateById(bcProgramList));
    }

    /**
     * 删除直播节目单
     */
    @PreAuthorize("@ss.hasPermi('bc:programList:remove')")
    @Log(title = "直播节目单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Integer> ids) {
        return toAjax(bcProgramListService.removeByIds(ids));
    }

    /**
     * 根据频率id获取节目列表
     */
    @GetMapping(value = "/queryListByFmId/{fmId}")
    public AjaxResult queryListByFmId(@PathVariable("fmId") Integer fmId) {
        return AjaxResult.success(bcProgramListService.queryListByFmId(fmId));
    }
}
