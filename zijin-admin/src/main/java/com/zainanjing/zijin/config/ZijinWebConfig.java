package com.zainanjing.zijin.config;

import com.ruoyi.common.config.SwitchDSInterceptor;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 配置拦截器
 *
 * <AUTHOR>
 */
@Configuration
public class ZijinWebConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        //C端拦截
        registry.addInterceptor(zijinSwitchDS()).addPathPatterns("/bc/**");

    }

    @Bean("zijinSwitchDS")
    public SwitchDSInterceptor zijinSwitchDS() {
        return new SwitchDSInterceptor(DataSourceType.SLAVE);
    }
}
