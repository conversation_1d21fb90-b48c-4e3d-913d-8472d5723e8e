package com.zainanjing.anchor.support;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class Constant {

    /**
     * 分类：供求信息、交通提示、声音日志
     */
    public static final String CATEGORY_SUPPLY = "supply";
    public static final String CATEGORY_TRAFFIC = "traffic";
    public static final String CATEGORY_VOICE = "voice";

    public static final Set<String> CATEGORY_SET = new HashSet<>(Arrays.asList(CATEGORY_SUPPLY, CATEGORY_TRAFFIC, CATEGORY_VOICE));


}
