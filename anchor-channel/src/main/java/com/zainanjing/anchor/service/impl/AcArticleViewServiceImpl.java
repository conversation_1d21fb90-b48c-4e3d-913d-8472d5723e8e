package com.zainanjing.anchor.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.anchor.domain.AcArticle;
import com.zainanjing.anchor.domain.AcArticleView;
import com.zainanjing.anchor.mapper.AcArticleMapper;
import com.zainanjing.anchor.mapper.AcArticleViewMapper;
import com.zainanjing.anchor.service.IAcArticleViewService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

/**
 * 阅读Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Service
public class AcArticleViewServiceImpl extends GenericCurdServiceImpl<AcArticleViewMapper, AcArticleView>
        implements IAcArticleViewService {

    @Resource
    private AcArticleMapper acArticleMapper;

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void updateView(AcArticleView acArticleView) {
        if (this.save(acArticleView)) {
            UpdateWrapper<AcArticle> updateWrapper = new UpdateWrapper<>();
            updateWrapper.setSql("viewed=viewed+1")
                    .eq("id", acArticleView.getArticleId());
            acArticleMapper.update(updateWrapper);
        }
    }

}
