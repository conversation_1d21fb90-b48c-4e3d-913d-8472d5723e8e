package com.zainanjing.anchor.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 主播号内容对象 ac_article
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Data
@TableName(value = "ac_article")
public class AcArticle implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId
    private Long id;

    private Long anchorId;

    /**
     * 分类
     */
    private String categoryCode;

    /**
     * 管理员id 关联管理员表id
     */
    private String uid;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 视频/音频时间
     */
    private Long duration;

    /**
     * 查看次数
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Long viewed;

    /**
     * 背景图片
     */
    private String bgImgUrl;

    /**
     * 是否推荐1为推荐，默认为0
     */
    private Integer isRecommend;

    /**
     * 置顶：
     * 0,否
     * 1,是
     */
    private Integer isTop;

    /**
     * 排序
     */
    @JsonIgnore
    private Long sortNum;

    /**
     * 0:显示（审核通过）1:删除 2:草稿 3:待审核 4:审核拒绝
     */
    private Integer status;

    /**
     * 是否删除，1:删除
     */
    @JsonIgnore
    @TableLogic
    private Integer delFlag;

    /**
     * 创建者
     */
    @JsonIgnore
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者
     */
    @JsonIgnore
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;

    @TableField(exist = false)
    private AcAnchor acAnchor;

    /**
     * 是否点赞
     */
    @TableField(exist = false)
    private Boolean isLike;

    /**
     * 分享地址
     */
    @TableField(exist = false)
    private String shareUrl;

}
