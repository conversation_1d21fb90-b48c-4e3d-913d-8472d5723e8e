package com.zainanjing.anchor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 点赞对象 ac_article_like
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Data
@TableName(value = "ac_article_like")
public class AcArticleLike implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * C端用户ID
     */
    private String uid;

    /**
     * 新闻资讯ID
     */
    @NotNull(message = "资讯ID不能为空")
    private Long articleId;

    /**
     * 收藏时间
     */
    private Date createTime;

}
