package com.zainanjing.anchor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 主播号对象 ac_anchor
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Data
@TableName(value = "ac_anchor")
public class AcAnchor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 主播图片
     */
    private String logoImage;

    /**
     * 背景图片
     */
    private String bgImage;

    /**
     * 所属频率
     */
    private String description;

    /**
     * 排序
     */
    private Long sortNum;

    /**
     * 管理员id 关联管理员表id
     */
    private Long uid;

    /**
     * 节目ID
     */
    private String programCode;


    /**
     * 1:启用;0停用
     */
    private Integer status;

    /**
     * 是否删除，1:删除
     */
    @JsonIgnore
    @TableLogic
    private Integer delFlag;

    /**
     * 创建者
     */
    @JsonIgnore
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    @JsonIgnore
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;

}
