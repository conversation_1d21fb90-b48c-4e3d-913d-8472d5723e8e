<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zainanjing</groupId>
        <artifactId>news-parent</artifactId>
        <version>1.2.0</version>
    </parent>
    <groupId>com.zainanjing</groupId>
    <artifactId>site-admin</artifactId>
    <name>site-admin</name>
    <description>site-admin</description>
    <dependencies>
        <!-- 订阅号 -->
        <dependency>
            <groupId>com.zainanjing</groupId>
            <artifactId>official-admin</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.zainanjing</groupId>-->
<!--            <artifactId>living-circle-admin</artifactId>-->
<!--            <version>${project.parent.version}</version>-->
<!--        </dependency>-->
        <!-- 新闻社区 -->
        <dependency>
            <groupId>com.zainanjing</groupId>
            <artifactId>article-admin</artifactId>
        </dependency>
        <!-- 紫金管理 -->
        <dependency>
            <groupId>com.zainanjing</groupId>
            <artifactId>zijin-admin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>
    <build>
        <finalName>hefeibm-admin</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>3.1.3</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.3</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <excludeGroupIds>org.projectlombok</excludeGroupIds>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>upload-to-test</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>scp</executable>
                            <arguments>
                                <argument>${project.build.directory}/${project.build.finalName}.jar</argument>
                                <argument>root@192.168.2.107:/usr/local/instance/hefei/</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>execute-test</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>ssh</executable>
                            <arguments>
                                <argument>root@192.168.2.107</argument>
                                <argument>cd /usr/local/instance/hefei/; ./admin.sh</argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
