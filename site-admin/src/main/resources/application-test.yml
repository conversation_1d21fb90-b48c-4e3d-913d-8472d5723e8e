#测试环境配置
spring:
  datasource:
    druid:
      # 主库数据源
      master:
        url: **********************************_${system.city}?useUnicode=true&characterEncoding=utf-8&useSSL=false
        username: bmuser
        password: bmuserA789F
      gdmm:
        enabled: true
        url: ************************************_${system.city}?useUnicode=true&characterEncoding=utf-8&useSSL=false
        username: bmuser
        password: bmuserA789F
  data:
    redis:
      database: 1
      host: *************
      port: 6379
      password: K4UMDap0
      timeout: 30000
  activemq:
    broker-url: tcp://*************:61616
    user: admin
    password: F0+lb4E1
  #配置虚拟目录
  elasticsearch:
    rest:
      uris: *************:9200
      password: 6jDhjr^GQGqv
      username: elastic
  servlet:
    multipart:
      location: /Users/<USER>/Desktop/temp

server:
  port: 9085
  servlet:
    context-path: /${system.city}/admin-api

system:
  push:
    is-production: false
  goods:
    url: https://wapinter${system.city}t.zainanjing365.com/siteapp/gdmm/invoke
  jwt-login:
    url: https://imagecensoringt.zainanjing365.com/tool/jwt/jiemi
    name: convenience-admin
    appid: n30fvfp2hx4nqnxj2mgplzujckompq0b
    key: 5OSr108V3Fozaoz6GZPP7vvLpcnNmjpT
  schedule:
    url: https://oriental${system.city}t.zainanjing365.com
    key: 3jzkn6ae2lpgfx52bqvj6r2cwsrsnyae
  im-url: https://im.njgdmm.com/${system.city}

push:
  production: false
  cMasterSecret: a8d3548822a1f146bbf6a0b2 #城市配置
  cAppKey: 3fc7bd38231bbae63df250c1 #城市配置

ali-oss:
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  access-key-id: LTAI5tBGfaG9WiDNUu2dkB52
  access-key-secret: ******************************
  bucket-name: zainanjingshoptest
  url: https://img.shoptest.zainanjing365.com/
