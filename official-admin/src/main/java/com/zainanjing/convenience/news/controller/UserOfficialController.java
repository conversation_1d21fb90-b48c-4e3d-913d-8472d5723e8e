package com.zainanjing.convenience.news.controller;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import com.zainanjing.official.domain.OfficialAccount;
import com.zainanjing.official.domain.UserOfficial;
import com.zainanjing.official.service.IOfficialAccountService;
import com.zainanjing.official.service.IUserOfficialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订阅号用户权限Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/news/officialUser")
public class UserOfficialController extends BaseController {
    @Autowired
    private IUserOfficialService userOfficialService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysRoleService iSysRoleService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private IOfficialAccountService iOfficialAccountService;


    /**
     * 查询订阅号用户权限列表
     */
    @GetMapping("/myOfficials")
    public AjaxResult myOfficials() {
        LambdaQueryWrapper<UserOfficial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserOfficial::getSysUserId, SecurityUtils.getUserId());
        List<UserOfficial> userOfficials = userOfficialService.list(wrapper);
        if (!userOfficials.isEmpty()) {
            LambdaQueryWrapper<OfficialAccount> ew = new LambdaQueryWrapper<>();
            ew.in(OfficialAccount::getId, userOfficials.stream().map(x -> x.getOfficialAccountId()).collect(Collectors.toList()));
            return AjaxResult.success(iOfficialAccountService.list(ew));
        }
        return AjaxResult.success(Collections.emptyList());
    }

    /**
     * 查询订阅号用户权限列表
     */
//    @GetMapping("/getOfficialRoles")
//    public AjaxResult getOfficialRoles(@RequestParam String name)
//    {
//        SysUser sysUser = iSysUserService.selectUserByUserName(name);
//        if(null == sysUser){
//            return AjaxResult.error(-1,"用户不存在");
//        }
//
//        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<SysRole>();
//        wrapper.in(SysRole::getRoleKey,"officialManger","publisher","approver");
//        List<SysRole> roles = iSysRoleService.list(wrapper);
//
//        iSysUserService.selectUserRoleGroup()
//
//        if(CollectionUtils.isNotEmpty(userOfficials))
//            return  AjaxResult.error(-1,"已添加该用户");
//
//        return AjaxResult.success(Collections.emptyList());
//    }

    /**
     * 查询订阅号用户权限列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('news:officialUser:list')")
    public TableDataInfo list(Searchable searchable) {
        List<UserOfficial> userOfficials = userOfficialService.findListBy(searchable);
        if (null != userOfficials && !userOfficials.isEmpty()) {
            // 获取用户相关信息
            List<SysUser> sysUsers = iSysUserService.listByIds(userOfficials.stream().map(UserOfficial::getSysUserId).collect(Collectors.toSet()));

            //查询用户和角色关系
            LambdaQueryWrapper<SysUserRole> sysUserRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysUserRoleLambdaQueryWrapper.in(SysUserRole::getUserId, sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList()));
            List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(sysUserRoleLambdaQueryWrapper);

            //获取角色描述信息
            LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<SysRole>();
            wrapper.in(SysRole::getRoleKey, "officialManger", "publisher", "approver");
            List<SysRole> roles = iSysRoleService.list(wrapper);

            List<Long> deleteUserOfficials = new ArrayList<>();
            //数据组装逻辑处理
            for (UserOfficial userOfficial : userOfficials) {
                Optional<SysUser> first = sysUsers.stream().filter(u -> u.getUserId().equals(userOfficial.getSysUserId())).findFirst();
                if (first.isPresent()) {
                    userOfficial.setUserName(first.get().getUserName());
                    userOfficial.setPhonenumber(first.get().getPhonenumber());
                    userOfficial.setNickName(first.get().getNickName());
                    List<Long> roleIds = sysUserRoles.stream().filter(x -> x.getUserId().equals(userOfficial.getSysUserId())).map(SysUserRole::getRoleId).collect(Collectors.toList());
                    if (!roleIds.isEmpty()) {
                        userOfficial.setRoleIds(roles.stream().filter(x -> roleIds.contains(x.getRoleId())).map(SysRole::getRoleId).collect(Collectors.toList()));
                        List<String> roleNames = roles.stream().filter(x -> roleIds.contains(x.getRoleId())).map(SysRole::getRoleName).collect(Collectors.toList());
                        userOfficial.setRoleNames(roleNames);
                    }
                } else {
                    deleteUserOfficials.add(userOfficial.getId());
                }
            }

            if (!deleteUserOfficials.isEmpty()) {
                userOfficialService.removeByIds(deleteUserOfficials);
            }
        }
        return new TableDataInfo(userOfficials, userOfficials.size());
    }

    /**
     * 获取订阅号用户权限详细信息
     */
    @PreAuthorize("@ss.hasPermi('news:officialUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(userOfficialService.getById(id));
    }

    /**
     * 新增订阅号用户权限
     */
    @PreAuthorize("@ss.hasPermi('news:officialUser:add')")
    @Log(title = "订阅号用户权限", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserOfficial userOfficial) {
        //数据校验用户账号是否有效
        SysUser sysUser = iSysUserService.selectUserByUserName(userOfficial.getUserName());
        if (null == sysUser) {
            return AjaxResult.error(-1, "用户不存在");
        }
        userOfficial.setSysUserId(sysUser.getUserId());
        // 判断是否重复
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("sys_user_id", sysUser.getUserId());
        queryMap.put("official_account_id", userOfficial.getOfficialAccountId());
        List<UserOfficial> userOfficials = userOfficialService.listByMap(queryMap);

        if (CollUtil.isNotEmpty(userOfficials))
            return AjaxResult.error(-1, "已添加该用户");

        //处理角色信息
//        List<SysUserRole> sysUserRoles = new ArrayList<>();
//        for (Long roleId : userOfficial.getRoleIds()){
//            SysUserRole sysUserRole = new SysUserRole();
//            sysUserRole.setUserId(sysUser.getUserId());
//            sysUserRole.setRoleId(roleId);
//            sysUserRoles.add(sysUserRole);
//        }
//        sysUserRoleMapper.batchUserRole(sysUserRoles);
//        userOfficial.setSysUserId(sysUser.getUserId());

        //
        return toAjax(userOfficialService.save(userOfficial));
    }

    /**
     * 修改订阅号用户权限
     */
    @PreAuthorize("@ss.hasPermi('news:officialUser:edit')")
    @Log(title = "订阅号用户权限", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserOfficial userOfficial) {
        //处理角色信息
//        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<SysRole>();
//        wrapper.in(SysRole::getRoleKey,"officialManger","publisher","approver");
//        List<SysRole> roles = iSysRoleService.list(wrapper);
//        LambdaUpdateWrapper<SysUserRole> ew = new LambdaUpdateWrapper();
//        ew.eq(SysUserRole::getUserId,userOfficial.getSysUserId());
//        ew.in(SysUserRole::getRoleId,roles.stream().map(x->x.getRoleId()).collect(Collectors.toList()));
//        sysUserRoleMapper.delete(ew);
//
//        List<SysUserRole> sysUserRoles = new ArrayList<>();
//        for (Long roleId : userOfficial.getRoleIds()){
//            SysUserRole sysUserRole = new SysUserRole();
//            sysUserRole.setUserId(userOfficial.getSysUserId());
//            sysUserRole.setRoleId(roleId);
//            sysUserRoles.add(sysUserRole);
//        }
//        sysUserRoleMapper.batchUserRole(sysUserRoles);
        return AjaxResult.success();
    }

    /**
     * 删除订阅号用户权限
     */
    @PreAuthorize("@ss.hasPermi('news:officialUser:remove')")
    @Log(title = "订阅号用户权限", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<String> ids) {
        //处理角色信息
//        List<UserOfficial> userOfficials = userOfficialService.listByIds(ids);
//        if(CollectionUtils.isNotEmpty(userOfficials)){
//            for (UserOfficial userOfficial : userOfficials){
//                LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<SysRole>();
//                wrapper.in(SysRole::getRoleKey,"officialManger","publisher","approver");
//                List<SysRole> roles = iSysRoleService.list(wrapper);
//                LambdaUpdateWrapper<SysUserRole> ew = new LambdaUpdateWrapper();
//                ew.eq(SysUserRole::getUserId,userOfficial.getSysUserId());
//                ew.in(SysUserRole::getRoleId,roles.stream().map(x->x.getRoleId()).collect(Collectors.toList()));
//                sysUserRoleMapper.delete(ew);
//            }
//        }

        return toAjax(userOfficialService.removeByIds(ids));
    }
}
