package com.zainanjing.convenience.news.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.zainanjing.official.service.IOfficialAccountService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 注销OGC用户
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
//@RestController
//@RequestMapping("/logoff")
public class LogOffController extends BaseController {

    @Resource
    private IOfficialAccountService officialAccountService;

    /**
     * 主要测试使用
     */
    @GetMapping("/{userId}")
    public AjaxResult list(@PathVariable("userId") String userId) {
        officialAccountService.logOff(userId);
        return AjaxResult.success();
    }

}
