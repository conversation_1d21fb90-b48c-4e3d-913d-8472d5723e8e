package com.zainanjing.convenience.news.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.ArticleCat;
import com.zainanjing.official.service.IArticleCatService;
import com.zainanjing.official.service.IArticleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 新闻分类Controller
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@RestController
@RequestMapping("/news/category")
public class ArticleCatController extends BaseController {
    @Autowired
    private IArticleCatService articleCatService;

    @Autowired
    private IArticleService iArticleService;


    /**
     * 查询新闻分类列表
     */
//    @PreAuthorize("@ss.hasPermi('news:category:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {

        IPage<ArticleCat> page = articleCatService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取新闻分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('news:category:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(articleCatService.getById(id));
    }

    /**
     * 新增新闻分类
     */
    @PreAuthorize("@ss.hasPermi('news:category:add')")
    @Log(title = "新闻分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ArticleCat articleCat) {
        return toAjax(articleCatService.save(articleCat));
    }

    /**
     * 修改新闻分类
     */
    @PreAuthorize("@ss.hasPermi('news:category:edit')")
    @Log(title = "新闻分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ArticleCat articleCat) {
        return toAjax(articleCatService.updateById(articleCat));
    }

    /**
     * 删除新闻分类
     */
    @PreAuthorize("@ss.hasPermi('news:category:remove')")
    @Log(title = "新闻分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        LambdaQueryWrapper<Article> ew = new LambdaQueryWrapper<Article>();
        ew.ne(Article::getStatus, 1);
        ew.in(Article::getArticleCatId, ids);
        if (iArticleService.count(ew) > 0) {
            return AjaxResult.error("该分类下存在对应新闻资讯，无法删除！");
        } else {
            return toAjax(articleCatService.removeByIds(ids));
        }
    }
}
