package com.zainanjing.convenience.news.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.oss.utils.AliOssFileUploadUtils;
import com.ruoyi.common.core.service.ISysConfigService;
import com.zainanjing.official.domain.OfficialAccountApply;
import com.zainanjing.official.service.IOfficialAccountApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订阅号申请Controller
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@RestController
@RequestMapping("/official/apply")
public class OfficialAccountApplyController extends BaseController {
    @Autowired
    private IOfficialAccountApplyService officialAccountApplyService;

    @Autowired
    private ISysConfigService iSysConfigService;

    /**
     * 查询订阅号申请列表
     */
    @PreAuthorize("@ss.hasPermi('official:apply:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<OfficialAccountApply> page = officialAccountApplyService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取订阅号申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('official:apply:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(officialAccountApplyService.getById(id));
    }


    /**
     * 订阅号申请审批
     */
    @PreAuthorize("@ss.hasPermi('official:apply:edit')")
    @Log(title = "订阅号申请审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OfficialAccountApply officialAccountApply) {
        return toAjax(officialAccountApplyService.approveApply(officialAccountApply));
    }

    /**
     * 模板文件上传
     */
    @PostMapping("/template/upload")
    @PreAuthorize("@ss.hasPermi('official:apply:template')")
    public AjaxResult upload(MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            String fileName = iSysConfigService.selectConfigByKey("gdmm.official.template.upload");
            String[] allowedExtension = {"docx"};
            return AjaxResult.success(AliOssFileUploadUtils.uploadByPath(fileName, file, allowedExtension));
        }
        return AjaxResult.error("上传异常，请联系管理员");
    }
}
