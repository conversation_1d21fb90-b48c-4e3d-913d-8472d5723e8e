package com.zainanjing.convenience.news.controller;

import com.ruoyi.common.utils.BeanUtil;
import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.dfa.WordTree;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.ISysConfigService;
import com.ruoyi.common.core.service.ISysDictTypeService;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.AsyncManager;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.html.EscapeUtil;
import com.ruoyi.framework.web.service.PermissionService;
import com.zainanjing.official.constant.ArticleConstant;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.domain.ArticleCat;
import com.zainanjing.official.domain.OfficialAccount;
import com.zainanjing.official.domain.UserOfficial;
import com.zainanjing.official.search.EArticle;
import com.zainanjing.official.search.EArticleService;
import com.zainanjing.official.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 新闻Controller
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
@RestController
@RequestMapping("/news/article")
public class ArticleController extends BaseController {
    @Autowired
    private IArticleService articleService;

    @Resource
    private IArticleCatService articleCatService;

    @Autowired
    private ISysConfigService iSysConfigService;

    @Autowired
    private IUserOfficialService iUserOfficialService;

    @Resource(name = "ss")
    private PermissionService permissionService;

    @Autowired
    private EArticleService eArticleService;

    @Resource
    private IArticleCollectionRefService iArticleCollectionRefService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private ISysDictTypeService dictTypeService;

    @Resource
    private IOfficialAccountService officialAccountService;

    @GetMapping("/reset/search")
    public AjaxResult resetSearch() {
        AsyncManager.me().execute(() -> {
            eArticleService.deleteAll();
            QueryWrapper<Article> ew = new QueryWrapper<>();
            ew.eq("status", 0);
            //一次性查询50条，循环同步
            int i = 1;
            while (true) {
                IPage page = new Page(i, 50, false);
                List<Article> articles = articleService.list(page, ew);
                i++;
                if (CollUtil.isNotEmpty(articles)) {
                    for (Article article : articles) {
                        if (!ArticleConstant.ARTICLE_CAT_COLLECT.equals(article.getArticleCatId())) {
                            eArticleService.save(BeanUtil.copyProperties(article, EArticle.class));
                        }
                    }
                } else {
                    break;
                }
            }
        });
        return AjaxResult.success();
    }

    /**
     * 查询新闻列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())
                && !permissionService.hasPermi("official:account:all")) {
            LambdaQueryWrapper<UserOfficial> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserOfficial::getSysUserId, SecurityUtils.getUserId());
            List<UserOfficial> userOfficials = iUserOfficialService.list(wrapper);
            searchable.addSearchParam("officialAccountId_in", userOfficials.stream().map(UserOfficial::getOfficialAccountId).collect(Collectors.toList()));
        }
        IPage<Article> page = articleService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    @GetMapping("/subList/{id}")
    public AjaxResult subList(@PathVariable("id") Long id) {
        return AjaxResult.success(articleService.getSubArticles(0, -1, id).getRecords());
    }

    /**
     * 获取新闻详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(articleService.getById(id));
    }

    /**
     * 新增新闻
     */
    @Log(title = "新闻", businessType = BusinessType.INSERT)
    @PostMapping
    @PreAuthorize("@ss.hasPermi('news:article:add')")
    public AjaxResult add(@RequestBody Article article) {

        AjaxResult ajaxResult = sensitiveFilter(article);
        if (!Objects.equals(ajaxResult.get("code"), 200)) {
            return ajaxResult;
        }

        if (StrUtil.isNotEmpty(article.getContent())) {
            String text = EscapeUtil.clean(article.getContent()).replaceAll("&nbsp;", "");
            article.setRemark(text.substring(0, Math.min(text.length(), 1000)));
        }

        //根据配置判断逻辑，初始化状态,如果开启设置状态为待审批3
        JSONObject config = JSON.parseObject(iSysConfigService.selectConfigByKey("gdmm.news.setting"));
        if (article.getStatus() == 3) {
            if ("1".equals(config.getString("isAuditNews"))) {
                article.setStatus(3);
            } else {
                article.setStatus(0);
            }
        }

        if (ArticleConstant.ARTICLE_CAT_COLLECT.equals(article.getArticleCatId())) {
            article.setType(ArticleConstant.ARTICLE_TYPE_COLLECT);
        } else if (ArticleConstant.ARTICLE_CAT_VIDEO.equals(article.getArticleCatId())) {
            article.setType(ArticleConstant.ARTICLE_TYPE_VIDEO);
        } else {
            article.setType(ArticleConstant.ARTICLE_TYPE_NORMAL);
        }

        if (articleService.saveAndEvent(article)) {
            return toAjax(true);
        }
        return toAjax(false);
    }

    /**
     * 敏感词过滤
     */
    private AjaxResult sensitiveFilter(Article article) {
        //敏感词过滤
        List<SysDictData> officialSensitives = dictTypeService.selectDictDataByType("official_sensitive");
        if (CollUtil.isNotEmpty(officialSensitives)) {
            WordTree tree = new WordTree();
            officialSensitives.stream().map(SysDictData::getDictValue).forEach(tree::addWord);


            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("code", 200);
            List<String> matchAllTitle = tree.matchAll(article.getTitle(), -1, false, false);
            if (CollUtil.isNotEmpty(matchAllTitle)) {
                ajaxResult.put("code", 500);
                ajaxResult.put("msg", "标题包含敏感词:" + matchAllTitle.toString() + ";");
            }

            List<String> matchAllText = tree.matchAll(article.getContent(), -1, false, false);
            if (CollUtil.isNotEmpty(matchAllText)) {
                ajaxResult.put("code", 500);
                ajaxResult.put("msg", ajaxResult.getOrDefault("msg", "") + "内容包含敏感词:" + matchAllText.toString() + ";");
            }

            List<String> matchAllEdit = tree.matchAll(article.getEditor(), -1, false, false);
            if (CollUtil.isNotEmpty(matchAllEdit)) {
                ajaxResult.put("code", 500);
                ajaxResult.put("msg", ajaxResult.getOrDefault("msg", "") + "编辑包含敏感词:" + matchAllEdit.toString() + ";");
            }

            List<String> matchAllReporter = tree.matchAll(article.getReporter(), -1, false, false);
            if (CollUtil.isNotEmpty(matchAllReporter)) {
                ajaxResult.put("code", 500);
                ajaxResult.put("msg", ajaxResult.getOrDefault("msg", "") + "记者包含敏感词:" + matchAllReporter.toString() + ";");
            }
            if (!Objects.equals(ajaxResult.get("code"), 200)) {
                ajaxResult.put("msg", ajaxResult.getOrDefault("msg", "") + "请修改后再提交");
                return ajaxResult;
            }
        }
        return AjaxResult.success();
    }


    /**
     * 修改新闻（仅修改附属信息，不影响状态、敏感词、ES检索）
     */
    @Log(title = "新闻", businessType = BusinessType.UPDATE)
    @PutMapping("/editExt")
    @PreAuthorize("@ss.hasPermi('news:article:edit')")
    public AjaxResult editExt(@RequestBody Article article) {
        return toAjax(articleService.updateByIdAndEvent(article));
    }

    /**
     * 修改新闻
     */
    @Log(title = "新闻", businessType = BusinessType.UPDATE)
    @PutMapping
    @PreAuthorize("@ss.hasPermi('news:article:edit')")
    public AjaxResult edit(@RequestBody Article article) {

        AjaxResult ajaxResult = sensitiveFilter(article);
        if (!Objects.equals(ajaxResult.get("code"), 200)) {
            return ajaxResult;
        }

        JSONObject config = JSON.parseObject(iSysConfigService.selectConfigByKey("gdmm.news.setting"));
        if (article.getStatus() == 3) {
            if ("1".equals(config.getString("isAuditNews"))) {
                article.setStatus(3);
            } else {
                article.setStatus(0);
            }
        }
        if (StrUtil.isNotEmpty(article.getContent())) {
            String str = EscapeUtil.clean(article.getContent()).replaceAll("&nbsp;", "");
            article.setRemark(str.substring(0, Math.min(str.length(), 1000)));
        }

        if (ArticleConstant.ARTICLE_CAT_COLLECT.equals(article.getArticleCatId())) {
            article.setType(ArticleConstant.ARTICLE_TYPE_COLLECT);
        } else if (ArticleConstant.ARTICLE_CAT_VIDEO.equals(article.getArticleCatId())) {
            article.setType(ArticleConstant.ARTICLE_TYPE_VIDEO);
        } else {
            article.setType(ArticleConstant.ARTICLE_TYPE_NORMAL);
        }

        if (articleService.updateByIdAndEvent(article)) {
            if ((article.getEnabled() != 0 || article.getStatus() != 0) && Integer.valueOf(1).equals(article.getRecommended())) {
                return AjaxResult.success("新闻已推荐至卡片，重新保存后，系統会按状态取消卡片推荐", 3000);//TODO 后续优化处理放到兰州定制版本中
            }
            return toAjax(true);
        }
        return toAjax(false);
    }

    @Log(title = "审批新闻", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    @PreAuthorize("@ss.hasPermi('news:article:approve')")
    public AjaxResult approve(@RequestBody Article article) {

        AjaxResult ajaxResult = sensitiveFilter(article);
        if (!Objects.equals(ajaxResult.get("code"), 200)) {
            return ajaxResult;
        }
        if (articleService.auditAndEvent(article)) {
            return toAjax(true);
        }
        return toAjax(false);
    }

    /**
     * 删除新闻
     */
    @Log(title = "新闻", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPermi('news:article:remove')")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        List<Article> articles = articleService.listByIds(ids);
        if (CollUtil.isEmpty(articles)) {
            return AjaxResult.error("删除失败，数据不存在");
        }
        articleService.removeByIdsAndEvent(ids);
        return AjaxResult.success();
    }

    @Log(title = "新闻更新虚拟点击量", businessType = BusinessType.UPDATE)
    @PutMapping("/changeInitValue")
    @PreAuthorize("@ss.hasPermi('news:article:edit')")
    public AjaxResult changeInitValue(@RequestBody Article article) {
        LambdaUpdateWrapper<Article> lambdaUpdateWrapper = new LambdaUpdateWrapper<Article>()
                .set(article.getInitView() != null, Article::getInitView, article.getInitView())
                .set(article.getInitLikeNum() != null, Article::getInitLikeNum, article.getInitLikeNum())
                .eq(Article::getId, article.getId());
        return AjaxResult.success(articleService.update(lambdaUpdateWrapper));
    }

    @Log(title = "新闻更新显示状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{ids}")
    @PreAuthorize("@ss.hasPermi('news:article:edit')")
    public AjaxResult status(@PathVariable("ids") List<Long> ids, @RequestParam("enabled") Integer enabled) {
        List<Article> articles = articleService.listByIds(ids);
        if (CollUtil.isEmpty(articles)) {
            return AjaxResult.error("数据不存在");
        }
        for (Article article : articles) {
            if (article.getEnabled() == enabled) {//相等不更新
                continue;
            }
            Article articleUpdate = new Article();
            articleUpdate.setId(article.getId());
            articleUpdate.setEnabled(enabled);
            articleService.updateByIdAndEvent(articleUpdate);
        }
        return AjaxResult.success();
    }
}
