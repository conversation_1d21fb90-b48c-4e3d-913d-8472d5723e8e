package com.zainanjing.convenience.news.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.search.Searchable;
import com.zainanjing.official.domain.OfficialAccountCategory;
import com.zainanjing.official.service.IOfficialAccountCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订阅号分类Controller
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@RestController
@RequestMapping("/official/category")
public class OfficialAccountCategoryController extends BaseController {
    @Autowired
    private IOfficialAccountCategoryService officialAccountCategoryService;

    /**
     * 查询订阅号分类列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        IPage<OfficialAccountCategory> page = officialAccountCategoryService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取订阅号分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('official:category:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(officialAccountCategoryService.getById(id));
    }

    /**
     * 新增订阅号分类
     */
    @PreAuthorize("@ss.hasPermi('official:category:add')")
    @Log(title = "订阅号分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OfficialAccountCategory officialAccountCategory) {
        return toAjax(officialAccountCategoryService.save(officialAccountCategory));
    }

    /**
     * 修改订阅号分类
     */
    @PreAuthorize("@ss.hasPermi('official:category:edit')")
    @Log(title = "订阅号分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OfficialAccountCategory officialAccountCategory) {
        return toAjax(officialAccountCategoryService.updateById(officialAccountCategory));
    }

    /**
     * 删除订阅号分类
     */
    @PreAuthorize("@ss.hasPermi('official:category:remove')")
    @Log(title = "订阅号分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(officialAccountCategoryService.removeByIds(ids));
    }
}
