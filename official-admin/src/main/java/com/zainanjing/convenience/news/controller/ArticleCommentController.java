package com.zainanjing.convenience.news.controller;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.annotation.JsonView;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.Views;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.framework.web.service.PermissionService;
import com.zainanjing.official.domain.ArticleComment;
import com.zainanjing.official.domain.UserOfficial;
import com.zainanjing.official.service.IArticleCommentService;
import com.zainanjing.official.service.IArticleService;
import com.zainanjing.official.service.IUserOfficialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 新闻评论Controller
 *
 * <AUTHOR>
 * @date 2022-12-10
 */
@RestController
@RequestMapping("/news/comment")
public class ArticleCommentController extends BaseController {
    @Autowired
    private IArticleCommentService articleCommentService;

    @Autowired
    private IUserOfficialService iUserOfficialService;

    @Resource(name = "ss")
    private PermissionService permissionService;

    @Resource
    private IArticleService iArticleService;

    /**
     * 查询新闻评论列表
     */
//    @PreAuthorize("@ss.hasPermi('news:comment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())
                && !permissionService.hasPermi("official:account:all")) {
            LambdaQueryWrapper<UserOfficial> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserOfficial::getSysUserId, SecurityUtils.getUserId());
            List<UserOfficial> userOfficials = iUserOfficialService.list(wrapper);
            searchable.addSearchParam("officialAccountId_in", userOfficials.stream().map(UserOfficial::getOfficialAccountId).collect(Collectors.toList()));
        }
        searchable.addSearchParam("status_in", Arrays.asList(0, 1));
        IPage<ArticleComment> page = articleCommentService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取新闻评论详细信息
     */
//    @PreAuthorize("@ss.hasPermi('news:comment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(articleCommentService.getById(id));
    }

    /**
     * 修改新闻评论
     */
    @PreAuthorize("@ss.hasPermi('news:comment:edit')")
    @Log(title = "新闻评论审核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ArticleComment articleComment) {
        articleCommentService.updateById(articleComment);
        iArticleService.reCountCommentNum(articleComment.getArticleId());
        return AjaxResult.success();

    }

    /**
     * 删除新闻评论
     */
    @PreAuthorize("@ss.hasPermi('news:comment:remove')")
    @Log(title = "新闻评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        List<ArticleComment> articleComments = articleCommentService.listByIds(ids);
        articleCommentService.removeByIds(ids);
        Set<Long> collect = articleComments.stream().map(ArticleComment::getArticleId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(collect)) {
            collect.forEach(
                    x -> iArticleService.reCountCommentNum(x)
            );
        }
        return AjaxResult.success();
    }
}
