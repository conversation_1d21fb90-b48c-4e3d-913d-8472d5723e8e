package com.zainanjing.convenience.news.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.framework.web.service.PermissionService;
import com.zainanjing.official.domain.OfficialAccount;
import com.zainanjing.official.domain.UserOfficial;
import com.zainanjing.official.search.EArticleService;
import com.zainanjing.official.service.IArticleService;
import com.zainanjing.official.service.IOfficialAccountService;
import com.zainanjing.official.service.IUserOfficialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订阅号Controller
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@RestController
@RequestMapping("/official/account")
public class OfficialAccountController extends BaseController {
    @Autowired
    private IOfficialAccountService officialAccountService;

    @Autowired
    private IUserOfficialService userOfficialService;

    @Resource(name = "ss")
    private PermissionService permissionService;

    @Resource
    private EArticleService eArticleService;

    @Resource
    private IArticleService iArticleService;

    /**
     * 查询订阅号列表
     */
//    @PreAuthorize("@ss.hasPermi('official:account:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        //没有
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())
                && !permissionService.hasPermi("official:account:all")) {
            LambdaQueryWrapper<UserOfficial> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserOfficial::getSysUserId, SecurityUtils.getUserId());
            List<UserOfficial> userOfficials = userOfficialService.list(wrapper);
            searchable.addSearchParam("id_in", userOfficials.stream().map(UserOfficial::getOfficialAccountId).collect(Collectors.toList()));
        }
        searchable.addSearchParam("deleted_eq", 0);
        IPage<OfficialAccount> page = officialAccountService.findByPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取订阅号详细信息
     */
    @PreAuthorize("@ss.hasPermi('official:account:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(officialAccountService.getById(id));
    }

    /**
     * 新增订阅号
     */
    @PreAuthorize("@ss.hasPermi('official:account:add')")
    @Log(title = "订阅号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OfficialAccount officialAccount) {
        return toAjax(officialAccountService.save(officialAccount));
    }

    /**
     * 修改订阅号
     */
    @PreAuthorize("@ss.hasPermi('official:account:edit')")
    @Log(title = "订阅号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OfficialAccount officialAccount) {
        return toAjax(officialAccountService.updateById(officialAccount));
    }

    /**
     * 删除订阅号
     */
    @PreAuthorize("@ss.hasPermi('official:account:remove')")
    @Log(title = "订阅号", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return AjaxResult.success(officialAccountService.remove(ids));
    }

    @PreAuthorize("@ss.hasPermi('official:account:edit')")
    @Log(title = "订阅号启用", businessType = BusinessType.UPDATE)
    @PostMapping("/enabled/{ids}")
    public AjaxResult enabled(@PathVariable("ids") List<Long> ids) {
        LambdaUpdateWrapper<OfficialAccount> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OfficialAccount::getEnabled, 1).in(OfficialAccount::getId, ids);
        return toAjax(officialAccountService.update(wrapper));
    }

    @PreAuthorize("@ss.hasPermi('official:account:edit')")
    @Log(title = "订阅号停用", businessType = BusinessType.UPDATE)
    @PostMapping("/unEnabled/{ids}")
    public AjaxResult unEnabled(@PathVariable("ids") List<Long> ids) {
        LambdaUpdateWrapper<OfficialAccount> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OfficialAccount::getEnabled, 0).in(OfficialAccount::getId, ids);
        return toAjax(officialAccountService.update(wrapper));
    }
}
