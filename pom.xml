<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zainanjing</groupId>
    <artifactId>news-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.2.0</version>
    <name>news-parent</name>
    <description>新闻板块</description>

    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi.zainanjing365</groupId>
        <version>3.10.0</version>
    </parent>

    <modules>
        <!-- 通用API -->
        <module>common</module>
        <module>common-api</module>
        <!-- 主播号 -->
        <module>anchor-channel</module>
        <!-- 订阅号 -->
        <module>official</module>
        <module>official-api</module>
        <module>official-admin</module>
        <!-- 资讯/社区 后台管理 -->
        <module>article</module>
        <module>article-admin</module>
        <module>article-api</module>
        <!-- 开发调试服务 -->
        <module>site-admin</module>
        <module>site-api</module>
        <module>zijin</module>
        <module>zijin-api</module>
        <module>zijin-admin</module>
        <module>ccms-mix-api</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>
        <elasticsearch>7.17.23</elasticsearch>
        <elasticsearch-rhlc>7.17.23</elasticsearch-rhlc>
    </properties>


    <pluginRepositories>
        <pluginRepository>
            <id>Aliyun</id>
            <name>Aliyun</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

    <dependencyManagement>
        <dependencies>

            <!-- nacos -->
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>0.3.99-RC</version>
            </dependency>

            <!-- elasticsearch -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>7.17.23</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.17.23</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.17.23</version>
            </dependency>

            <!-- 工具 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.10.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.14.0</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>3.37.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>2.21.1</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>zijin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>zijin-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>zijin-admin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>ccms-mix-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>article</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>article-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>article-admin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>official</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>official-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>official-admin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zainanjing</groupId>
                <artifactId>common-api</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>