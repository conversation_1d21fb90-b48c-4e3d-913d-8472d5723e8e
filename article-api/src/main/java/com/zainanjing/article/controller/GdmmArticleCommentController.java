package com.zainanjing.article.controller;

import com.ruoyi.common.utils.NumberUtil;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.article.domain.GdmmArticle;
import com.zainanjing.article.domain.GdmmArticleComment;
import com.zainanjing.article.domain.GdmmSpecialWord;
import com.zainanjing.article.service.IGdmmArticleCommentService;
import com.zainanjing.article.service.IGdmmArticleService;
import com.zainanjing.article.service.IGdmmSpecialWordService;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.common.util.RedisUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/api-ae/gdmmArticleComment")
public class GdmmArticleCommentController {

    private final Logger logger = LoggerFactory.getLogger(GdmmArticleCommentController.class);

    @Resource
    private IGdmmArticleService gdmmArticleService;

    @Resource
    private IGdmmArticleCommentService gdmmArticleCommentService;

    @Resource
    private IGdmmSpecialWordService gdmmSpecialWordService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;

    /**
     * 添加评论
     */
    @PostMapping("/save")
    public ResResult saveComment(HttpServletRequest request) {

        GdmmArticleComment gdmmArticleComment = new GdmmArticleComment();
        Long uid = SecurityUtils.getUserId();
        String articleId = request.getParameter("articleId");
        String content = request.getParameter("content");
        String type = request.getParameter("type");
        String commentId = request.getParameter("commentId");
        if (!NumberUtil.isLong(articleId)) {
            return ResResult.error(30020, "入参有误");
        }
        if (StrUtil.isBlank(content)) {
            return ResResult.error(30020, "评论内容不能为空");
        }
        if (content.length() > Constants.COMMENT_CONTENT_MAX_SIZE) {
            return ResResult.error(30020, "评论内容不能超过" + Constants.COMMENT_CONTENT_MAX_SIZE + "个字符");
        }
        if (StrUtil.isBlank(type)) {
            return ResResult.error(30020, "评论类型不能为空");
        }

        //判断是否是子评论
        if (Constants.TYPE_COMMENT.equals(type)) {
            if (!NumberUtil.isLong(commentId)) {
                return ResResult.error(30020, "入参有误");
            }
            gdmmArticleComment.setParentId(Long.parseLong(commentId));
        } else {
            gdmmArticleComment.setParentId(0L);
        }

        wangyiCheckUtil.checkText("GdmmArticleCommentServiceImpl_gdmmSaveByMap",content);

        //判断是否可以评论,先根据总设置，然后再根据单条新闻设置
        GdmmArticle gdmmArticle = gdmmArticleService.getById(articleId);
        String isCommentStr = RedisUtil.findString(RedisKey.ARTICLE_IS_COMMENT.getKey());
        logger.debug("查看articleId={}新闻,根据key={},从Redis取 新闻是否开启评论为{}", articleId, RedisKey.ARTICLE_IS_COMMENT.getKey(), isCommentStr);
        Integer isComment = StrUtil.isBlank(isCommentStr) ? 0 : Integer.valueOf(isCommentStr) == 1 ? gdmmArticle.getIsComment() : 0;
        if (Constants.FOU.equals(isComment)) {
            return ResResult.error(30022, "该新闻不允许评论");
        }
        //判断是否需要审核
        String isExamineStr = RedisUtil.findString(RedisKey.ARTICLE_IS_EXAMINE.getKey());
        logger.debug("查看articleId={}新闻,根据key={},从Redis取 新闻是否开启评论审核为{}", articleId, RedisKey.ARTICLE_IS_EXAMINE.getKey(), isExamineStr);
        Integer isExamine = StrUtil.isBlank(isExamineStr) ? 0 : Integer.valueOf(isExamineStr);
        gdmmArticleComment.setStatus(isExamine);
        //如果评论不需要审核 评论数直接加上 (这里要考虑 redis 没key值的情况)
        if (!Constants.YES.equals(isExamineStr)) {
            //新闻评论数
            CommonUtil.redisCommonAdd(Constants.ARTICLE + "_" + Constants.COMMENTNUM + "_" + articleId);
        }

        // 敏感词过滤
        List<String> sensitiveWords = gdmmSpecialWordService.lambdaQuery().select(GdmmSpecialWord::getName).list().stream().map(GdmmSpecialWord::getName).toList();
        for (String string : sensitiveWords) {
            if (content.contains(string)) {
                return ResResult.error(10002, "帖子内容中含有敏感词 '" + string + "'，提交失败");
            }
        }

        gdmmArticleComment.setArticleId(Integer.parseInt(articleId));
        gdmmArticleComment.setContent(content);
        gdmmArticleComment.setType(Integer.parseInt(type));
        gdmmArticleComment.setSort(0);
        gdmmArticleComment.setUid(uid.intValue());
        Integer currentTime = CommonUtil.getTimestamp();
        gdmmArticleComment.setCreateTime(currentTime);
        gdmmArticleComment.setUpdateTime(currentTime);
        gdmmArticleCommentService.save(gdmmArticleComment);
        applicationEventPublisher.publishEvent(new CommonEvent(gdmmArticleComment, EventAction.builder()
                .operator(SecurityUtils.getLoginUser())
                .type(BusinessType.INSERT)
                .build()));
        return ResResult.success(gdmmArticleComment.getId());

    }

    @PostMapping("/praise")
    public ResResult praise(HttpServletRequest request) {
        Long uid = SecurityUtils.getUserId();
        String commentId = request.getParameter("commentId");
        //类型【必填】 0赞1取消赞   ===新闻保持一致
        String status = request.getParameter("status");
        if (!NumberUtil.isInteger(commentId)) {
            ResResult.error(30020, "入参有误");
        }

        Set<String> values = Set.of("0", "1");//0 取消点赞，1点赞
        if (!values.contains(status)) {
            ResResult.error(30020, "入参有误");
        }

        String uid_commentId_key = RedisKey.ARTICLE_COMMENT_PRAISE.getKeyForSqComment(uid.toString(), commentId);
        String commentId_key = RedisKey.ARTICLE_COMMENT_PRAISE_NUM.getKey(commentId);

        Long value = CommonUtil.redisCommonFind(uid_commentId_key);

        //点赞
        if (Constants.YES.equals(status)) {
            if (value == 1l) {
                throw new ServiceException("该新闻评论已经点赞");
            } else {
                CommonUtil.redisCommonPut(uid_commentId_key, Constants.YES);
                Long praiseNum = CommonUtil.redisCommonAdd(commentId_key);
                logger.debug("新闻评论点赞------------redis +1-----------:commentId:{},点赞数：{}", commentId, praiseNum);
                GdmmArticleComment gdmmArticleComment = new GdmmArticleComment();
                gdmmArticleComment.setId(Long.valueOf(commentId));
                applicationEventPublisher.publishEvent(new CommonEvent(gdmmArticleComment, EventAction.builder()
                        .operator(SecurityUtils.getLoginUser())
                        .type(BusinessType.LIKE)
                        .build()));
            }
            //取消点赞
        } else if (Constants.NO.equals(status)) {
            if (value == 1l) {
                CommonUtil.redisCommonPut(uid_commentId_key, Constants.NO);
                CommonUtil.redisCommonMinus(commentId_key);
                logger.debug("新闻评论取消点赞------------redis -1-----------:commentId:{}", commentId);
            } else {
                throw new ServiceException("该新闻评论已经取消点赞");
            }
        }
        return ResResult.success();
    }
}
