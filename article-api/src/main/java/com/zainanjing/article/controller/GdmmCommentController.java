package com.zainanjing.article.controller;

import com.ruoyi.common.utils.NumberUtil;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.article.domain.BcImg;
import com.zainanjing.article.domain.GdmmComment;
import com.zainanjing.article.domain.GdmmSpecialWord;
import com.zainanjing.article.domain.GdmmVideo;
import com.zainanjing.article.service.IGdmmCommentService;
import com.zainanjing.article.service.IGdmmImgService;
import com.zainanjing.article.service.IGdmmSpecialWordService;
import com.zainanjing.article.service.IGdmmVideoService;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/api-ae/gdmmComment")
public class GdmmCommentController {

    private final Logger logger = LoggerFactory.getLogger(GdmmCommentController.class);

    @Resource
    private IGdmmVideoService gdmmVideoService;

    @Resource
    private IGdmmSpecialWordService gdmmSpecialWordService;

    @Resource
    private IGdmmCommentService gdmmCommentService;

    @Resource
    private IGdmmImgService gdmmImgService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;

    /**
     * 添加评论
     */
    @PostMapping("/save")
    public ResResult saveComment(HttpServletRequest request) {
        GdmmComment gdmmComment = new GdmmComment();
        Long uid = SecurityUtils.getUserId();
        String resId = request.getParameter("resId");
        String resType = request.getParameter("resType");
        String content = request.getParameter("content");
        String type = request.getParameter("type");
        String commentId = request.getParameter("commentId");
        String bcImgIds = request.getParameter("bcImgIds");
        String isAnonymous = request.getParameter("isAnonymous");
        String msgtype = request.getParameter("msgtype");

        wangyiCheckUtil.checkText("GdmmCommentServiceImpl_gdmmSaveByMap",content);

        //20240204 评论记录ip并转换为省市区 主要要做到转换失败了，评论也要成功
        String ip = IpUtils.getIpAddr(request);
        String region = AddressUtils.getRealAddressByIP(ip);

        if (StrUtil.isBlank(resId)) {
            return ResResult.error(30020, "资源ID不能为空");
        }
        if (StrUtil.isBlank(resType)) {
            return ResResult.error(30020, "资源类型不能为空");
        }
        if (StrUtil.isBlank(content)) {
            return ResResult.error(30020, "评论内容不能为空");
        }
        if (content.length() > Constants.COMMENT_CONTENT_MAX_SIZE) {
            return ResResult.error(30020, "评论内容不能超过" + Constants.COMMENT_CONTENT_MAX_SIZE + "个字符");
        }
        if (StrUtil.isBlank(type)) {
            return ResResult.error(30020, "评论类型不能为空");
        }

        //短视频
        if (Constants.SHORT_VIDEO_NUM.equals(resType)) {
            GdmmVideo gdmmVideo = gdmmVideoService.getById(resId);
            Integer isOpenShortVideoComment = Integer.parseInt(CommonUtil.redisCommonFind(RedisKey.ARTICLE_IS_OPEN_SHORT_VIDEO_COMMENT.getKey()) + "");
            Integer isAuditShortVideoComment = Integer.parseInt(CommonUtil.redisCommonFind(RedisKey.ARTICLE_IS_AUDIT_SHORT_VIDEO_COMMENT.getKey()) + "");
            logger.debug("短视频 发表评论[resId:{},resType:{},isOpenShortVideoComment:{},isAuditShortVideoComment:{},isComment:{}", resId, resType, isOpenShortVideoComment, isAuditShortVideoComment, gdmmVideo.getIsComment());

            //是否开启评论，先判断全局，再判断单条
            if (Constants.FOU.equals(isOpenShortVideoComment) || Constants.FOU.equals(gdmmVideo.getIsComment())) {
                return ResResult.error(90001, "短视频评论已关闭");
            }

            //是否需要审核
            gdmmComment.setStatus(Constants.SHI.equals(isAuditShortVideoComment) ? Constants.SHI : Constants.FOU);

            //如果评论不需要审核 评论数直接加上 (这里要考虑 redis 没key值的情况)
            if (Constants.FOU.equals(isAuditShortVideoComment)) {
                //短视频评论数
                CommonUtil.redisCommonAdd(Constants.SHORT_VIDEO_STR + "_" + Constants.COMMENTNUM + "_" + resId);
            }
        }

        // 敏感词过滤
        List<String> sensitiveWords = gdmmSpecialWordService.lambdaQuery().select(GdmmSpecialWord::getName).list().stream().map(GdmmSpecialWord::getName).toList();
        for (String string : sensitiveWords) {
            if (content.contains(string)) {
                return ResResult.error(10002, "帖子内容中含有敏感词 '" + string + "'，提交失败");

            }
        }

        //是否是子评论
        if (Constants.TYPE_COMMENT.equals(type)) {
            if(!NumberUtil.isLong(commentId)) {
                return ResResult.error(30020, "入参有误");
            }
            gdmmComment.setParentId(Long.parseLong(commentId));
        } else {
            gdmmComment.setParentId(0L);
        }

        //是否匿名
        if (StrUtil.isNotEmpty(isAnonymous)) {
            gdmmComment.setIsAnonymous(Integer.valueOf(isAnonymous));
        } else {
            gdmmComment.setIsAnonymous(0);
        }

        gdmmComment.setResId(Integer.parseInt(resId));
        gdmmComment.setContent(content);
        gdmmComment.setType(Integer.parseInt(type));
        gdmmComment.setResType(Long.parseLong(resType));
        gdmmComment.setFloor(0L);
        gdmmComment.setSort(0);
        gdmmComment.setUid(uid.intValue());
        gdmmComment.setCreateTime(CommonUtil.getTimestamp());
        gdmmComment.setUpdateTime(CommonUtil.getTimestamp());
        gdmmComment.setIp(ip);
        gdmmComment.setRegion(region);
        gdmmCommentService.save(gdmmComment);
        Long gdmmCommentId = gdmmComment.getId();
        applicationEventPublisher.publishEvent(new CommonEvent(gdmmComment, EventAction.builder()
                .operator(SecurityUtils.getLoginUser())
                .type(BusinessType.INSERT)
                .build()));

        //更新楼层
        if (Constants.TYPE_POST.equals(type)) {
            HashMap<String, Object> map = new HashMap<String, Object>();
            map.put("resId", gdmmComment.getResId());
            map.put("type", Constants.ONE);
            map.put("idForFloor", gdmmCommentId);
            Integer floor = gdmmCommentService.count(map);
            map = new HashMap<String, Object>();
            map.put("floor", floor);
            map.put("commentId", gdmmCommentId);
            gdmmCommentService.updateByMap(map);
        }


        //图片插入评论id
        if (StrUtil.isNotEmpty(bcImgIds)) {
            int sort = 1;
            String[] pics = bcImgIds.split("_");
            for (String id : pics) {
                BcImg bcImg = new BcImg();
                if (Constants.SHORT_VIDEO_NUM.equals(resType)) {
                    bcImg.setType(Long.parseLong(Constants.IMAGE_TYPE_SHORT_VIDEO));
                }
                bcImg.setCommentId(gdmmCommentId);
                bcImg.setId(Long.valueOf(id));
                bcImg.setPostId(gdmmComment.getResId().longValue());
                bcImg.setSort(sort);
                gdmmImgService.updateById(bcImg);
                sort++;
            }
        }

        return ResResult.success(gdmmCommentId);

    }

}
