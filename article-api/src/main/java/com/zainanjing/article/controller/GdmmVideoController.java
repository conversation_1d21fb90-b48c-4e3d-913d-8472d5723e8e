package com.zainanjing.article.controller;

import com.ruoyi.common.utils.NumberUtil;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.article.domain.GdmmVideo;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.web.ResResult;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@RestController
@RequestMapping("/api-ae/video")
public class GdmmVideoController {

    private final Logger logger = LoggerFactory.getLogger(GdmmVideoController.class);

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;


    /**
     * 视频点赞
     */
    @PostMapping("/praise")
    public ResResult likeVideo(HttpServletRequest request) {
        Long uid = SecurityUtils.getUserId();

        String videoId = request.getParameter("videoId");
        //类型【必填】 0赞1取消赞   ===去社区保持一致
        String status = request.getParameter("status");

        if (!NumberUtil.isInteger(videoId)) {
            ResResult.error(30020, "入参有误");
        }

        Set<String> values = Set.of("0", "1");//0 取消点赞，1点赞
        if (!values.contains(status)) {
            ResResult.error(30020, "入参有误");
        }

        String uid_videoId_key = RedisKey.VIDEO_PRAISE.getKeyForVideo(String.valueOf(uid), videoId);
        String videoId_key = RedisKey.VIDEO_PRAISE_NUM.getKey(videoId);
        Long value = CommonUtil.redisCommonFind(uid_videoId_key);

        //点赞
        if (Constants.YES.equals(status)) {
            if (value == 1l) {
                throw new ServiceException("该短视频已经点赞");
            } else {
                CommonUtil.redisCommonPut(uid_videoId_key, Constants.YES);
                Long praiseNum = CommonUtil.redisCommonAdd(videoId_key);
                logger.debug("短视频点赞------------redis +1-----------:videoId:{},点赞数：{}", videoId, praiseNum);
                GdmmVideo gdmmVideo = new GdmmVideo();
                gdmmVideo.setId(Long.parseLong(videoId));
                applicationEventPublisher.publishEvent(new CommonEvent(gdmmVideo, EventAction.builder()
                        .operator(SecurityUtils.getLoginUser())
                        .type(BusinessType.LIKE)
                        .build()));
            }
            //取消点赞
        } else if (Constants.NO.equals(status)) {
            if (value == 1l) {
                CommonUtil.redisCommonPut(uid_videoId_key, Constants.NO);
                CommonUtil.redisCommonMinus(videoId_key);
                logger.debug("短视频取消点赞------------redis -1-----------:videoId:{}", videoId);
            } else {
                throw new ServiceException("该短视频已经取消点赞");
            }
        }
        return ResResult.success();
    }


}
