package com.zainanjing.shequ.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.shequ.domain.SqMessage;
import com.zainanjing.shequ.domain.SqPost;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 私信Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
public interface SqMessageMapper extends GenericMapper<SqMessage> {

    IPage<SqMessage> friendList(IPage<SqMessage> page, @Param("p") Map<String, Object> params);

}
