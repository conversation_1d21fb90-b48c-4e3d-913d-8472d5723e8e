package com.zainanjing.shequ.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.article.domain.GdmmComment;
import com.zainanjing.shequ.domain.SqComment;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 评论Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
public interface SqCommentMapper extends GenericMapper<SqComment> {


    IPage<SqComment> searchPage(IPage<SqComment> page, @Param("p") Map<String, Object> p);

}
