package com.zainanjing.shequ.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.shequ.domain.SqPost;
import com.zainanjing.shequ.dto.SqPostDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 帖子数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
public interface SqPostMapper extends GenericMapper<SqPost> {

    IPage<SqPostDTO> list(IPage<SqPost> page, @Param("p") Map<String, Object> params);

    SqPostDTO findById(@Param("id") Long id);



}
