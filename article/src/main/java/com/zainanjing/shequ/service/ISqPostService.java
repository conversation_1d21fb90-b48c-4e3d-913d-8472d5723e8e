package com.zainanjing.shequ.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.shequ.domain.SqPost;
import com.zainanjing.shequ.dto.SqPostDTO;

/**
 * 帖子数据Service接口
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
public interface ISqPostService extends GenericCurdService<SqPost> {

    IPage<SqPostDTO> list(Searchable searchable);

    void savePost(SqPost sqPost);

    void updatePost(SqPost sqPost);

    void updatePostCommentNum(Long postId);
}
