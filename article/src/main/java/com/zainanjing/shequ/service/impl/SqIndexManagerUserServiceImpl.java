package com.zainanjing.shequ.service.impl;

import java.util.List;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zainanjing.shequ.mapper.SqIndexManagerUserMapper;
import com.zainanjing.shequ.domain.SqIndexManagerUser;
import com.zainanjing.shequ.service.ISqIndexManagerUserService;

/**
 * 管理用户和会员对应关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class SqIndexManagerUserServiceImpl extends GenericCurdServiceImpl<SqIndexManagerUserMapper, SqIndexManagerUser>
        implements ISqIndexManagerUserService
{

}
