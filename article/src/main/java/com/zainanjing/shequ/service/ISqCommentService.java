package com.zainanjing.shequ.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.shequ.domain.SqComment;
import com.zainanjing.shequ.domain.SqPost;

import java.util.List;

/**
 * 评论Service接口
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
public interface ISqCommentService extends GenericCurdService<SqComment> {

    List<SqComment> searchCommentsTree(Long postId);

    IPage<SqComment> searchPage(Searchable searchable);

    Boolean saveComment(SqComment sqComment);

    void updateHasNewComment(final Long uid,final String type,final String postId,final String commentId,final Integer hasNewComment);
}
