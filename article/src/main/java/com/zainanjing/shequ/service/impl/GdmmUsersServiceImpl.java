package com.zainanjing.shequ.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.shequ.domain.GdmmUsers;
import com.zainanjing.shequ.domain.SqIndexUsersRole;
import com.zainanjing.shequ.mapper.GdmmUsersMapper;
import com.zainanjing.shequ.service.IGdmmUsersService;
import com.zainanjing.shequ.service.ISqIndexUsersRoleService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Service
public class GdmmUsersServiceImpl extends GenericCurdServiceImpl<GdmmUsersMapper, GdmmUsers>
        implements IGdmmUsersService {

    @Resource
    private ISqIndexUsersRoleService sqIndexUsersRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUser(GdmmUsers gdmmUsers) {
        sqIndexUsersRoleService.lambdaUpdate()
                .eq(SqIndexUsersRole::getUid, gdmmUsers.getUserId())
                .remove();
        if (gdmmUsers.getRoleId() != null && gdmmUsers.getRoleId() != 0) {
            SqIndexUsersRole sqIndexUsersRole = new SqIndexUsersRole();
            sqIndexUsersRole.setUid(gdmmUsers.getUserId());
            sqIndexUsersRole.setRoleId(gdmmUsers.getRoleId());
            sqIndexUsersRoleService.save(sqIndexUsersRole);
        }
        return true;
    }

}
