package com.zainanjing.shequ.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.utils.*;
import com.zainanjing.article.domain.BcImg;
import com.zainanjing.article.mapper.GdmmImgMapper;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.shequ.domain.SqPost;
import com.zainanjing.shequ.dto.SqPostDTO;
import com.zainanjing.shequ.mapper.SqPostMapper;
import com.zainanjing.shequ.service.ISqPostService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;

/**
 * 帖子数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Service
public class SqPostServiceImpl extends GenericCurdServiceImpl<SqPostMapper, SqPost>
        implements ISqPostService {

    private Logger logger = LoggerFactory.getLogger(SqPostServiceImpl.class);

    @Autowired
    private GdmmImgMapper gdmmImgMapper;

    @Override
    public IPage<SqPostDTO> list(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return baseMapper.list(pagination, searchable.getFilterCdtns());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePost(SqPost sqPost) {
        //从富文本截取图片地址
        postImgHandle(sqPost);
        this.save(sqPost);
        int sort = 1;
        if (CollUtil.isNotEmpty(sqPost.getBcImgList())) {
            for (BcImg bcImg : sqPost.getBcImgList()) {
                if (StrUtil.isEmpty(bcImg.getImgUrl())) continue;
                bcImg.setType(Long.valueOf(Constants.IMAGE_TYPE_SQPOST));
                bcImg.setPostId(sqPost.getId());
                bcImg.setSort(sort);
                gdmmImgMapper.insert(bcImg);
                sort++;
            }
        }

        //初始化sort 最后回复时间为id
        this.lambdaUpdate().eq(SqPost::getId, sqPost.getId())
                .set(SqPost::getSort, sqPost.getId())
                .set(SqPost::getLastReplyDateline, sqPost.getId())
                .update();
        if (ObjUtil.equals(0, sqPost.getStatus())) {
            CommonUtil.redisCommonAdd(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sqPost.getForumId());
            CommonUtil.redisCommonAdd(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sqPost.getForumId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePost(SqPost sqPost) {
        //当帖子不存在附件上传的图片时 则从富文本截取图片地址
        if (CollUtil.isEmpty(sqPost.getBcImgList())) {
            postImgHandle(sqPost);
        }
        //更新帖子
        this.updateById(sqPost);
        //更新帖子图片
        LambdaUpdateWrapper<BcImg> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BcImg::getPostId, sqPost.getId())
                .eq(BcImg::getType, Constants.IMAGE_TYPE_SQPOST)
                .set(BcImg::getStatus, Constants.STATUS_DELETE);
        gdmmImgMapper.update(lambdaUpdateWrapper);
        int sort = 1;
        for (BcImg bcImg : sqPost.getBcImgList()) {
            if (StrUtil.isEmpty(bcImg.getImgUrl())) continue;
            bcImg.setType(Long.valueOf(Constants.IMAGE_TYPE_SQPOST));
            bcImg.setPostId(sqPost.getId());
            bcImg.setSort(sort);
            gdmmImgMapper.insert(bcImg);
            sort++;
        }
    }

    @Override    // 这里暂时不用事物 免得影响性能 需要时再加
    public void updatePostCommentNum(Long postId) {
        AsyncManager.me().execute(() -> {
            LambdaUpdateWrapper<SqPost> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(SqPost::getId, postId)
                    .set(SqPost::getLastReplyDateline, CommonUtil.getLongTimestamp())
                    .setIncrBy(SqPost::getCommentNum, 1);
            SqPost sqPost = this.getById(postId);
            Boolean isSqPostNeedSetHot = CommonUtil.isSqPostNeedSetHot(postId.toString(), sqPost.getIsHot());
            if (isSqPostNeedSetHot) {
                lambdaUpdateWrapper.set(SqPost::getIsHot, 2);
            }
            //修改评论数
            this.update(lambdaUpdateWrapper);
        });
    }

    /**
     * 从富文本截取图片地址 逗号分隔保存到帖子表html_img字段
     */
    private void postImgHandle(SqPost sqPost) {
        String html = sqPost.getHtml();
        List<String> imgList = CommonUtil.getImgSrc(html);
        StringBuffer htmlImg = new StringBuffer();
        sqPost.setHtmlImg("");
        if (imgList != null && imgList.size() > 0) {
            for (String s : imgList) {
                htmlImg.append(OSSUtil.getImageKey(s, null)).append(",,");
            }
            if (htmlImg.length() > 0) {
                sqPost.setHtmlImg(htmlImg.substring(0, htmlImg.length() - 2));
            }
            //单张图片时 保存图片的宽和高
            if (imgList.size() == 1) {
                BufferedImage bufferedImage = getBufferedImage(imgList.get(0));
                if (bufferedImage != null) {
                    sqPost.setImgSize(bufferedImage.getWidth() + "," + bufferedImage.getHeight());
                }
            } else {
                sqPost.setImgSize("");
            }
        }
        if (html != null) {
            String goods = "<section[^>]*?>[\\s\\S]*?<\\/section>";
            String regex = "<[^>]*>";
            String content = html.replaceAll(goods, "").replaceAll(regex, "").replaceAll("&nbsp;", "").replaceAll("\\s*|\t|\r|\n", "");
            if (StrUtil.isNotBlank(content)) {
                sqPost.setContent(content);
            }
        }
    }

    /**
     * @param imgUrl 图片地址
     * @return
     */
    public static BufferedImage getBufferedImage(String imgUrl) {
        URL url = null;
        InputStream is = null;
        BufferedImage img = null;
        try {
            url = new URL(imgUrl);
            is = url.openStream();
            img = ImageIO.read(is);
        } catch (MalformedURLException e) {
        } catch (IOException e) {
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (IOException e) {
            }
        }
        return img;
    }
}
