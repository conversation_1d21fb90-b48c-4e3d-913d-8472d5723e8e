package com.zainanjing.shequ.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.shequ.domain.SqMessage;
import com.zainanjing.shequ.mapper.SqMessageMapper;
import com.zainanjing.shequ.service.ISqMessageService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 私信Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Service
public class SqMessageServiceImpl extends GenericCurdServiceImpl<SqMessageMapper, SqMessage>
        implements ISqMessageService {

    @Resource
    private SqMessageMapper sqMessageMapper;

    @Override
    public IPage<SqMessage> messagePage(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return sqMessageMapper.friendList(pagination, searchable.getFilterCdtns());
    }

}
