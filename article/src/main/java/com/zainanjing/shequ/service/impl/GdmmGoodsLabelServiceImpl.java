package com.zainanjing.shequ.service.impl;

import java.util.List;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zainanjing.shequ.mapper.GdmmGoodsLabelMapper;
import com.zainanjing.shequ.domain.GdmmGoodsLabel;
import com.zainanjing.shequ.service.IGdmmGoodsLabelService;

/**
 * 商品标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Service
public class GdmmGoodsLabelServiceImpl extends GenericCurdServiceImpl<GdmmGoodsLabelMapper, GdmmGoodsLabel>
        implements IGdmmGoodsLabelService
{

}
