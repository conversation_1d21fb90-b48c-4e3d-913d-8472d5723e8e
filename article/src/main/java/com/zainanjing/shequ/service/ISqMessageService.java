package com.zainanjing.shequ.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.shequ.domain.SqMessage;

/**
 * 私信Service接口
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
public interface ISqMessageService extends GenericCurdService<SqMessage> {


    IPage<SqMessage> messagePage(Searchable searchable);

}
