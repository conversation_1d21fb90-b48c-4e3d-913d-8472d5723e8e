package com.zainanjing.shequ.service.impl;

import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.shequ.domain.GdmmGoodsLabel;
import com.zainanjing.shequ.domain.GdmmUsers;
import com.zainanjing.shequ.domain.SqForum;
import com.zainanjing.shequ.domain.SqIndexForumUser;
import com.zainanjing.shequ.mapper.GdmmGoodsLabelMapper;
import com.zainanjing.shequ.mapper.SqForumMapper;
import com.zainanjing.shequ.service.IGdmmUsersService;
import com.zainanjing.shequ.service.ISqForumService;
import com.zainanjing.shequ.service.ISqIndexForumUserService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

/**
 * 社区板块数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Service
public class SqForumServiceImpl extends GenericCurdServiceImpl<SqForumMapper, SqForum>
        implements ISqForumService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ISqIndexForumUserService sqIndexForumUserService;

    @Resource
    private GdmmGoodsLabelMapper gdmmGoodsLabelMapper;

    @Resource
    private IGdmmUsersService gdmmUsersService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveForum(SqForum sqForum) {
        logger.info("sqForumService  保存forum开始：：");
        boolean postLabelFlag = false;
        boolean browseLabelFlag = false;
        //发帖所需标签
        if (CollUtil.isNotEmpty(sqForum.getLabelPost())) {
            sqForum.setIsLabelPost(1);
            postLabelFlag = true;
        }
        //浏览所需标签
        if (CollUtil.isNotEmpty(sqForum.getLabelBrowse())) {
            sqForum.setIsLabelBrowse(1);
            browseLabelFlag = true;
        }
        logger.info("sqForumService  保存forum");
        this.save(sqForum);
        logger.info("sqForumService  保存forum id=" + sqForum.getId());
        if (postLabelFlag) {
            logger.info("sqForumService  保存评论标签");
            for (Long labelId : sqForum.getLabelPost()) {
                GdmmGoodsLabel gdmmGoodsLabel = new GdmmGoodsLabel();
                gdmmGoodsLabel.setGoodsId(sqForum.getId());
                gdmmGoodsLabel.setLabelId(labelId);
                gdmmGoodsLabel.setStatus(1);
                gdmmGoodsLabel.setType(Integer.parseInt(Constants.LABEL_TYPE_SQFORUM_POST));
                gdmmGoodsLabelMapper.insert(gdmmGoodsLabel);
            }
        }

        if (browseLabelFlag) {
            logger.info("sqForumService  保存浏览标签");
            for (Long labelId : sqForum.getLabelBrowse()) {
                GdmmGoodsLabel gdmmGoodsLabel = new GdmmGoodsLabel();
                gdmmGoodsLabel.setGoodsId(sqForum.getId());
                gdmmGoodsLabel.setLabelId(labelId);
                gdmmGoodsLabel.setStatus(1);
                gdmmGoodsLabel.setType(Integer.parseInt(Constants.LABEL_TYPE_SQFORUM));
                gdmmGoodsLabelMapper.insert(gdmmGoodsLabel);
            }
        }

        //判断版主
        logger.info("sqForumService 根据版主判断是否添加");
        String forumUserNames = sqForum.getForumUserName();
        if (StrUtil.isNotBlank(forumUserNames)) {
            String[] mobilePhones = forumUserNames.split(",");
            if (mobilePhones != null && mobilePhones.length > 0) {
                //根据userNames查询出uids(改为根据手机号查询)
                gdmmUsersService.lambdaQuery().in(GdmmUsers::getMobilePhone, Arrays.asList(mobilePhones)).list().forEach(users -> {
                    SqIndexForumUser sifu = new SqIndexForumUser();
                    sifu.setForumId(sqForum.getId());
                    sifu.setUid(users.getUserId());
                    sqIndexForumUserService.save(sifu);
                });
            }
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateForum(SqForum sqForum) {
        logger.info("sqForumService  保存forum开始：：");
        boolean postLabelFlag = false;
        boolean browseLabelFlag = false;
        //发帖所需标签
        if (CollUtil.isNotEmpty(sqForum.getLabelPost())) {
            sqForum.setIsLabelPost(1);
            postLabelFlag = true;
        } else {
            sqForum.setIsLabelPost(0);
        }
        //浏览所需标签
        if (CollUtil.isNotEmpty(sqForum.getLabelBrowse())) {
            sqForum.setIsLabelBrowse(1);
            browseLabelFlag = true;
        } else {
            sqForum.setIsLabelBrowse(0);
        }

        logger.info("sqForumService  保存forum");
        this.updateById(sqForum);

        //先将之前所有的status=0
        LambdaUpdateWrapper<GdmmGoodsLabel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(GdmmGoodsLabel::getGoodsId, sqForum.getId());
        wrapper.eq(GdmmGoodsLabel::getType, Constants.LABEL_TYPE_SQFORUM_POST);
        wrapper.set(GdmmGoodsLabel::getStatus, 0);
        gdmmGoodsLabelMapper.update(wrapper);
        if (postLabelFlag) {
            //			  然后updateById  如果条数==0  则新增
            for (Long labelId : sqForum.getLabelPost()) {
                wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(GdmmGoodsLabel::getGoodsId, sqForum.getId());
                wrapper.eq(GdmmGoodsLabel::getType, Constants.LABEL_TYPE_SQFORUM_POST);
                wrapper.eq(GdmmGoodsLabel::getLabelId, labelId);
                wrapper.set(GdmmGoodsLabel::getStatus, 1);
                gdmmGoodsLabelMapper.update(wrapper);
                if (gdmmGoodsLabelMapper.update(wrapper) <= 0) {
                    //表示数据库中不存在这条数据
                    GdmmGoodsLabel egl = new GdmmGoodsLabel();
                    egl.setGoodsId(sqForum.getId());
                    egl.setLabelId(labelId);
                    egl.setStatus(1);
                    egl.setType(Integer.parseInt(Constants.LABEL_TYPE_SQFORUM_POST));
                    gdmmGoodsLabelMapper.insert(egl);
                }

            }
        }

        wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(GdmmGoodsLabel::getGoodsId, sqForum.getId());
        wrapper.eq(GdmmGoodsLabel::getType, Constants.LABEL_TYPE_SQFORUM);
        wrapper.set(GdmmGoodsLabel::getStatus, 0);
        gdmmGoodsLabelMapper.update(wrapper);
        if (browseLabelFlag) {

            for (Long labelId : sqForum.getLabelBrowse()) {
                wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(GdmmGoodsLabel::getGoodsId, sqForum.getId());
                wrapper.eq(GdmmGoodsLabel::getType, Constants.LABEL_TYPE_SQFORUM);
                wrapper.eq(GdmmGoodsLabel::getLabelId, labelId);
                wrapper.set(GdmmGoodsLabel::getStatus, 1);
                if (gdmmGoodsLabelMapper.update(wrapper) <= 0) {
                    //表示数据库中不存在这条数据
                    GdmmGoodsLabel egl = new GdmmGoodsLabel();
                    egl.setGoodsId(sqForum.getId());
                    egl.setLabelId(labelId);
                    egl.setStatus(1);
                    egl.setType(Integer.parseInt(Constants.LABEL_TYPE_SQFORUM));
                    gdmmGoodsLabelMapper.insert(egl);
                }
            }
        }
        //判断版主
        logger.info("sqForumService updateforum 更新版主");
        //首先 删除以前的所有记录
        sqIndexForumUserService.lambdaUpdate().eq(SqIndexForumUser::getForumId, sqForum.getId()).remove();

        String forumUserNames = sqForum.getForumUserName();
        if (StrUtil.isNotBlank(forumUserNames)) {
            String[] mobilePhones = forumUserNames.split(",");
            if (mobilePhones != null && mobilePhones.length > 0) {
                //根据userNames查询出uids(改为根据手机号查询)
                gdmmUsersService.lambdaQuery().in(GdmmUsers::getMobilePhone, mobilePhones).list().forEach(users -> {
                    SqIndexForumUser sifu = new SqIndexForumUser();
                    sifu.setForumId(sqForum.getId());
                    sifu.setUid(users.getUserId());
                    sqIndexForumUserService.save(sifu);
                });
            }
        }
        return true;
    }
}
