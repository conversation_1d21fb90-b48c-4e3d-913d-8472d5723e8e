package com.zainanjing.shequ.dto;

import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.shequ.domain.SqComment;
import com.zainanjing.shequ.domain.SqPost;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SqPostDTO extends SqPost {

    /**
     * 用户勋章等级
     */
    private Integer medalLevel;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 头像地址
     */
    private String imgUrl;

    /**
     * 头像类型
     */
    private Integer avatarType;

    /**
     * 论坛logo
     */
    private String forumLogo;

    /**
     * 用户浏览等级
     */
    private Integer userLevelBrowse;

    /**
     * 是否认证浏览
     */
    private Integer isCertificationBrowse;

    /**
     * 是否签到浏览
     */
    private Integer isSignInBrowse;

    /**
     * 用户发帖等级
     */
    private Integer userLevelPost;

    /**
     * 是否认证发帖
     */
    private Integer isCertificationPost;

    /**
     * 是否签到发帖
     */
    private Integer isSignInPost;

    /**
     * 是否标签浏览
     */
    private Integer isLabelBrowse;

    /**
     * 是否标签发帖
     */
    private Integer isLabelPost;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品标题
     */
    private String goodsTitle;

    /**
     * 商品缩略图
     */
    private String goodsThumbnail;

    /**
     * 商品市场价格
     */
    private Integer goodsMarketPrice;

    /**
     * 商品价格
     */
    private Integer goodsPrice;

    /**
     * 用户状态 0正常，1注销
     */
    private Integer userStatus;

    private Integer isHost;

    private Integer isPraise;

    private Integer isHtmlVideo;

    public Integer getIsHtmlVideo() {
        if (StrUtil.isNotEmpty(getHtml()) && getHtml().contains("<video")) {
            isHtmlVideo = Constants.SHI;
        } else {
            isHtmlVideo = Constants.FOU;
        }
        return isHtmlVideo;
    }

    //20250605富文本首个视频封面
    private String firstVideoImg;
    //20250605富文本首个视频封面宽
    private Integer firstVideoImgWidth;
    //20250605富文本首个视频封面高
    private Integer firstVideoImgHeight;

    //是否显示市场价
    private Integer showMarketPrice;

    //链接类型：1内链 2外链 0无链接
    private Integer linkType;
    //链接跳转的模块编号
    private Integer linkModule;
    //资源ID
    private Integer resourceId;
    //链接到：1分类列表页 2商品内容页
    private Integer linkTo;
    //外链地址
    private String linkUrl;

    private LinkObj linkObj;

    private List<SqComment> sqCommentList = new ArrayList<SqComment>();

    //是否评论 否0 是1
    private Integer hasComment;
}
