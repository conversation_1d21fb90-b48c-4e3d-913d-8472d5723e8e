package com.zainanjing.shequ.dto;

/**
 * 页面跳转类
 * <AUTHOR>
 *
 */
public class LinkObj implements java.io.Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8431194925413530739L;
	//链接类型：1内链 2外链 0无链接
	private Integer linkType;
	//链接跳转的模块编号
	private Integer linkModule;
	//资源ID
	private Integer resourceId;
	//链接到：1分类列表页 2商品内容页
	private Integer linkTo;
	//外链地址
	private String linkUrl;
	
	public LinkObj(Integer linkType, Integer linkModule, Integer resourceId,
                   Integer linkTo, String linkUrl) {
		this.linkType = linkType;
		this.linkModule = linkModule;
		this.resourceId = resourceId;
		this.linkTo = linkTo;
		this.linkUrl = linkUrl;
	}
	

	public LinkObj() {
	
	}


	public Integer getLinkType() {
		return linkType;
	}

	public void setLinkType(Integer linkType) {
		this.linkType = linkType;
	}

	public Integer getLinkModule() {
		return linkModule;
	}

	public void setLinkModule(Integer linkModule) {
		this.linkModule = linkModule;
	}

	public Integer getResourceId() {
		return resourceId;
	}

	public void setResourceId(Integer resourceId) {
		this.resourceId = resourceId;
	}

	public Integer getLinkTo() {
		return linkTo;
	}

	public void setLinkTo(Integer linkTo) {
		this.linkTo = linkTo;
	}

	public String getLinkUrl() {
		return linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	@Override
	public String toString() {
		return "LinkObj [linkType=" + linkType + ", linkModule=" + linkModule
				+ ", resourceId=" + resourceId + ", linkTo=" + linkTo
				+ ", linkUrl=" + linkUrl + "]";
	}
	
	
}
