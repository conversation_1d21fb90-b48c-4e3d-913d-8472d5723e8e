package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户角色关联对象 sq_index_users_role
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_index_users_role")
public class SqIndexUsersRole implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 角色id
     */
    private Integer roleId;

    @TableField(exist = false)
    private String roleName;

}
