package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 私信对象 sq_message
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_message")
public class SqMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 私信ID
     */
    @TableId
    private Long id;

    /**
     * 私信类型 1文字  2图片
     */
    private Integer type;

    /**
     * 私信内容
     */
    private String content;

    /**
     * 发件已读 1是0否
     */
    private Integer senderRead;

    /**
     * 收件已读 1是0否
     */
    private Integer receiverRead;

    /**
     * 发件删除 1是0否
     */
    private Integer senderDelete;

    /**
     * 收件删除 1是0否
     */
    private Integer receiverDelete;

    /**
     * 发件人uid
     */
    private Long sender;

    /**
     * 收件人uid
     */
    private Long receiver;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 发私信的两个人uid 号小的在前 100_101 用于统计
     */
    private String twoUid;

    /**
     * 0双方可见 , 1双方不可见, 如status=一方uid则 为该uid删除 不可见 另一方可见
     */
    private Integer status;


    @TableField(exist = false)
    private String friendImageUrl = "";//发消息对象的头像
    @TableField(exist = false)
    private String friendName = "";//发消息对象的昵称
    @TableField(exist = false)
    private String friendPhone = "";//发消息对象的手机号
    @TableField(exist = false)
    private Long friendUid = 0L;//发消息对象的uid

}
