package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色对象 sq_role
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@TableName(value = "sq_role")
public class SqRole implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @TableId
    private Integer id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 权限
     */
    private String authorities;

    /**
     * 是否是系统管理员 0普通权限 1系统超级管理员 2帖子超级管理员
     */
    private Integer isSystem;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

}
