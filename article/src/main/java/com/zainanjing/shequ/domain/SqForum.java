package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 社区板块数据对象 sq_forum
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_forum")
public class SqForum implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 板块ID
     */
    @TableId
    private Integer id;

    /**
     * 分区ID
     */
    private Integer areaId;

    /**
     * 板块名称
     */
    private String name;

    /**
     * 板块LOGO
     */
    private String logo;

    /**
     * 板块形象图
     */
    private String imgUrl;

    /**
     * 标题颜色
     */
    private String color;

    /**
     * 主题数量
     */
    private Long subjectNum;

    /**
     * 回复数量
     */
    private Long replyNum;

    /**
     * 今日发帖总数（含主题和回复）
     */
    private Long todayPostNum;

    /**
     * 是否推荐 1是0否
     */
    private Integer isRec;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否开启 1正常0关闭
     */
    private Integer isShow;

    /**
     * 主持人
     */
    private String userName;

    /**
     * 浏览等级
     */
    private Integer userLevelBrowse;

    /**
     * 浏览是否需要实名制 0否 1是
     */
    private Integer isCertificationBrowse;

    /**
     * 浏览是否需要签到 0否 1是
     */
    private Integer isSignInBrowse;

    /**
     * 发帖等级
     */
    private Integer userLevelPost;

    /**
     * 发帖是否需要实名制 0否 1是
     */
    private Integer isCertificationPost;

    /**
     * 发帖是否需要签到 0否 1是
     */
    private Integer isSignInPost;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 浏览需要标签，0不需要1需要
     */
    private Integer isLabelBrowse;

    /**
     * 发帖需要标签，0不需要1需要
     */
    private Integer isLabelPost;

    /**
     * 排序字段
     */
    private Long sort;

    /**
     * 链接类型 1内链 2外链 0无链接
     */
    private Integer linkType;

    /**
     * 跳转模块
     */
    private Integer linkModule;

    /**
     * 资源ID
     */
    private Integer resourceId;

    /**
     * 链接到,1分类列表页 2商品内容页
     */
    private Integer linkTo;

    /**
     * 外链URL
     */
    private String linkUrl;

    /**
     * 存放预览所需标签ids
     */
    @TableField(exist = false)
    private List<Long> labelBrowse = new ArrayList<>();

    /**
     * 存放发帖所需标签ids
     */
    @TableField(exist = false)
    private List<Long> labelPost  = new ArrayList<>();

    /**
     * 特殊字段，用于存放版主名
     */
    @TableField(exist = false)
    private String forumUserName;

    /**
     * 特殊字段，用于存放版主ids
     */
    @TableField(exist = false)
    private String forumUids;

}
