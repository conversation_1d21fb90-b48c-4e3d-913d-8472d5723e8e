package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 管理用户和会员对应关系对象 sq_index_manager_user
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@TableName(value ="sq_index_manager_user")
public class SqIndexManagerUser  implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 角色id */
    @TableId(type = IdType.INPUT)
    private Long managerId;

    /** 用户ID */
    private Long uid;

}
