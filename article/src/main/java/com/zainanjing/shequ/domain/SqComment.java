package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.zainanjing.article.domain.BcImg;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 评论对象 sq_comment
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_comment")
public class SqComment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评论自增ID
     */
    @TableId
    private Long id;

    /**
     * 帖子ID
     */
    private Long postId;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 20240118 回帖记录IP地址
     */
    private String ip;

    /**
     * 20240118 回帖记录地区
     */
    private String region;

    /**
     * 父评论ID
     */
    private Long parentId;

    /**
     * 1对帖子的评论 2对评论的评论
     */
    private Integer type;

    /**
     * 1对帖子的评论 2对评论的评论
     */
    private Integer level;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 状态 0正常（审核通过） 1用户删除 2管理员删除 3待审核 4审核拒绝
     */
    private Integer status;

    /**
     * 是否匿名 1是 0否
     */
    private Integer isAnonymous;

    /**
     * 版块ID
     */
    private Integer forumId;

    /**
     * 是否有未读回帖1是 0否
     */
    private Integer hasNewComment;

    /**
     * 楼层
     */
    private Long floor;

    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 审核时间
     */
    private Integer auditTime;


    @TableField(exist = false)
    private String userName;	//评论用户姓名	小海
    @TableField(exist = false)
    private Integer userStatus;  //20240110 用户状态 0正常  1注销
    @TableField(exist = false)
    private Integer medalLevel;	//用户等级
    @TableField(exist = false)
    private String imgUrl;//评论用户头像	http://mt.zainangj365.com
    @TableField(exist = false)
    private Integer avatarType;//0表示php那边的图片   1表示
    @TableField(exist = false)
    private String forumName;//版块名称
    @TableField(exist = false)
    private Integer isHost;//是否主持人评论  1.是 0否
    @TableField(exist = false)
    private Integer replyId;//我回复的帖子/评论的id 或  我被评论的的帖子/评论id
    @TableField(exist = false)
    private String replyContent;//我回复的帖子/评论的内容 或 我被评论的的帖子/评论内容
    @TableField(exist = false)
    private List<SqComment> sqCommentList = new ArrayList<SqComment>();
    @TableField(exist = false)
    private List <BcImg>bcImgList = new ArrayList<BcImg>();
    @TableField(exist = false)
    private Integer sonCommentNum = 0;
    @TableField(exist = false)
    private Integer isStar;//帖子是否加星 1.是 0否
    @TableField(exist = false)
    private String postSubject;//帖子主题
    @TableField(exist = false)
    private String postContent;//帖子内容
    @TableField(exist = false)
    private Long postCreateTime;
    @TableField(exist = false)
    private String postUserName;
    @TableField(exist = false)
    private Integer clickNum;
    @TableField(exist = false)
    private Integer commentNum;

    //20250416点赞数
    @TableField(exist = false)
    private Long praiseNum;
    //当前用户是否点赞
    @TableField(exist = false)
    private Integer isPraise;


}
