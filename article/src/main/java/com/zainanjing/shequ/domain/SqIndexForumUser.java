package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 板块用户关联数据对象 sq_index_forum_user
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_index_forum_user")
public class SqIndexForumUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 板块ID
     */
    private Integer forumId;

}
