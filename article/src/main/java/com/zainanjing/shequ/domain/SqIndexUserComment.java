package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 社区版主收藏评论关系对象 sq_index_user_comment
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_index_user_comment")
public class SqIndexUserComment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 版主id
     */
    @TableId(type = IdType.INPUT)
    private Integer uid;

    /**
     * 评论id
     */
    private Long commentId;

}
