package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.zainanjing.article.domain.BcImg;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 帖子数据对象 sq_post
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_post")
public class SqPost implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 帖子ID
     */
    @TableId
    private Long id;

    /**
     * 版块ID
     */
    private Integer forumId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 标题
     */
    private String subject;

    /**
     * 内容
     */
    private String content;

    /**
     * 20240118 发帖记录IP地址
     */
    private String ip;

    /**
     * 20240118 发帖记录地区
     */
    private String region;

    /**
     * 标题颜色
     */
    private String color;

    /**
     * 用户ID
     */
    private Long uid;



    /**
     * 初始浏览量
     */
    private Long initClickNum;


    /**
     * 点赞数
     */
    private Long praiseNum;

    /**
     * 1普通帖 2主持人帖
     */
    private Integer type;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否精华，0:普通;1:推荐;2:精华;3:广告;
     */
    private Integer isRec;

    /**
     * 是否置顶 1是0否
     */
    private Integer isTop;

    /**
     * 是否匿名 1是 0否
     */
    private Integer isAnonymous;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 查看等级 1版主可见 0都可见
     */
    private Integer seeLevel;

    /**
     * 是否允许回复 1不可以 0可以
     */
    private Integer canReply;

    /**
     * 状态 0正常（审核通过） 1用户删除 2管理员删除 3帖子超级管理员删除 4待审核 5审核拒绝
     */
    private Integer status;

    /**
     * 是否有未读回帖1是 0否
     */
    private Integer hasNewComment;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 视频图片
     */
    private String videoImg;

    /**
     * 置顶类型,1本讨论版  2 本分区 3 全局
     */
    private Integer topType;

    /**
     * 最后回帖时间戳 排序用
     */
    private Integer lastReplyDateline;

    /**
     * 是否有隐藏内容 1是 0否
     */
    private Integer haveHidden;

    /**
     * 隐藏内容
     */
    private String hiddenContent;

    /**
     * 是否热点，0:否;1是(手动设置);2是(自动设置)
     */
    private Integer isHot;

    /**
     * 是否在社区首页显示 0显示，1不显示，默认显示
     */
    private Integer isShowIndex;

    /**
     * 特殊内容
     */
    private String html;

    /**
     * 标记类型 0:普通 1:分享 2:求助 3:欣赏 4:投票
     */
    private Integer attribute;

    /**
     * 富文本中的图片 逗号分隔
     */
    private String htmlImg;

    /**
     * 单张图片时的图片宽和高 逗号分隔
     */
    private String imgSize;

    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 审核时间
     */
    private Integer auditTime;

    /**
     * 板块名称
     */
    @TableField(exist = false)
    private String forumName;

    /**
     * 用户名(用于后台展示userName全名)
     */
    @TableField(exist = false)
    private String userName;


    /**
     * 浏览量
     */
    private Long clickNum;


    /**
     * 评论数
     */
    private Long commentNum;


    /**
     * 发帖时间
     */

    private Integer createTime;
    @TableField(exist = false)
    private String createTimeStr;

    /**
     * 帖子是否加星 1.是 0否
     */
    @TableField(exist = false)
    private Integer isStar;


    @TableField(exist = false)
    private List<BcImg> bcImgList;

}
