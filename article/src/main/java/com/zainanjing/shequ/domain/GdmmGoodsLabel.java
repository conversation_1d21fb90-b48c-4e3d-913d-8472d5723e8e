package com.zainanjing.shequ.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商品标签对象 gdmm_goods_label
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@TableName(value ="gdmm_goods_label")
public class GdmmGoodsLabel  implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 商品id(活动id) */
    private Integer goodsId;

    /** 商品类型:1是精选，2是实惠 , 3活动,4浏览板块 5板块发帖 */
    private Integer type;

    /** 标签id */
    private Long labelId;

    /** 标签状态：1是可用，0是不可用 */
    private Integer status;

}
