package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 点赞数据对象 sq_praise
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_praise")
public class SqPraise implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 帖子ID
     */
    private Integer postId;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 帖子状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}
