package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户信息对象 gdmm_users
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@TableName(value = "gdmm_users")
public class GdmmUsers implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 20240104 新增用户状态 0正常  1注销
     */
    private Integer status;

    private String mobilePhone;

    @TableField
    private Integer medalLevel;	//用户等级

    @TableField(exist = false)
    private Long postNum;

    @TableField(exist = false)
    private Long commentNum;

    @TableField(exist = false)
    private Integer roleId;

    @TableField(exist = false)
    private String roleName;

    @TableField(exist = false)
    private Integer avatarType;

    @TableField(exist = false)
    private String imageUrl;

}
