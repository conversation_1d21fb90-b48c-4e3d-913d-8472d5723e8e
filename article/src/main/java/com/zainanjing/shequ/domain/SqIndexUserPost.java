package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 社区版主收藏帖子关系对象 sq_index_user_post
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Data
@TableName(value = "sq_index_user_post")
public class SqIndexUserPost implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 版主id
     */
    private Integer uid;

    /**
     * 帖子id
     */
    private Long postId;

}
