package com.zainanjing.article.event;

import com.ruoyi.common.dto.EventAction;
import com.zainanjing.article.domain.GdmmArticle;
import org.springframework.context.ApplicationEvent;

public class ArticleEvent extends ApplicationEvent {

    private GdmmArticle article;
    private EventAction action;

    public ArticleEvent(Object source, GdmmArticle article, EventAction action) {
        super(source);
        this.article = article;
        this.action = action;
    }

    public GdmmArticle getArticle() {
        return article;
    }

    public EventAction getAction() {
        return action;
    }
}