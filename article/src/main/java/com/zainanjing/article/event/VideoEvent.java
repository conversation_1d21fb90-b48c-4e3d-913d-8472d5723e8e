package com.zainanjing.article.event;

import com.ruoyi.common.dto.EventAction;
import com.zainanjing.article.domain.GdmmVideo;
import org.springframework.context.ApplicationEvent;

public class VideoEvent extends ApplicationEvent {

    private GdmmVideo video;
    private EventAction action;

    public VideoEvent(Object source, GdmmVideo video, EventAction action) {
        super(source);
        this.video = video;
        this.action = action;
    }

    public GdmmVideo getArticle() {
        return video;
    }

    public EventAction getAction() {
        return action;
    }
}