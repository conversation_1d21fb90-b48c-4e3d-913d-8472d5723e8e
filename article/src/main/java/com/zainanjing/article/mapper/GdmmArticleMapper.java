package com.zainanjing.article.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.article.domain.GdmmArticle;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 新闻Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-18
 */

public interface GdmmArticleMapper extends GenericMapper<GdmmArticle> {

    IPage<GdmmArticle> list(IPage<GdmmArticle> page, @Param("param") Map<String, Object> params);

}
