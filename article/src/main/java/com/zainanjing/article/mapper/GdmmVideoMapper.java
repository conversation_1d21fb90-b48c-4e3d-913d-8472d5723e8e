package com.zainanjing.article.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.article.domain.GdmmVideo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 短视频Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-20
 */

public interface GdmmVideoMapper extends GenericMapper<GdmmVideo> {
    IPage<GdmmVideo> search(IPage<GdmmVideo> page, @Param("param") Map<String, Object> params);

    void redis2db(Map<String, Long> map);

    Long auditCount(@Param("param") Map<String, Object> params);
}
