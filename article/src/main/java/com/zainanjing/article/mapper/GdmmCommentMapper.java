package com.zainanjing.article.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.article.domain.GdmmComment;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 通用评论Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */

public interface GdmmCommentMapper extends GenericMapper<GdmmComment> {
    IPage<GdmmComment> selectGdmmCommentList(IPage<GdmmComment> page, @Param("param") Map<String, Object> params);


    Integer updateByMap(Map<String, Object> params);

    Integer count(@Param("param") Map<String, Object> params);
}
