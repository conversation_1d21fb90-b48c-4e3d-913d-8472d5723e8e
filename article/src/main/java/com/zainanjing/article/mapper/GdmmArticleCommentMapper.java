package com.zainanjing.article.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.article.domain.GdmmArticleComment;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 资讯评论Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */

public interface GdmmArticleCommentMapper extends GenericMapper<GdmmArticleComment> {
    IPage<GdmmArticleComment> selectGdmmArticleCommentList(IPage<GdmmArticleComment> page, @Param("param") Map<String, Object> params);
}
