package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 系统参数对象 gdmm_params
 *
 * <AUTHOR>
 * @date 2023-10-20
 */
@TableName(value = "gdmm_params")
@Data
public class GdmmParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 模块
     */
    private String module;

    /**
     * 与module 联合主键
     */
    private String code;

    /**
     * 具体内容
     */
    private String value;

    /**
     * 描述：说明使用的位置
     */
    private String remark;

    /**
     * 0显示  1删除
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 修改时间
     */
    private Integer updateTime;

    /**
     * 显示内容
     */
    private String display;

    /**
     * 是否对接口开放,0不开放  1开放   不开放的意思不让接口可以调到，程序内部使用
     */
    private Integer isOpen;

    /**
     * 是否是扩展:0否 1是. 如果是 表示此条数据不需要在后台帮助菜单维护
     */
    private Integer isExtend;


}
