package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 新闻图文直播对象 gdmm_article_live
 *
 * <AUTHOR>
 * @date 2023-11-26
 */
@TableName(value = "gdmm_article_live")
public class GdmmArticleLive implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 图文直播ID
     */
    @TableId
    private Integer id;

    /**
     * 新闻ID
     */
    private Integer articleId;

    /**
     * 内容
     */
    private String content;

    /**
     * 发帖时间
     */
    private Integer createTime;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    /**
     * 图片
     */
    private String imgUrl;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setArticleId(Integer articleId) {
        this.articleId = articleId;
    }

    public Integer getArticleId() {
        return articleId;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateTime() {
        return createTime;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("articleId", getArticleId())
                .append("content", getContent())
                .append("createTime", getCreateTime())
                .append("status", getStatus())
                .append("imgUrl", getImgUrl())
                .toString();
    }
}
