package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 帖子评论图片对象 bc_img
 *
 * <AUTHOR>
 * @date 2023-11-26
 */
@TableName(value = "bc_img")
@Data
public class BcImg implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId
    private Long id;

    /**
     * 帖子id
     */
    private Long postId;

    /**
     * 评论id
     */
    private Long commentId;

    /**
     * 1帖子 2评论
     */
    private Long type;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 高度
     */
    private Integer height;

    /**
     * 排序
     */
    private Integer sort;

}
