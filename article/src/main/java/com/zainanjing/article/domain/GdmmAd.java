package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 广告对象 gdmm_ad
 *
 * <AUTHOR>
 * @date 2023-11-05
 */
@TableName(value = "gdmm_ad")
public class GdmmAd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 广告位置id
     */
    private Long positionId;

    /**
     * 原 ecs_ad.positionid 迁移
     */
    private Integer positionCode;

    /**
     * 图片路径
     */
    private String imgUrl;

    /**
     * 广告类型 0图片1flash2代码3文字
     */
    private Integer mediaType;

    /**
     * 广告标题
     */
    private String name;

    /**
     * 标题
     */
    private String title;

    /**
     * 链接
     */
    private String linkUrl;

    /**
     * 原始id（微信小程序）
     */
    private String rawId;

    /**
     * 跳转路径（微信小程序）
     */
    private String skipUrl;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 0开启1关闭
     */
    private Integer enabled;

    /**
     * 0内链1外链
     */
    private Integer linkType;

    /**
     * 链接跳转的模块id
     */
    private Integer linkModule;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 是否连接
     */
    private Integer isLink;

    /**
     * 连接名称
     */
    private String linkName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 跳转到1分类列表2资源详情页
     */
    private Integer linkTo;

    /**
     * 专区ids
     */
    private String areaIds;

    /**
     * 资讯类型id 以逗号分隔
     */
    private String articleCats;

    /**
     * 广告显示的行数
     */
    private Integer lineNum;

    /**
     * 广告显示时间，签到广告和启动广告专用
     */
    private Integer showTime;

    /**
     * 是否答题，0否 1是 默认不答题
     */
    private Integer isAnswer;

    /**
     * 题目
     */
    private String questions;

    /**
     * 选项1
     */
    private String option1;

    /**
     * 选项2
     */
    private String option2;

    /**
     * 正确答案，1 选项1 2 选项2  0 没有
     */
    private Integer answer;

    /**
     * 手机类型
     */
    private String clienttype;

    /**
     * 广告类型 热点0 全局1  分区2  板块3（社区）2是3的父类 这里可扩展其他模块
     */
    private Integer type;

    /**
     * 广告配的资源的id type=3时对应的 这里是板块id
     */
    private Long foreignId;

    /**
     * 广告配的资源的父类id type=2时对应的 这里是分区id
     */
    private Long foreignParentId;

    /**
     * 查看次数
     */
    private Long viewed;

    /**
     * 备用参数字段
     */
    private String resourceParamIds;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionCode(Integer positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getPositionCode() {
        return positionCode;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setMediaType(Integer mediaType) {
        this.mediaType = mediaType;
    }

    public Integer getMediaType() {
        return mediaType;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setRawId(String rawId) {
        this.rawId = rawId;
    }

    public String getRawId() {
        return rawId;
    }

    public void setSkipUrl(String skipUrl) {
        this.skipUrl = skipUrl;
    }

    public String getSkipUrl() {
        return skipUrl;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setLinkType(Integer linkType) {
        this.linkType = linkType;
    }

    public Integer getLinkType() {
        return linkType;
    }

    public void setLinkModule(Integer linkModule) {
        this.linkModule = linkModule;
    }

    public Integer getLinkModule() {
        return linkModule;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setIsLink(Integer isLink) {
        this.isLink = isLink;
    }

    public Integer getIsLink() {
        return isLink;
    }

    public void setLinkName(String linkName) {
        this.linkName = linkName;
    }

    public String getLinkName() {
        return linkName;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public void setLinkTo(Integer linkTo) {
        this.linkTo = linkTo;
    }

    public Integer getLinkTo() {
        return linkTo;
    }

    public void setAreaIds(String areaIds) {
        this.areaIds = areaIds;
    }

    public String getAreaIds() {
        return areaIds;
    }

    public void setArticleCats(String articleCats) {
        this.articleCats = articleCats;
    }

    public String getArticleCats() {
        return articleCats;
    }

    public void setLineNum(Integer lineNum) {
        this.lineNum = lineNum;
    }

    public Integer getLineNum() {
        return lineNum;
    }

    public void setShowTime(Integer showTime) {
        this.showTime = showTime;
    }

    public Integer getShowTime() {
        return showTime;
    }

    public void setIsAnswer(Integer isAnswer) {
        this.isAnswer = isAnswer;
    }

    public Integer getIsAnswer() {
        return isAnswer;
    }

    public void setQuestions(String questions) {
        this.questions = questions;
    }

    public String getQuestions() {
        return questions;
    }

    public void setOption1(String option1) {
        this.option1 = option1;
    }

    public String getOption1() {
        return option1;
    }

    public void setOption2(String option2) {
        this.option2 = option2;
    }

    public String getOption2() {
        return option2;
    }

    public void setAnswer(Integer answer) {
        this.answer = answer;
    }

    public Integer getAnswer() {
        return answer;
    }

    public void setClienttype(String clienttype) {
        this.clienttype = clienttype;
    }

    public String getClienttype() {
        return clienttype;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setForeignId(Long foreignId) {
        this.foreignId = foreignId;
    }

    public Long getForeignId() {
        return foreignId;
    }

    public void setForeignParentId(Long foreignParentId) {
        this.foreignParentId = foreignParentId;
    }

    public Long getForeignParentId() {
        return foreignParentId;
    }

    public void setViewed(Long viewed) {
        this.viewed = viewed;
    }

    public Long getViewed() {
        return viewed;
    }

    public void setResourceParamIds(String resourceParamIds) {
        this.resourceParamIds = resourceParamIds;
    }

    public String getResourceParamIds() {
        return resourceParamIds;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("positionId", getPositionId())
                .append("positionCode", getPositionCode())
                .append("imgUrl", getImgUrl())
                .append("mediaType", getMediaType())
                .append("name", getName())
                .append("title", getTitle())
                .append("linkUrl", getLinkUrl())
                .append("rawId", getRawId())
                .append("skipUrl", getSkipUrl())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("enabled", getEnabled())
                .append("linkType", getLinkType())
                .append("linkModule", getLinkModule())
                .append("resourceId", getResourceId())
                .append("isLink", getIsLink())
                .append("linkName", getLinkName())
                .append("sort", getSort())
                .append("linkTo", getLinkTo())
                .append("areaIds", getAreaIds())
                .append("articleCats", getArticleCats())
                .append("lineNum", getLineNum())
                .append("showTime", getShowTime())
                .append("isAnswer", getIsAnswer())
                .append("questions", getQuestions())
                .append("option1", getOption1())
                .append("option2", getOption2())
                .append("answer", getAnswer())
                .append("clienttype", getClienttype())
                .append("type", getType())
                .append("foreignId", getForeignId())
                .append("foreignParentId", getForeignParentId())
                .append("viewed", getViewed())
                .append("resourceParamIds", getResourceParamIds())
                .toString();
    }
}
