package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 数据字典对象 gdmm_dictionary
 *
 * <AUTHOR>
 * @date 2023-10-19
 */
@Data
@TableName(value = "gdmm_dictionary")
public class GdmmDictionary implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 字典模块
     */
    private String module;

    /**
     * 字典码
     */
    private String code;

    /**
     * 字典值
     */
    private String value;

    /**
     * 字典名称
     */
    private String name;

    /**
     * 图片url 有的字典有图片
     */
    private String imgUrl;

    /**
     * 0显示  1删除  2不开启
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * gdmmDictionary.setModuleName(enumObj.getModuleName());
     * gdmmDictionary.setCodeName(enumObj.getCodeName());
     * gdmmDictionary.setIsImage(enumObj.getIsImage());
     * gdmmDictionary.setWidth(enumObj.getWidth());
     * gdmmDictionary.setHeight(enumObj.getHeight());
     * gdmmDictionary.setIsValue(enumObj.getIsValue());
     */

    @TableField(exist = false)
    private String moduleName;

    @TableField(exist = false)
    private String codeName;

    @TableField(exist = false)
    private Integer isImage;

    @TableField(exist = false)
    private String width;

    @TableField(exist = false)
    private String height;

    @TableField(exist = false)
    private Integer isValue;

}
