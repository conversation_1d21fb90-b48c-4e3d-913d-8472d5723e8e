package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 新闻对象 gdmm_article
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@TableName(value = "gdmm_article")
@Data
public class GdmmArticle implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 分类id
     */
    private Long articleCatId;

    /**
     * 1普通新闻，2图文直播，3专题新闻
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 合肥资讯预览显示二维码 20230926
     */
    private String qrCodeUrl;

    /**
     * 作者
     */
    private String author;

    /**
     * 作者邮箱
     */
    private String authorEmail;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 0后台插入,中搜新闻通过定时任务入库
     */
    private Integer articleType;

    /**
     * 状态：0显示(审核通过）1删除  2草稿  3待审核 4审核拒绝
     */
    private Integer status;

    /**
     * 是否开启 0开启1关闭
     */
    private Integer enabled;

    /**
     * 是否开启热门 0开启 1关闭
     */
    private Integer isHot;

    /**
     * 描述
     */
    private String remark;

    /**
     * 新闻列表图片
     */
    private String imgUrl;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 视频默认图片
     */
    private String videoImgUrl;

    /**
     * 视频播放时长秒数
     */
    private Integer videoSecond;

    /**
     * 中搜新闻对应的id
     */
    private String idForChinaSearch;

    /**
     * 拉取的西宁新闻里的时间戳 用于调西宁新闻接口过滤数据
     */
    private Long timeForChinaSearch;

    /**
     * 资讯URL(中搜详情等)
     */
    private String articleDetailUrl;

    /**
     * 第三方新闻的id
     */
    private Long idForThird;

    /**
     * 查看次数
     */
    private Long viewed;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否高亮显示 1是 0否
     */
    private Integer isHighLight;

    /**
     * 添加时间
     */
    private Integer createTime;

    /**
     * 显示模式 0无图 1普通 2多图 3单图
     */
    private Integer displayMode;

    /**
     * 资讯来源id
     */
    private Integer sourceId;

    /**
     * 第三方新闻对应的来源名称
     */
    private String sourceNameForThird;

    /**
     * 资讯标签id 逗号分隔
     */
    private String labelId;

    /**
     * 是否设为通告 0否 1是
     */
    private Integer isNotice;

    /**
     * 是否开启评论 0否 1是
     */
    private Integer isComment;

    /**
     * 资讯编辑
     */
    private String editor;

    private Integer guilinType = 0;

    private String guilinId; //桂林id 主要用于唯一索引

    private String guilinJson;

    /**
     * 资讯记者
     */
    private String reporter;

    /**
     * 所属专题新闻的id 为0时不属于任何专题
     */
    private Long subjectArticleId;

    /**
     * 专题分类id
     */
    private Integer subjectCatId;

    /**
     * 专题id
     */
    private Integer subjectId;

    /**
     * 是否推荐主页 0不推荐  1推荐
     */
    private Integer recommendHome;

    /**
     * 初始点击量，加上真实点击量后在前台展示
     */
    private Long initView;

    /**
     * 点击量倍数
     */
    private Integer viewTimes;

    /**
     * 管理员id 关联管理员表id
     */
    private Long managerId;

    /**
     * 评分
     */
    private Integer score;

    /**
     * 打开方式 0内容 1外链
     */
    private Integer openType;

    /**
     * 外链地址
     */
    private String linkUrl;

    /**
     * 卡片id
     */
    private Integer cardId;

    //发布类型 0立即发布 1定时发布
    private Integer publishStatus;

    //发布时间
    private Integer publishTime;


    /**
     * 标题字号
     */
    private Integer fontSize;


    @TableField(exist = false)
    private String catName;

    @TableField(exist = false)
    private String source;

    @TableField(exist = false)
    private String label;

    @TableField(exist = false)
    private Long commentNum;

    @TableField(exist = false)
    private String createTimeS;

}
