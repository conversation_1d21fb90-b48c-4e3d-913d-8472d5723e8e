package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 新闻专题对象 gdmm_article_subject
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@TableName(value = "gdmm_article_subject")
public class GdmmArticleSubject implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 简介
     */
    private String remark;

    /**
     * 类型 0新闻 1短视频
     */
    private Integer type;

    /**
     * 封面图
     */
    private String imgUrl;

    /**
     * 0显示 1隐藏
     */
    private Integer enabled;

    /**
     * 来源id
     */
    private Integer sourceId;

    /**
     * 是否推荐主页 0不推荐  1推荐
     */
    private Integer recommendHome;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 管理员id 关联管理员表id
     */
    private Integer managerId;

    /**
     * 状态 0正常 1删除
     */
    private Integer status;

    /**
     * 卡片id
     */
    private Integer cardId;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setRecommendHome(Integer recommendHome) {
        this.recommendHome = recommendHome;
    }

    public Integer getRecommendHome() {
        return recommendHome;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateTime() {
        return createTime;
    }

    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUpdateTime() {
        return updateTime;
    }

    public void setManagerId(Integer managerId) {
        this.managerId = managerId;
    }

    public Integer getManagerId() {
        return managerId;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public Integer getCardId() {
        return cardId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("remark", getRemark())
                .append("type", getType())
                .append("imgUrl", getImgUrl())
                .append("enabled", getEnabled())
                .append("sourceId", getSourceId())
                .append("recommendHome", getRecommendHome())
                .append("sort", getSort())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("managerId", getManagerId())
                .append("status", getStatus())
                .append("cardId", getCardId())
                .toString();
    }
}
