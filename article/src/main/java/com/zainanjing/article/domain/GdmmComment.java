package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通用评论对象 gdmm_comment
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@TableName(value = "gdmm_comment")
@Data
public class GdmmComment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId
    private Long id;

    /**
     * 资源ID
     */
    private Integer resId;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论层级 1父评论 2子评论
     */
    private Integer type;

    /**
     * 资源类型 0短视频 1红云视频直播 2红云电视直播
     */
    private Long resType;

    /**
     * 楼层
     */
    private Long floor;

    /**
     * type为2时 父评论ID
     */
    private Long parentId;

    /**
     * 评论时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态 0正常（审核通过） 1待审核 2管理员删除 3审核拒绝
     */
    private Integer status;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date auditTime;

    /**
     * 审核人
     */
    private Long auditUid;

    /**
     * 是否匿名 1是 0否
     */
    private Integer isAnonymous;

    /**
     * 红云返回主键 msg_id 主键
     */
    private String msgId;

    /**
     * 红云的消息类型
     */
    private String msgType;

    /**
     * 审核拒绝原因
     */
    private String auditRejectReason;

    private String ip;

    private String region;

    @TableField(exist = false)
    private String title;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private String mobilePhone;

    @TableField(exist = false)
    private String medalLevel;

    @TableField(exist = false)
    private String imgUrl;
}
