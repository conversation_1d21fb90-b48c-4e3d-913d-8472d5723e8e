package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 广电报对象 gdmm_paper
 *
 * <AUTHOR>
 * @date 2023-10-19
 */
@TableName(value = "gdmm_paper")
public class GdmmPaper implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图
     */
    private String imgUrl;

    /**
     * 外链地址
     */
    private String linkUrl;

    /**
     * 0显示 1隐藏
     */
    private Integer enabled;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 管理员id 关联管理员表id
     */
    private Integer managerId;

    /**
     * 状态 0正常 1删除
     */
    private Integer status;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateTime() {
        return createTime;
    }

    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUpdateTime() {
        return updateTime;
    }

    public void setManagerId(Integer managerId) {
        this.managerId = managerId;
    }

    public Integer getManagerId() {
        return managerId;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("imgUrl", getImgUrl())
                .append("linkUrl", getLinkUrl())
                .append("enabled", getEnabled())
                .append("sort", getSort())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("managerId", getManagerId())
                .append("status", getStatus())
                .toString();
    }
}
