package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位置对象 gdmm_ad_position
 *
 * <AUTHOR>
 * @date 2023-11-05
 */
@TableName(value = "gdmm_ad_position")
@Data
public class GdmmAdPosition implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 广告位置上级ID
     */
    private Long parentId;

    /**
     * 广告位置名称
     */
    private String name;

    /**
     * 图片宽度
     */
    private Integer width;

    /**
     * 高度
     */
    private Integer height;

    /**
     * 图片张数
     */
    private Integer num;

    /**
     * 广告位置code 唯一
     */
    private String code;

    /**
     * 广告位置module
     */
    private String module;

    /**
     * 客户端是否可关闭 0不可以 1可以
     */
    private Integer enabled;

    @TableField(exist = false)
    private Integer mediaType;

    /**
     * 资源id
     */
    private String resourceId;
}
