package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 敏感词对象 gdmm_special_word
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@TableName(value = "gdmm_special_word")
public class GdmmSpecialWord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 0敏感词 1注册保留字
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

}
