package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 资讯评论对象 gdmm_article_comment
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@TableName(value = "gdmm_article_comment")
@Data
public class GdmmArticleComment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 资讯评论自增ID
     */
    @TableId
    private Long id;

    /**
     * 资讯ID
     */
    private Integer articleId;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 1评论资讯 2评论评论
     */
    private Integer type;

    /**
     * type为2时 上级评论ID
     */
    private Long parentId;

    /**
     * 评论时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态 0正常（审核通过）1待审核 2管理员删除
     */
    private Integer status;

    @TableField(exist = false)
    private String title;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private String mobilePhone;

    @TableField(exist = false)
    private String medalLevel;

    @TableField(exist = false)
    private String imgUrl;
}
