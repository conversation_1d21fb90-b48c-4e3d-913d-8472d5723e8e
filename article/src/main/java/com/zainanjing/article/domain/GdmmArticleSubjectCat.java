package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 新闻专题分类对象 gdmm_article_subject_cat
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@TableName(value = "gdmm_article_subject_cat")
public class GdmmArticleSubjectCat implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 新闻专题分类名称
     */
    private String name;

    /**
     * 是否开启 0开启1关闭
     */
    private Integer enabled;

    /**
     * 新闻专题id
     */
    private Integer articleSubjectId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 0正常  1删除
     */
    private Integer status;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setArticleSubjectId(Integer articleSubjectId) {
        this.articleSubjectId = articleSubjectId;
    }

    public Integer getArticleSubjectId() {
        return articleSubjectId;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("enabled", getEnabled())
                .append("articleSubjectId", getArticleSubjectId())
                .append("sort", getSort())
                .append("status", getStatus())
                .toString();
    }
}
