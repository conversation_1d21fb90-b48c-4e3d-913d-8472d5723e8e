package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 短视频对象 gdmm_video
 *
 * <AUTHOR>
 * @date 2023-10-20
 */
@Data
@TableName(value = "gdmm_video")
public class GdmmVideo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 分类id
     */
    private Integer catId;

    /**
     * 专题id
     */
    private Integer subjectId;

    /**
     * 专题分类id
     */
    private Integer subjectCatId;

    /**
     * 类型: 1 本地上传：上传视频文件, 2 外链：输入视频地址
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 查看次数
     */
    private Long viewed;

    /**
     * 初始点击量，加上真实点击量后在前台展示
     */
    private Long initView;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 点赞数
     */
    private Integer praiseNum;

    /**
     * 虚拟点赞数 init_praise_num
     */
    private Integer initPraiseNum;

    /**
     * 来源id
     */
    private Integer sourceId;

    /**
     * 作者
     */
    private String author;

    /**
     * 编辑
     */
    private String editor;

    /**
     * 内容
     */
    private String content;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 视频播放时长秒数
     */
    private Integer videoSecond;

    /**
     * 视频默认图片
     */
    private String videoImgUrl;

    /**
     * 视频图片宽度
     */
    private Integer videoImgWidth;

    /**
     * 视频图片高度
     */
    private Integer videoImgHeight;

    /**
     * 是否推荐 0不推荐  1推荐
     */
    private Integer isRecommend;

    /**
     * 是否开启评论 0否 1是
     */
    private Integer isComment;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 0显示  1删除  3待一审 4拒绝 8待二审 9待三审（和新闻一致）
     */
    private Integer status;

    /**
     * 是否开启 0开启1关闭
     */
    private Integer enabled;

    /**
     * 管理员id 关联管理员表id
     */
    private Long managerId;

    /**
     * 添加时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 观看所需标签
     */
    private Long labelId;

    /**
     * 第三方接入ID
     */
    private String idForThird;

    /**
     * 卡片id
     */
    private Integer cardId;

    //发布类型 0立即发布 1定时发布
    private Integer publishStatus;

    //发布时间
    private Integer publishTime;

    private String sourceNameForThird;

    @TableField(exist = false)
    private String catName;

    @TableField(exist = false)
    private String createTimeS;
}
