package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 频道管理对象 gdmm_home_menu
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@TableName(value = "gdmm_home_menu")
@Data
public class GdmmHomeMenu implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 上级菜单id
     */
    private Integer parentId;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单类型 1.推荐2.普通新闻3.短视频（短视频 纪录片）4.专题5.直播6.电视7.广播8.广电报
     */
    private Integer menuType;

    /**
     * 资源类型
     */
    private Integer resourceType;

    /**
     * 编码
     */
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    //打开方式0普通 1外链
    private Integer openType;

    //外链地址
    private String linkUrl;

    /**
     * 0开启 1关闭
     */
    private Integer enabled;

    /**
     * 0正常 1删除
     */
    private Integer status;

    /**
     * 0默认首页 1视听 2待扩展
     */
    private Integer location;


}
