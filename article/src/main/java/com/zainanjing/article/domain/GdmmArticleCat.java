package com.zainanjing.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.io.Serializable;

/**
 * 新闻分类对象 gdmm_article_cat
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@Data
@TableName(value = "gdmm_article_cat")
public class GdmmArticleCat implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 分类名称
     */
    private String catName;

    /**
     * 0:新闻 1:短视频
     */
    private Integer type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 0显示  1删除
     */
    private Integer status;

    /**
     * 是否开启 0开启1关闭
     */
    private Integer enabled;

    /**
     * 分类图片
     */
    private String imgUrl;

    /**
     * 中国搜索接口
     */
    private String chinasoApi;

    /**
     * 链接类型 （0 无链接1 内链 2 外链）
     */
    private Integer linkType;

    /**
     * 内链模块
     */
    private Integer linkModule;

    /**
     * 内链资源id
     */
    private Long resourceId;

    /**
     * 链接到,1分类列表页 2商品内容页
     */
    private Integer linkTo;

    /**
     * 外链地址
     */
    private String linkUrl;

    /**
     * 是否默认分类,0:非默认 1:默认
     */
    @TableField(value = "isDefault")
    private Integer isDefault;

    /**
     * 公众号名 用于新闻拉取
     */
    private String wechatName;


}
