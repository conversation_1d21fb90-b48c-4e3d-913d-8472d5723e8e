package com.zainanjing.article.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.article.domain.GdmmVideo;
import com.zainanjing.article.mapper.GdmmVideoMapper;
import com.zainanjing.article.service.IGdmmVideoService;
import com.zainanjing.common.util.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 短视频Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-20
 */
@Service

public class GdmmVideoServiceImpl extends GenericCurdServiceImpl<GdmmVideoMapper, GdmmVideo> implements IGdmmVideoService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public IPage<GdmmVideo> search(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return baseMapper.search(pagination, searchable.getFilterCdtns());
    }

    public void redis2dbVideo() {
        logger.error("redis 持久化短视频点击量开始");
        CommonUtil.redisCommonPut(RedisKey.VIDEO_IS_SYNC_CLICKNUM.getKey(), Constants.YES);
        Integer beginTime = CommonUtil.getTimestamp();
        //短视频
        Map<Long, Long> videoCommentNumMap = CommonUtil.getRedisByKeyword(Constants.SHORT_VIDEO_STR + "_" + Constants.COMMENTNUM + "_*");
        Map<Long, Long> videoPraiseNumMap = CommonUtil.getRedisByKeyword(RedisKey.VIDEO_PRAISE_NUM.getKey() + "_*");
        Map<Long, Long> videoClickNumMap = CommonUtil.getRedisByKeyword(RedisKey.VIDEO_CLICKNUM.getKey() + "_*");

        logger.error("redis 持久化短视频点击量.总数量是:" + videoClickNumMap.size());

        Map<String, Long> videoMap = new HashMap<String, Long>();
        for (Map.Entry<Long, Long> entry : videoClickNumMap.entrySet()) {
            Long id = entry.getKey();
            Long clickNum = entry.getValue();
            Long praiseNum = videoPraiseNumMap.get(id);
            if (praiseNum == null) {
                praiseNum = 0L;
            }
            Long commentNum = videoCommentNumMap.get(id);
            if (commentNum == null) {
                commentNum = 0L;
            }
            videoMap.clear();
            videoMap.put("id", id);
            videoMap.put("clickNum", clickNum);
            videoMap.put("praiseNum", praiseNum);
            videoMap.put("commentNum", commentNum);
            this.baseMapper.redis2db(videoMap);
        }
        CommonUtil.redisCommonPut(RedisKey.VIDEO_IS_SYNC_CLICKNUM.getKey(), Constants.NO);
        logger.error("redis 持久化短视频点击量结束，用时:" + (CommonUtil.getTimestamp() - beginTime) + "秒");
    }

}
