package com.zainanjing.article.service.impl;

import com.ruoyi.common.utils.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.article.domain.GdmmArticle;
import com.zainanjing.article.domain.GdmmArticleHis;
import com.zainanjing.article.mapper.GdmmArticleMapper;
import com.zainanjing.article.service.IGdmmArticleHisService;
import com.zainanjing.article.service.IGdmmArticleService;
import com.zainanjing.common.util.CommonUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 新闻Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@Service

public class GdmmArticleServiceImpl extends GenericCurdServiceImpl<GdmmArticleMapper, GdmmArticle>
        implements IGdmmArticleService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IGdmmArticleHisService gdmmArticleHisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateArticle(GdmmArticle gdmmArticle) {
        if (this.updateById(gdmmArticle)) {
            GdmmArticle old = this.getById(gdmmArticle.getId());
            GdmmArticleHis gdmmArticleHis = BeanUtil.copyProperties(old, GdmmArticleHis.class, "id");
            gdmmArticleHis.setArticleId(old.getId());
            gdmmArticleHis.setOperTime(new Date());
            gdmmArticleHis.setOperId(SecurityUtils.getUserId().toString());
            gdmmArticleHis.setOperName(SecurityUtils.getUsername());
            return gdmmArticleHisService.save(gdmmArticleHis);
        }
        return false;
    }

    @Override
    public IPage<GdmmArticle> list(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return baseMapper.list(pagination, searchable.getFilterCdtns());
    }

    @Override
    public void redis2dbArticle() {
        logger.error("redis 持久化新闻点击量开始");
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_SYNC_CLICKNUM.getKey(), Constants.YES);
        Integer beginTime = CommonUtil.getTimestamp();
        //获取redis的新闻点击量
        Map<Long, Long> articleClickNumMap = CommonUtil.getRedisByKeyword(Constants.ARTICLE + "_" + Constants.CLICKNUM + "_*");
        logger.error("redis 持久化新闻点击量.总数量是:" + articleClickNumMap.size());
        Map<String, Long> articleMap = new HashMap<String, Long>();
        if (!articleClickNumMap.isEmpty()) {
            for (Map.Entry<Long, Long> entry : articleClickNumMap.entrySet()) {
                Long id = entry.getKey();
                Long clickNum = entry.getValue();
                articleMap = new HashMap<String, Long>();
                articleMap.put("id", id);
                articleMap.put("clickNum", clickNum);
                try {
                    LambdaUpdateWrapper<GdmmArticle> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(GdmmArticle::getId, id).set(GdmmArticle::getViewed, clickNum);
                    this.update(updateWrapper);
                } catch (Exception e) {
                    logger.error("redis 持久化新闻点击量报错:新闻id=" + id);
                }
            }
        }
        CommonUtil.redisCommonPut(RedisKey.ARTICLE_IS_SYNC_CLICKNUM.getKey(), Constants.NO);
        logger.error("redis 持久化新闻点击量结束，用时:" + (CommonUtil.getTimestamp() - beginTime) + "秒");
    }


}
