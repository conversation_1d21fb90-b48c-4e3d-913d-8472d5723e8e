package com.zainanjing.article.service.impl;

import com.ruoyi.common.utils.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.article.domain.GdmmArticle;
import com.zainanjing.article.domain.GdmmArticleCat;
import com.zainanjing.article.mapper.GdmmArticleCatMapper;
import com.zainanjing.article.service.IGdmmArticleCatService;
import com.zainanjing.article.service.IGdmmArticleService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 新闻分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@Service

public class GdmmArticleCatServiceImpl extends GenericCurdServiceImpl<GdmmArticleCatMapper, GdmmArticleCat>
        implements IGdmmArticleCatService {

    private static final Logger logger = LoggerFactory.getLogger(GdmmArticleCatServiceImpl.class);

    @Resource
    private IGdmmArticleService iGdmmArticleService;

    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public boolean deleteArticleCat(List<Long> ids) {
        for (Long id : ids) {
            LambdaQueryWrapper<GdmmArticle> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GdmmArticle::getArticleCatId, id).eq(GdmmArticle::getEnabled, Constants.STATUS_NORMAL).eq(GdmmArticle::getStatus, Constants.STATUS_NORMAL);
            if (iGdmmArticleService.count(queryWrapper) > 0) {
                logger.error("后台删除新闻分类操作:新闻分类Id：" + id + "与在用新闻有关联,不能进行删除操作！！！");
                throw new ServiceException(501, "后台删除新闻分类操作:新闻分类Id：" + id + "与在用新闻有关联,不能进行删除操作！！！");
            }
        }
        LambdaUpdateWrapper<GdmmArticleCat> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GdmmArticleCat::getId, ids).set(GdmmArticleCat::getStatus, Constants.STATUS_DELETE);
        return this.update(updateWrapper);
    }


    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public boolean save(GdmmArticleCat entity) {
        this.changeDefault(entity);
        return this.baseMapper.insert(entity) > 0;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRED)
    public boolean update(GdmmArticleCat entity) {
        // 这里首先把默认是的去掉
        this.changeDefault(entity);
        return this.baseMapper.updateById(entity) > 0;
    }

    private void changeDefault(GdmmArticleCat entity) {
        if (entity.getIsDefault() != null && entity.getIsDefault() == 1) {
            // 为了不修改sqlMap里面的update 需要ids[]参数，这里先查后修改
            LambdaQueryWrapper<GdmmArticleCat> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(GdmmArticleCat::getId).eq(GdmmArticleCat::getIsDefault, 1).eq(GdmmArticleCat::getStatus, Constants.STATUS_NORMAL);
            List<GdmmArticleCat> list = this.list(queryWrapper);
            if (CollUtil.isNotEmpty(list)) {// 如果非当前修改的分类
                LambdaUpdateWrapper<GdmmArticleCat> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(GdmmArticleCat::getId, list.stream().map(x -> x.getId()).collect(Collectors.toList())).set(GdmmArticleCat::getIsDefault, 0);
                this.update(updateWrapper);
            }
        }
    }

}
