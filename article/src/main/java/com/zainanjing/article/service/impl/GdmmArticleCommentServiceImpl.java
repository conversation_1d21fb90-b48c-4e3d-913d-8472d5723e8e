package com.zainanjing.article.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.article.domain.GdmmArticleComment;
import com.zainanjing.article.mapper.GdmmArticleCommentMapper;
import com.zainanjing.article.service.IGdmmArticleCommentService;
import org.springframework.stereotype.Service;

/**
 * 资讯评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@Service

public class GdmmArticleCommentServiceImpl extends GenericCurdServiceImpl<GdmmArticleCommentMapper, GdmmArticleComment>
        implements IGdmmArticleCommentService {

    @Override
    public IPage<GdmmArticleComment> selectGdmmArticleCommentList(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return baseMapper.selectGdmmArticleCommentList(pagination, searchable.getFilterCdtns());
    }
}
