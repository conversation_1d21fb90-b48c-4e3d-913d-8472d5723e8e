package com.zainanjing.article.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.article.domain.GdmmArticleComment;

/**
 * 资讯评论Service接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public interface IGdmmArticleCommentService extends GenericCurdService<GdmmArticleComment> {

    IPage<GdmmArticleComment> selectGdmmArticleCommentList(Searchable searchable);

}
