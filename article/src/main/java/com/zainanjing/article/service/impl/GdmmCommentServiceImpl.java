package com.zainanjing.article.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.article.domain.GdmmComment;
import com.zainanjing.article.mapper.GdmmCommentMapper;
import com.zainanjing.article.service.IGdmmCommentService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 通用评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@Service

public class GdmmCommentServiceImpl extends GenericCurdServiceImpl<GdmmCommentMapper, GdmmComment>
        implements IGdmmCommentService {

    @Override
    public IPage<GdmmComment> selectGdmmCommentList(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return baseMapper.selectGdmmCommentList(pagination, searchable.getFilterCdtns());
    }

    @Override
    public Boolean updateByMap(Map<String, Object> params) {
        return baseMapper.updateByMap(params) > 0;
    }

    @Override
    public Integer count(Map<String, Object> params) {
        return baseMapper.count(params);
    }
}
