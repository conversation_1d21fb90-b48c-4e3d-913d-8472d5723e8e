package com.zainanjing.article.service.impl;

import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.zainanjing.article.domain.GdmmAd;
import com.zainanjing.article.mapper.GdmmAdMapper;
import com.zainanjing.article.service.IGdmmAdService;
import org.springframework.stereotype.Service;

/**
 * 广告Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-05
 */
@Service
//官方API 有问题，只加在mapper 曾该方法切换数据库失败gdmmAdService.removeByIds
public class GdmmAdServiceImpl extends GenericCurdServiceImpl<GdmmAdMapper, GdmmAd>
        implements IGdmmAdService {

}
