package com.zainanjing.article.service;

import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.article.domain.GdmmArticleCat;

import java.util.List;

/**
 * 新闻分类Service接口
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
public interface IGdmmArticleCatService extends GenericCurdService<GdmmArticleCat> {
    boolean deleteArticleCat(List<Long> ids);

    boolean update(GdmmArticleCat entity);

}
