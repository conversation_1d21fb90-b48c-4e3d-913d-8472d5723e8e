package com.zainanjing.article.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.article.domain.GdmmArticle;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 新闻Service接口
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
public interface IGdmmArticleService extends GenericCurdService<GdmmArticle> {

    boolean updateArticle(GdmmArticle gdmmArticle);

    IPage<GdmmArticle> list(Searchable searchable);

    void redis2dbArticle();

}
