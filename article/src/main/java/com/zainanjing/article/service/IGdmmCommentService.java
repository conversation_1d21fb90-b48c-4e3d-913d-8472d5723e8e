package com.zainanjing.article.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.GenericCurdService;
import com.zainanjing.article.domain.GdmmComment;

import java.util.Map;

/**
 * 通用评论Service接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public interface IGdmmCommentService extends GenericCurdService<GdmmComment> {
    IPage<GdmmComment> selectGdmmCommentList(Searchable searchable);

    Boolean updateByMap(Map<String, Object> params);

    Integer count(Map<String, Object> params);
}
