package com.zainanjing.article.constant;


import com.zainanjing.article.domain.GdmmDictionary;

/**
 * @ClassName: DictionaryEnum
 * @Description: 字典枚举
 */
public enum DictionaryEnum {

    ARTICLE_SOURCE(1, "ARTICLE", "SOURCE", "资讯", "来源", 1, "50", "50", 0),
    ARTICLE_LABEL(2, "ARTICLE", "LABEL", "资讯", "标签", 0, "0", "0", 0),
    ARTICLE_CHANNEL(3, "ARTICLE", "CHANNEL", "新闻", "频率", 0, "0", "0", 0),
    AUTHENTICATION_CHANNEL(4, "AUTHENTICATION", "CHANNEL", "实名认证", "方式", 0, "0", "0", 1),

    VIDEO_CAT(5, "video", "cat", "短视频", "分类", 0, "0", "0", 0),
    AGENT_REMARK(6, "agent", "remark", "供应商", "备注", 0, "0", "0", 0),
    AUDIT_REMARK(7, "audit", "remark", "审核", "常用语", 0, "0", "0", 0),

    SHOP_SUBJECT(8, "SHOP", "SUBJECT", "商城", "专题", 1, "0", "0", 0),
    DOCUMENTARY_CAT(9, "documentary", "cat", "纪录片", "分类", 0, "0", "0", 0),
    INDEX_CAT(10, "index", "cat", "首页", "分类", 0, "0", "0", 1),
    HOT_SEARCH(11, "hot", "search", "热门", "搜索", 0, "0", "0", 0),
    HEYI_HOME(12, "heyi", "home", "推荐", "首页", 0, "0", "0", 1),
    ;

    private Integer id;
    //模块
    private String module;
    //编码
    private String code;
    //模块名称
    private String moduleName;
    //编码名称
    private String codeName;
    //是否包含图片 0否 1是
    private Integer isImage;
    //图片的宽 没有图片时为0
    private String width;
    //图片的高 没有图片时为0
    private String height;
    //是否包含value 0不含 1包含
    private Integer isValue;

    /**
     * @param id
     * @param module
     * @param code
     * @param moduleName
     * @param codeName
     * @param isImage
     * @param width
     * @param height
     */
    private DictionaryEnum(Integer id, String module, String code, String moduleName,
                           String codeName, Integer isImage, String width, String height, Integer isValue) {
        this.id = id;
        this.module = module;
        this.code = code;
        this.moduleName = moduleName;
        this.codeName = codeName;
        this.isImage = isImage;
        this.width = width;
        this.height = height;
        this.isValue = isValue;
    }

    //通过module,code获取枚举值
    public static DictionaryEnum findByModuleCode(String module, String code) {
        for (DictionaryEnum dEnum : DictionaryEnum.values()) {
            if (dEnum.getModule().equalsIgnoreCase(module) && dEnum.getCode().equalsIgnoreCase(code)) {
                return dEnum;
            }
        }
        return null;
    }

    //将枚举属性值传给字典实体类
    public static void initGdmmDictionary(GdmmDictionary gdmmDictionary) {
        DictionaryEnum enumObj = findByModuleCode(gdmmDictionary.getModule(), gdmmDictionary.getCode());
        if (enumObj != null) {
            gdmmDictionary.setModuleName(enumObj.getModuleName());
            gdmmDictionary.setCodeName(enumObj.getCodeName());
            gdmmDictionary.setIsImage(enumObj.getIsImage());
            gdmmDictionary.setWidth(enumObj.getWidth());
            gdmmDictionary.setHeight(enumObj.getHeight());
            gdmmDictionary.setIsValue(enumObj.getIsValue());
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public Integer getIsImage() {
        return isImage;
    }

    public void setIsImage(Integer isImage) {
        this.isImage = isImage;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public Integer getIsValue() {
        return isValue;
    }

    public void setIsValue(Integer isValue) {
        this.isValue = isValue;
    }


}
