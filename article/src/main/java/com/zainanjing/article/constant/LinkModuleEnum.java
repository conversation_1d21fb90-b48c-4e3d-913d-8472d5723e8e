package com.zainanjing.article.constant;

import com.zainanjing.common.constant.CityEnum;

import java.util.Arrays;

/**
 * @ClassName: LinkModuleEnum
 * @Description: 链接模块枚举
 * @date 2019年4月09日
 */
public enum LinkModuleEnum {
    NOTHING(0, "空模块", "AppInLinkNothing", ""),

    //	before 201901118
    BROADCAST_BC(1, "广播频率(列表和详情)", "AppInLinkToBroadcast", "bc"),
    BROADCASTPOST(2, "广播帖子", "AppInLinkToBroadcastPost", "bc"),
    VIDEOLIVE_BC(3, "视频直播(列表和详情)", "AppInLinkToVideoLive", "bc"),
    BROADCASTREPLY(33, "广播回听", "AppInLinkToBroadcastReply", "bc"),
    TVLIVESHOW(35, "电视直播", "AppInLinkToTVLiveShow", "bc"),//电视直播

//	after 20190118

    TOPICPOSTDETAILSPAGE(1, "紫金2.0-话题帖子详情页", "AppInLinkToTopicPostDetailsPage", "zj"),
    RADIOPROGRAMHOMEPAGE(2, "紫金2.0-电台节目主页", "AppInLinkToRadioProgramHomepage", "zj"),
    //	RADIOPROGRAMDETAILSPAGE(3,"紫金2.0-电台节目播放页","AppInLinkToRadioProgramDetailsPage","zj"),
//	AUDIOPROGRAMPLAYBACKPAGE(33,"紫金2.0-音频集节目播放页","AppInLinkToAudioProgramPlaybackPage","zj"),
//	VIDEOPROGRAMPLAYBACKPAGE(35,"紫金2.0-视频集节目播放页","AppInLinkToVideoProgramPlaybackPage","zj"),
    LIVEVIDDEOPAGE(100, "紫金2.0-视频直播页面", "AppInLinkToLiveVideoPage", "zj"),
    ANCHORHOMEPAGE(101, "紫金2.0-主播个人主页", "AppInLinkToAnchorHomePage", "zj"),
    AUDIOALBUMPAGE(102, "紫金2.0-音频专辑页", "AppInLinkToAudioAlbumPage", "zj"),
    VIDEOALBUMPAGE(103, "紫金2.0-视频专辑页", "AppInLinkToVideoAlbumPage", "zj"),
    BROADCAST(104, "紫金2.0-频率节目列表页", "AppInLinkToBroadcast", "zj"),
    VIDEOLIVE(105, "紫金2.0-频率直播列表页", "AppInLinkToVideoLive", "zj"),
    CITYRADIOPLAYING(106, "紫金2.0-城市电台页", "AppInLinkToCityRadioPage", "zj"),
    STUDIOLIST(107, "紫金2.0-竖屏列表页面", "AppInLinkToStudioList", "zj"),
    STUDIOLIVE(108, "紫金2.0-竖屏直播页面", "AppInLinkToStudioLive", "zj"),


    TOPICPOSTDETAILSPAGE2(1, "紫金-话题帖子详情页", "AppInLinkToTopicPostDetailsPage", "zjsj"),
    RADIOPROGRAMHOMEPAGE2(2, "紫金-电台节目主页", "AppInLinkToRadioProgramHomepage", "zjsj"),
    //	RADIOPROGRAMDETAILSPAGE2(3,"紫金-电台节目播放页","AppInLinkToRadioProgramDetailsPage","zjsj"),
    AUDIOPROGRAMPLAYBACKPAGE2(133, "紫金-音频集节目播放页", "AppInLinkToAudioProgramPlaybackPage", "zjsj"),
    VIDEOPROGRAMPLAYBACKPAGE2(135, "紫金-视频集节目播放页", "AppInLinkToVideoProgramPlaybackPage", "zjsj"),
    LIVEVIDDEOPAGE2(100, "紫金-视频直播页面", "AppInLinkToLiveVideoPage", "zjsj"),
    APPINLINKTOFMTVPLAYPAGE2(117, "紫金-电视直播页面", "AppInLinkToFMTVPlayPage", "zjsj"),
    APPINLINKTOFMTVLISTPAGE2(118, "紫金-电视列表页面", "AppInLinkToFMTVListPage", "zjsj"),
    ANCHORHOMEPAGE2(101, "紫金-主播个人主页", "AppInLinkToAnchorHomePage", "zjsj"),
    AUDIOALBUMPAGE2(102, "紫金-音频专辑页", "AppInLinkToAudioAlbumPage", "zjsj"),
    VIDEOALBUMPAGE2(103, "紫金-视频专辑页", "AppInLinkToVideoAlbumPage", "zjsj"),
    BROADCAST2(104, "紫金-频率节目列表页", "AppInLinkToBroadcast", "zjsj"),
    VIDEOLIVE2(105, "紫金-视频直播列表页", "AppInLinkToVideoLive", "zjsj"),

    NEWS(4, "资讯-列表或详情", "AppInLinkToNews", ""),
    ARTICLELIVE(50, "资讯-图文直详情页", "AppInLinkToArticleLive", ""),
    CHINASOUSUO(5, "资讯-中搜新闻详情页", "AppInLinkToChinaSouSuo", ""),

    CONVENIENCE(6, "便民-首页", "AppInLinkToConvenience", ""),
    BILL(7, "便民-话费充值", "AppInLinkToBill", ""),
    PAYMENTWATER(8, "便民-交水费", "AppInLinkToPaymentWater", ""),
    PAYMENTELECTRIC(9, "便民-交电费", "AppInLinkToPaymentElectric", ""),
    PAYMENTGAS(10, "便民-交燃气费", "AppInLinkToPaymentGas", ""),
    HONGYI(39, "便民-红蚁加油卡", "AppInLinkToOilCard", ""),


    SHOP(11, "商品-店铺首页", "AppInLinkToShop", ""),
    AFFORDABLE(12, "商品-商品列表或详情", "AppInLinkToAffordable", ""),
    PUBLICSALE(14, "商品-涨价拍列表或详情", "AppInLinkToPublicSale", ""),
    AUCTION(15, "商品-竞买列表或详情", "APPInLinkToAuction", ""),
    SHOPPINGCART(31, "商品-购物车", "AppInLinkToShoppingCart", ""),

    COMMUNITY(17, "社区-版块列表或版首", "AppInLinkToCommunity", ""),
    COMMUNITYHOT(40, "社区-首页热门", "AppInLinkToCommunityHot", ""),
    COMMUNITYPOST(18, "社区-帖子详情页", "AppInLinkToCommunityPost", ""),

    SIGN(13, "我的-签到有奖", "AppInLinkToSign", ""),
    MINEPAGE(19, "我的-首页", "AppInLinkToMinePage", ""),
    BALANCE(20, "我的-余额首页", "AppInLinkToBalance", ""),
    RECHARGE(21, "我的-余额充值", "AppInLinkToRecharge", ""),
    REFUND(22, "我的-余额退款", "AppInLinkToRefund", ""),
    GOODSCARD(36, "我的-消费码", "AppInLinkToGoodsCard", ""),//消费码
    COUPON(23, "我的-优惠券", "AppInLinkToCoupon", ""),
    REDPACKAGE(24, "我的-红包", "AppInLinkToRedPackage", ""),
    CATFOODPRIZE(25, "我的-猫粮兑换", "AppInLinkToCatFoodPrize", ""),
    MESSAGECENTER(27, "我的-消息中心", "AppInLinkToMessageCenter", ""),
    INVITEPAGE(26, "我的-邀请达人", "AppInLinkToInvitePage", ""),
    SHIPPINGGOODS(34, "我的-物流查询", "AppInLinkToShippingGoods", ""),


    SHAKELOTTERY(16, "促销-摇一摇", "AppInLinkToShakeLottery", ""),
    GETCOUPON(29, "促销-直接领取优惠券(非页面跳转)", "AppInLinkToGetCoupon", ""),
    GETREDPACKET(30, "促销-直接领取红包(非页面跳转)", "AppInLinkToGetRedPacket", ""),

    OUTLINK(28, "其他-内链中的外链", "AppInLinkToOutLink", ""),
    SCANPAGE(32, "其他-扫描二维码", "AppInLinkToScanPage", ""),
    PAYFORGOODS(99, "其他-商品直接支付", "AppInLinkToPayForGoods", ""),
    CCBDIRECTBANK(37, "其他-建行直销银行", "AppInLinkToCCBDirectBank", ""),
    CCBFLOWBANK(44, "其他-建行导流", "AppInLinkToCCBFlowBank", ""),
    BIANMINFUWU(52, "便民服务", "AppInLinkToBianMinFuWu", ""),
    GONGZHENGFUWU(53,"公证服务","AppInLinkToGONGZHENGFuWu",""),
    UNIONARTICLE(121, "联盟-资讯分类页或详情", "AppInLinkToUnionArticle", ""),
    UNIONCHOICETYPE(122, "联盟-视听分类页", "AppInLinkToUnionChoiceType", ""),
    UNIONCHOICEAUDIO(123, "联盟-视听音频专辑或详情", "AppInLinkToUnionChoiceAudio", ""),
    UNIONCHOICEVIDEO(124, "联盟-视听视频专辑或详情", "AppInLinkToUnionChoiceVideo", ""),
    UNIONPROGRAMLIVE(125, "联盟-直播分类页或详情", "AppInLinkToUnionProgramLive", ""),
    UNIONSHORTVIDEO(136, "联盟-短视频分类页或详情", "AppInLinkToUnionShortVideo", ""),
    UNIONARTICLELIVE(137, "联盟-资讯图文直播详情页", "AppInLinkToUnionArticleLive", ""),
    GUANGDIANBANXUE(41, "广电伴学", "AppInLinkToGuangDianBanXue", ""),
    GDBXGOODSDETAIL(42, "广电伴学商品详情页", "AppInLinkToGDBXGoodsDetail", ""),
    //********
    SPECIALARTICLELIST(43, "专题新闻列表页", "AppInLinkToSpecialArticleList", ""),
    //订阅号
    SUBSCRIPTION(46, "订阅号", "AppInLinkToSubscription", ""),
    //凡科互动
    FANKEHUDONG(47, "凡科互动", "AppInLinkToFankehudong", ""),
    //15分钟生活圈
    SHENGHUOQUAN(48,"15分钟生活圈","AppInLinkToShenghuoquan",""),
    //广电超市专区搜索
    GUANGDIANSEARCH(54,"广电超市专区搜索","AppInLinkToGuangDianSearch",""),
    ;

    private Integer id;
    private String display;
    private String desc;
    // 新广播 和 老广播 要展示的枚举不同。 老的广播  bc。 zj 表示紫金的 枚举。  空串表示通用。
    private String type;

    //这里起到注释作用 bc 表示广播，zj 表示紫金2.0的 枚举。
    private static final String TYPE_BC = "bc";
    private static final String TYPE_ZJ = "zj";
    private static final String TYPE_ZJ_TWO = "zjsj"; //这个应该是紫金1.5
    //新广播 城市 ，可扩展。目前有南京，宁波。 20190827
    public static String[] NEW_BC_CITYS = new String[]{"nanjing", "ningbo", "baoding", "zhongshan", "chengde"};

    //不使用升级版(暂叫1.5版)紫金的城市 包含NEW_BC_CITYS中的城市也不升级
    // 填入的城市 使用 老紫金
    // 不使用新紫金和不填入的城市 使用 升级版紫金
    public static String[] NOT_BE_CITYS = new String[]{};

    /**
     * @param id
     * @param display
     * @param desc
     */
    private LinkModuleEnum(Integer id, String display, String desc, String type) {
        this.id = id;
        this.display = display;
        this.desc = desc;
        this.type = type;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    static {
        String city = CityEnum.getLOCATION();
        //用紫金 删掉 老的广播和升级的紫金
        if (Arrays.asList(NEW_BC_CITYS).contains(city)) {
            for (LinkModuleEnum lme : LinkModuleEnum.values()) {
                String tempType = lme.getType();
                if (TYPE_BC.equals(tempType) || TYPE_ZJ_TWO.equals(tempType)) {
//                    DynamicEnumUtil.removeEnum(LinkModuleEnum.class, lme.toString());
                }
            }

            //使用老紫金 删掉 新紫金和升级的紫金
        } else if (Arrays.asList(NOT_BE_CITYS).contains(city)) {
            //在集合中说明不使用紫金升级的版本（即不使用新紫金 和 升级版紫金 =======  使用老紫金）
            for (LinkModuleEnum lme : LinkModuleEnum.values()) {
                String lmeType = lme.getType();
                if (TYPE_ZJ_TWO.equals(lmeType) || TYPE_ZJ.equals(lmeType)) {
//                    DynamicEnumUtil.removeEnum(LinkModuleEnum.class, lme.toString());
                }
            }

            //使用紫金升级版  删掉老紫金广播 和 新紫金
        } else {
            for (LinkModuleEnum lme : LinkModuleEnum.values()) {
                String tempType = lme.getType();
                if (TYPE_ZJ.equals(tempType) || TYPE_BC.equals(tempType)) {
//                    DynamicEnumUtil.removeEnum(LinkModuleEnum.class, lme.toString());
                }
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(LinkModuleEnum.values().length);
    }
}
