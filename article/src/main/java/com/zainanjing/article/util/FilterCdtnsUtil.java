package com.zainanjing.article.util;

import java.util.Objects;

public class FilterCdtnsUtil {

    public static Integer getInt(Object o) {
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof Integer) {
            return (Integer) o;
        }
        if (o instanceof String) {
            return Integer.valueOf((String) o);
        }
        return null;
    }

    public static Long getLong(Object o) {
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof Long) {
            return (Long) o;
        }
        if (o instanceof String) {
            return Long.valueOf((String) o);
        }
        return null;
    }

    public static String getString(Object o) {
        if (Objects.isNull(o)) {
            return null;
        }
        return o.toString();
    }

}
