<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqAreaMapper">

    <resultMap type="com.zainanjing.shequ.domain.SqArea" id="SqAreaResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="color" column="color"/>
        <result property="isExtend" column="is_extend"/>
        <result property="isShow" column="is_show"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  name,  color,  is_extend,  is_show,  sort,  status,  create_time,  update_time     </sql>

</mapper>