<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.GdmmGoodsLabelMapper">
    
    <resultMap type="com.zainanjing.shequ.domain.GdmmGoodsLabel" id="GdmmGoodsLabelResult">
        <result property="goodsId"    column="goods_id"    />
        <result property="type"    column="type"    />
        <result property="labelId"    column="label_id"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="Base_Column_List">
 goods_id,  type,  label_id,  status     </sql>

</mapper>