<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqIndexUsersRoleMapper">

    <resultMap type="com.zainanjing.shequ.domain.SqIndexUsersRole" id="SqIndexUsersRoleResult">
        <result property="uid" column="uid"/>
        <result property="roleId" column="role_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        uid,  role_id     </sql>


    <select id="selectUserRoleByUids" resultType="com.zainanjing.shequ.domain.SqIndexUsersRole">
        select iur.*, r.name as role_name from sq_index_users_role iur
        inner join sq_role r
        on iur.role_id = r.id
        where iur.uid in
        <foreach collection="uids" item="uid" open="(" close=")" separator=",">
            #{uid}
        </foreach>
    </select>


</mapper>