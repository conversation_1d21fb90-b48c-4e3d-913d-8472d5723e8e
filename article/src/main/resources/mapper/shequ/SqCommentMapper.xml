<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqCommentMapper">

    <resultMap type="com.zainanjing.shequ.domain.SqComment" id="SqCommentResult">
        <result property="id" column="id"/>
        <result property="postId" column="post_id"/>
        <result property="uid" column="uid"/>
        <result property="content" column="content"/>
        <result property="ip" column="ip"/>
        <result property="region" column="region"/>
        <result property="parentId" column="parent_id"/>
        <result property="type" column="type"/>
        <result property="level" column="level"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="forumId" column="forum_id"/>
        <result property="hasNewComment" column="has_new_comment"/>
        <result property="floor" column="floor"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="auditTime" column="audit_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  post_id,  uid,  content,  ip,  region,  parent_id,  type,  level,  create_time,  update_time,  status,  is_anonymous,  forum_id,  has_new_comment,  floor,  reject_reason,  audit_time     </sql>


    <select id="searchPage" parameterType="java.util.Map" resultType="com.zainanjing.shequ.domain.SqComment">
        select sc.*, sp.click_num, sp.comment_num, sp.subject, sp.create_time as postCreateTime, eup.user_name as
        postUserName, eu.user_name, eu.status as userStatus, eu.medal_level, if(ISNULL(sfu.forum_id),0,1) as isHost,
        if(ISNULL(suc.comment_id),0,1) as isStar, ea.address as headImageAddress, ea.type as avatarType
        , sp.subject as postSubject, sp.content as postContent, sforum.name as forumName
        from sq_comment sc
        left join gdmm_users eu on sc.uid = eu.user_id
        left join sq_post sp on sp.id = sc.post_id
        left join gdmm_users eup on sp.uid = eup.user_id
        left join sq_index_forum_user sfu on sc.uid = sfu.uid
        left join gdmm_avatar ea on sc.uid=ea.uid
        left join sq_index_user_comment suc on sc.id = suc.comment_id and suc.uid=#{p.uidForStar}
        LEFT JOIN sq_forum sforum ON sforum.id = sp.forum_id
        <where>
            <if test="p.isStar != null">
                and ((suc.comment_id IS NOT NULL and #{p.isStar} = 1) or (ISNULL(suc.comment_id) and #{p.isStar} = 0))
            </if>
            <if test="p.uid != null">
                and sc.uid = #{p.uid}
            </if>
            <if test="p.closeUids != null  and p.closeUids.size() > 0">
                and sc.uid not in
                <foreach item="closeUid" collection="p.closeUids" open="(" separator="," close=")">
                    #{closeUid}
                </foreach>
            </if>
            <if test="p.type != null">
                and sc.type = #{p.type}
            </if>
            <if test="p.postId != null">
                and sc.post_id = #{p.postId}
            </if>
            <if test="p.status != null">
                and sc.status = #{p.status}
            </if>
            <if test="p.statusArr != null">
                and sc.status in
                <foreach item="statusItem" collection="p.statusArr" open="(" separator="," close=")">
                    #{statusItem}
                </foreach>
            </if>
            <if test="p.statusLanZhou != null">
                and (sc.status = 0 or (sc.status = 3 and sc.uid = #{p.uidLanZhou}))
            </if>
            <if test="p.parentId != null">
                and sc.parent_id = #{p.parentId}
            </if>
            <if test="p.content != null">
                and sc.content like CONCAT('%', #{p.content}, '%')
            </if>
            <if test="p.subject != null">
                and sp.subject like CONCAT('%', #{p.subject}, '%')
            </if>
            <if test="p.seeLevel != null">
                and (sp.see_level = #{p.seeLevel} or sc.uid = #{p.uidSelf})
            </if>
            <if test="p.userName != null">
                and eu.user_name like CONCAT('%', #{p.userName}, '%')
            </if>
        </where>
        group by sc.id
        <if test="p.orderBy != null">
            <choose>
                <when test="p.orderBy == 1">
                    order by sc.id asc
                </when>
                <otherwise>
                    order by sc.id desc
                </otherwise>
            </choose>
        </if>
        <if test="p.orderBy == null">
            order by sc.id desc
        </if>
    </select>

    <select id="searchListReplyMe" resultMap="sqCommentMapForMe" parameterClass="java.util.Map">
        <!-- 回复我的帖子的 -->
        (
        select
        sc.*,1 as type,sp.id as replyId,sp.content as replyContent ,eu.user_name,eu.medal_level,sf.name as forumName,if(ISNULL(sfu.forum_id),0,1) as isHost,ea.address as headImageAddress,ea.type as avatarType
        from
        sq_comment sc
        left join   sq_post sp
        on sc.post_id = sp.id
        left join  gdmm_users eu
        on sc.uid = eu.user_id
        left join  sq_forum sf
        on sf.id = sc.forum_id
        left join sq_index_forum_user sfu
        on sc.uid = sfu.uid
        left join gdmm_avatar ea
        on sc.uid=ea.uid
        where
        sp.uid = #uid# and sp.status = 0 and sc.type = 1 and sc.status = 0
        )
        union
        <!-- 回复我的评论的 -->
        (
        select sc.*,2 as type,scp.id as replyId,scp.content as replyContent,eu.user_name ,eu.medal_level,sf.name as forumName ,if(ISNULL(sfu.forum_id),0,1) as isHost,ea.address as headImageAddress,ea.type as avatarType
        from
        (select * from sq_comment where  type =2 and status = 0) sc
        left join
        sq_comment scp
        on  scp.id = sc.parent_id
        left join
        gdmm_users eu
        on sc.uid = eu.user_id
        left join  sq_forum sf
        on sf.id = sc.forum_id
        left join sq_index_forum_user sfu
        on sc.uid = sfu.uid
        left join gdmm_avatar ea
        on sc.uid=ea.uid
        where scp.uid = #uid#
        )
        order by id desc
        <dynamic prepend="">
            <isNotNull property="start">
                <isNotNull property="size">
                    limit #start#, #size#
                </isNotNull>
            </isNotNull>
        </dynamic>
    </select>
</mapper>