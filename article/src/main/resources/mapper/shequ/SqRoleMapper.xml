<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqRoleMapper">

    <resultMap type="com.zainanjing.shequ.domain.SqRole" id="SqRoleResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="authorities" column="authorities"/>
        <result property="isSystem" column="is_system"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  name,  description,  authorities,  is_system,  create_time,  update_time     </sql>

    <select id="selectRoleByUserId" resultType="com.zainanjing.shequ.domain.SqRole">
        SELECT *
        FROM sq_role
        WHERE sq_role.id IN (SELECT sq_index_users_role.role_id
                             FROM sq_index_users_role
                             WHERE sq_index_users_role.uid = #{uid})
    </select>

</mapper>