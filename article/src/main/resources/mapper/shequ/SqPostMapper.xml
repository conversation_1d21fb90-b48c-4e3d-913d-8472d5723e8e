<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqPostMapper">

    <resultMap type="com.zainanjing.shequ.dto.SqPostDTO" id="sqPostMapBase">
        <result column="id" property="id"/>
        <result column="forum_id" property="forumId"/>
        <result column="goods_id" property="goodsId"/>
        <result column="subject" property="subject"/>
        <result column="content" property="content"/>
        <result column="color" property="color"/>
        <result column="uid" property="uid"/>
        <result column="click_num" property="clickNum"/>
        <result column="init_click_num" property="initClickNum"/>
        <result column="comment_num" property="commentNum"/>
        <result column="praise_num" property="praiseNum"/>
        <result column="type" property="type"/>
        <result column="sort" property="sort"/>
        <result column="is_rec" property="isRec"/>
        <result column="is_top" property="isTop"/>
        <result column="is_hot" property="isHot"/>
        <result column="is_show_index" property="isShowIndex"/>
        <result column="is_anonymous" property="isAnonymous"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="see_level" property="seeLevel"/>
        <result column="can_reply" property="canReply"/>
        <result column="status" property="status"/>
        <result column="has_new_comment" property="hasNewComment"/>
        <result column="video_url" property="videoUrl"/>
        <result column="video_img" property="videoImg"/>
        <result column="top_type" property="topType"/>
        <result column="have_hidden" property="haveHidden"/>
        <result column="hidden_content" property="hiddenContent"/>
        <result column="html" property="html"/>
        <result column="attribute" property="attribute"/>
        <result column="html_img" property="htmlImg"/>
        <result column="img_size" property="imgSize"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="audit_time" property="auditTime"/>
        <!-- 20240110 加一个用状态 0正常，1注销 -->
        <result column="userStatus" property="userStatus"/>
        <!--  20240118 省市信息 。ip先不返回 -->
        <result property="region" column="region"/>
    </resultMap>

    <resultMap type="com.zainanjing.shequ.dto.SqPostDTO" id="sqPostMapBaseForFindById" extends="sqPostMapBase">
        <result column="user_name" property="userName" />
        <result column="medal_level" property="medalLevel" />
        <result column="forumName" property="forumName" />
        <result column="areaName" property="areaName" />
        <result property="imgUrl" column="headImageAddress" />
        <result property="avatarType" column="avatarType" />
        <result property="forumLogo" column="logo" />
        <!--  <result property="isHost" column="isHost" /> -->
        <result property="isStar" column="isStar" />
        <result column="user_level_browse" property="userLevelBrowse" />
        <result column="is_certification_browse" property="isCertificationBrowse" />
        <result column="is_sign_in_browse" property="isSignInBrowse" />
        <result column="user_level_post" property="userLevelPost" />
        <result column="is_certification_post" property="isCertificationPost" />
        <result column="is_sign_in_post" property="isSignInPost" />
        <result column="is_label_browse" property="isLabelBrowse" />
        <result column="is_label_post" property="isLabelPost" />
        <result column="goodsName" property="goodsName" />
        <result column="goodsTitle" property="goodsTitle" />
        <result column="goodsThumbnail" property="goodsThumbnail" />
        <result column="goodsMarketPrice" property="goodsMarketPrice" />
        <result column="showMarketPrice" property="showMarketPrice" />

        <result column="goodsPrice" property="goodsPrice" />
        <result column="link_module" property="linkModule"/>
        <result column="resource_id" property="resourceId" />
        <result column="link_to" property="linkTo" />
        <result column="link_url" property="linkUrl" />
        <result column="link_type" property="linkType" />

    </resultMap>

    <resultMap type="com.zainanjing.shequ.dto.SqPostDTO" id="sqPostMapBaseForSearch" extends="sqPostMapBase">
        <result column="user_name" property="userName"/>
        <result column="medal_level" property="medalLevel"/>
        <result column="forumName" property="forumName"/>
        <result column="areaName" property="areaName"/>
        <result column="headImageAddress" property="imgUrl"/>
        <result column="avatarType" property="avatarType"/>
        <result column="logo" property="forumLogo"/>
        <!--  <result property="isHost" column="isHost" /> -->
        <result property="isStar" column="isStar"/>
        <result column="user_level_browse" property="userLevelBrowse"/>
        <result column="is_certification_browse" property="isCertificationBrowse"/>
        <result column="is_sign_in_browse" property="isSignInBrowse"/>
        <result column="user_level_post" property="userLevelPost"/>
        <result column="is_certification_post" property="isCertificationPost"/>
        <result column="is_sign_in_post" property="isSignInPost"/>
        <result column="is_label_browse" property="isLabelBrowse"/>
        <result column="is_label_post" property="isLabelPost"/>
        <result column="goodsName" property="goodsName"/>
        <result column="goodsTitle" property="goodsTitle"/>
        <result column="goodsThumbnail" property="goodsThumbnail"/>
        <result column="goodsMarketPrice" property="goodsMarketPrice"/>
        <result column="goodsPrice" property="goodsPrice"/>
    </resultMap>

    <select id="list" parameterType="java.util.Map" resultMap="sqPostMapBaseForSearch">
        select  sp.* ,
        sf.is_certification_browse,
        sf.is_sign_in_browse,
        sf.user_level_browse,
        sf.user_level_post,
        sf.is_certification_post,
        sf.is_sign_in_post,
        sf.is_label_browse,
        sf.is_label_post,
        eu.user_name  ,
        eu.status as userStatus ,
        eu.medal_level  ,
        eu.mobile_phone ,
        sf.logo,sf.name as  forumName ,
        sa.name as areaName,
        if(ISNULL(sup.post_id),0,1) as isStar,
        ea.address as headImageAddress,
        ea.type as avatarType ,
        '' as goodsName,
        '' as goodsTitle,
        '' as goodsThumbnail,
        0 as goodsMarketPrice,
        0 as goodsPrice
        from sq_post sp
        left join  gdmm_users eu on sp.uid = eu.user_id
        left join sq_forum  sf on sf.id = sp.forum_id
        left join sq_area sa on sa.id = sf.area_id
        left join gdmm_avatar ea on sp.uid=ea.uid
        left join sq_index_user_post sup on sp.id = sup.post_id and sup.uid=#{p.uidForStar}
        <where>
            <if test="p.isTop != null">
                AND sp.is_top = #{p.isTop}
            </if>
            <if test="p.latestId != null">
                <if test="p.isHot == null">
                    AND <![CDATA[((sp.sort <= #{p.lastPostSort} and sp.id < #{p.latestId}) or (sp.sort < #{p.lastPostSort}))]]>
                </if>
                <if test="p.isHot != null">
                    AND <![CDATA[sp.id < #{p.latestId}]]>
                </if>
            </if>
            <if test="p.status != null">
                AND sp.status = #{p.status}
            </if>
            <if test="p.statusArr != null">
                AND sp.status in
                <foreach collection="p.statusArr" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="p.forumId != null">
                AND sp.forum_id = #{p.forumId}
            </if>
            <if test="p.goodsId != null">
                AND sp.goods_id = #{p.goodsId}
            </if>
            <if test="p.startSort != null">
                AND <![CDATA[ sp.sort >= #{p.endSort} and sp.sort <= #{p.startSort} ]]>
            </if>
            <if test="p.startLastReplyDateline != null">
                AND <![CDATA[ sp.last_reply_dateline >= #{p.endLastReplyDateline} and sp.last_reply_dateline <= #{p.startLastReplyDateline} ]]>
            </if>
            <if test="p.allTop != null">
                <!-- 置顶帖，本讨论版内，本频率内和全局 -->
                AND ((sp.forum_id = #{p.forumIdForTop} and sp.top_type=1) || (sp.top_type=2 and sf.area_id=#{p.areaIdForTop}) ||(sp.top_type=3))
            </if>
            <if test="p.uid != null">
                AND sp.uid = #{p.uid}
            </if>
            <if test="p.closeUids != null and p.closeUids.size() > 0">
                AND sp.uid not in
                <foreach collection="p.closeUids" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
            </if>
            <if test="p.isStar != null">
                <if test="p.isStar == 1">
                    AND sup.post_id IS NOT NULL
                </if>
                <if test="p.isStar == 0">
                    AND ISNULL(sup.post_id)
                </if>
            </if>
            <if test="p.isShowIndex != null">
                AND sp.is_show_index = #{p.isShowIndex}
            </if>
            <if test="p.isHot != null">
                AND (sp.is_hot > 0  or sp.is_rec > 0  )
                and sp.create_time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 day))
            </if>
            <if test="p.beginTime != null">
                AND <![CDATA[ sp.create_time >= #{p.beginTime} ]]>
            </if>
            <if test="p.endTime != null">
                AND <![CDATA[ sp.create_time <= #{p.endTime} ]]>
            </if>
            <if test="p.subject != null">
                AND sp.subject like CONCAT('%',#{p.subject},'%')
            </if>
            <if test="p.content != null">
                AND sp.content like CONCAT('%',#{p.content},'%')
            </if>
            <if test="p.forumName != null">
                AND sf.name like CONCAT('%',#{p.forumName},'%')
            </if>
            <if test="p.userName != null">
                AND eu.user_name like CONCAT('%',#{p.userName},'%')
            </if>
            <if test="p.keyword != null">
                AND (sp.content like CONCAT('%',#{p.keyword},'%')
                or     sp.subject like CONCAT('%',#{p.keyword},'%'))
            </if>
            <if test="p.forumStatus != null">
                AND sf.status = #{p.forumStatus}
            </if>
            <if test="p.forumIds != null">
                AND sp.forum_id in
                <foreach collection="p.forumIds" item="forumId" open="(" separator="," close=")">
                    #{forumId}
                </foreach>
            </if>
            <if test="p.postId != null">
                AND sp.id = #{p.postId}
            </if>
            <if test="p.userLevelBrowse != null">
                AND sf.user_level_browse = #{p.userLevelBrowse}
            </if>
            <if test="p.isCertificationBrowse != null">
                AND sf.is_certification_browse = #{p.isCertificationBrowse}
            </if>
            <if test="p.isSignInBrowse != null">
                AND sf.is_sign_in_browse = #{p.isSignInBrowse}
            </if>
            <if test="p.isLabelBrowse != null">
                AND sf.is_label_browse = #{p.isLabelBrowse}
            </if>
            <if test="p.orderBy != null and p.orderBy == 3">
                <!-- 3推荐贴 -->
                AND sp.is_rec = 1
            </if>
        </where>
        <choose>
            <when test="p.orderBy != null">
                <choose>
                    <!-- 最新发布 -->
                    <when test="p.orderBy == 1">
                        order by sp.sort desc,sp.id desc
                    </when>
                    <!-- 最后回复 -->
                    <when test="p.orderBy == 2">
                        order by sp.last_reply_dateline desc,sp.id desc
                    </when>
                    <!-- 3推荐贴 -->
                    <when test="p.orderBy == 3">
                        order by sp.is_rec desc , sp.id desc
                    </when>
                    <otherwise>
                        order by sp.id desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by sp.id desc
            </otherwise>
        </choose>
    </select>

    <select id="findById" resultMap="sqPostMapBaseForFindById">
        select sp.* ,
        sf.is_certification_browse,
        sf.is_sign_in_browse,
        sf.user_level_browse,
        sf.user_level_post,
        sf.is_certification_post,
        sf.is_sign_in_post,
        sf.is_label_browse,
        sf.is_label_post,

        sf.link_type,
        sf.link_module,
        sf.resource_id,
        sf.link_to,
        sf.link_url,

        eu.user_name ,
        eu.status as userStatus ,
        eu.medal_level ,
        sf.logo,sf.name as forumName ,
        sa.name as areaName,
        if(ISNULL(sfu.forum_id),0,1) as isHost,
        if(ISNULL(sup.post_id),0,1) as isStar,
        ea.address as headImageAddress,
        ea.type as avatarType ,
        <!-- 6.2.1新增返回字段 -->
        g.name as goodsName,
        g.title as goodsTitle,
        p.thumbnail as goodsThumbnail,
        g.market_price as goodsMarketPrice,
        g.price as goodsPrice,
        g.show_market_price as showMarketPrice
        from sq_post sp
        left join gdmm_users eu on
        sp.uid = eu.user_id
        left join sq_forum sf on
        sf.id = sp.forum_id
        left join sq_area sa on
        sa.id = sf.area_id
        left join sq_index_forum_user sfu
        on sp.uid = sfu.uid
        left join gdmm_avatar ea
        on sp.uid=ea.uid
        left join sq_index_user_post sup
        on sp.id = sup.post_id and sup.uid=#uidForStar#
        left join gdmm_goods g
        on sp.goods_id = g.id
        left join gdmm_product p
        on g.id = p.goods_id and p.is_default=1 and p.status in(0,2,3)
        where sp.id=#{id}
        group by sp.id
    </select>
</mapper>