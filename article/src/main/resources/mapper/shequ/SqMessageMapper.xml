<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqMessageMapper">

    <resultMap type="com.zainanjing.shequ.domain.SqMessage" id="SqMessageResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="senderRead" column="sender_read"/>
        <result property="receiverRead" column="receiver_read"/>
        <result property="senderDelete" column="sender_delete"/>
        <result property="receiverDelete" column="receiver_delete"/>
        <result property="sender" column="sender"/>
        <result property="receiver" column="receiver"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="twoUid" column="two_uid"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  type,  content,  sender_read,  receiver_read,  sender_delete,  receiver_delete,  sender,  receiver,  create_time,  update_time,  two_uid,  status     </sql>


    <select id="friendList" parameterType="java.util.Map" resultType="com.zainanjing.shequ.domain.SqMessage">
            <![CDATA[
        select e.*,
               user.user_name    as friendName,
               user.mobile_phone as friendPhone,
               avatar.type       as friendAvatarType,
               avatar.address    as friendImageUrl
        from (select a.*,
                     case
                         when a.sender = #{p.uid} then a.receiver
                         when a.receiver = #{p.uid} then a.sender end as friendUid,
                     d.unreadnum
              from sq_message a,
                   (select c.two_uid, max(c.id) as id, sum(c.unread) as unReadNum
                    from (select b.*,
                                 case
                                     when b.receiver_read = 0 and b.sender != #{p.uid} then 1
                                     else 0 end as unread
                          from sq_message b
                          where (status = 0 or (status != #{p.uid} and status != 1))
                            and (b.sender = #{p.uid} or b.receiver = #{p.uid})) c
                    group by c.two_uid) d
              where a.id = d.id) e
                 inner join gdmm_users user on e.friendUid = user.user_id
                 left join gdmm_avatar avatar on e.friendUid = avatar.uid
        order by e.id desc
        ]]>
    </select>

</mapper>