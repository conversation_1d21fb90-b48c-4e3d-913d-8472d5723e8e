<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqPraiseMapper">

    <resultMap type="com.zainanjing.shequ.domain.SqPraise" id="SqPraiseResult">
        <result property="postId" column="post_id"/>
        <result property="uid" column="uid"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        post_id,  uid,  status,  create_time,  update_time     </sql>

</mapper>