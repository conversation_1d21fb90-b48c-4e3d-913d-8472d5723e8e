<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.SqForumMapper">

    <resultMap type="com.zainanjing.shequ.domain.SqForum" id="SqForumResult">
        <result property="id" column="id"/>
        <result property="areaId" column="area_id"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="imgUrl" column="img_url"/>
        <result property="color" column="color"/>
        <result property="subjectNum" column="subject_num"/>
        <result property="replyNum" column="reply_num"/>
        <result property="todayPostNum" column="today_post_num"/>
        <result property="isRec" column="is_rec"/>
        <result property="description" column="description"/>
        <result property="isShow" column="is_show"/>
        <result property="userName" column="user_name"/>
        <result property="userLevelBrowse" column="user_level_browse"/>
        <result property="isCertificationBrowse" column="is_certification_browse"/>
        <result property="isSignInBrowse" column="is_sign_in_browse"/>
        <result property="userLevelPost" column="user_level_post"/>
        <result property="isCertificationPost" column="is_certification_post"/>
        <result property="isSignInPost" column="is_sign_in_post"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isLabelBrowse" column="is_label_browse"/>
        <result property="isLabelPost" column="is_label_post"/>
        <result property="sort" column="sort"/>
        <result property="linkType" column="link_type"/>
        <result property="linkModule" column="link_module"/>
        <result property="resourceId" column="resource_id"/>
        <result property="linkTo" column="link_to"/>
        <result property="linkUrl" column="link_url"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  area_id,  name,  logo,  img_url,  color,  subject_num,  reply_num,  today_post_num,  is_rec,  description,  is_show,  user_name,  user_level_browse,  is_certification_browse,  is_sign_in_browse,  user_level_post,  is_certification_post,  is_sign_in_post,  status,  create_time,  update_time,  is_label_browse,  is_label_post,  sort,  link_type,  link_module,  resource_id,  link_to,  link_url     </sql>

</mapper>