<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.shequ.mapper.GdmmUsersMapper">

    <resultMap type="com.zainanjing.shequ.domain.GdmmUsers" id="GdmmUsersResult">
        <result property="userId" column="user_id"/>
        <result property="email" column="email"/>
        <result property="userName" column="user_name"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,  email,  user_name,  status     </sql>

    <select id="selectUser" parameterType="long" resultType="com.zainanjing.shequ.domain.GdmmUsers">
        select user.user_id,
               user.email,
               user.user_name,
               user.status,
               user.medal_level,
               avatar.type as avatarType,
               avatar.address as imageUrl
        from gdmm_users user
                 left join gdmm_avatar avatar
                           on user.user_id = avatar.uid
        where user.user_id = #{userId}
    </select>
</mapper>