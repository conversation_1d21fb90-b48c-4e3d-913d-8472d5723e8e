<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmArticleMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmArticle" id="GdmmArticleResult">
        <result property="id" column="id"/>
        <result property="articleCatId" column="article_cat_id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="qrCodeUrl" column="qr_code_url"/>
        <result property="author" column="author"/>
        <result property="authorEmail" column="author_email"/>
        <result property="keywords" column="keywords"/>
        <result property="articleType" column="article_type"/>
        <result property="status" column="status"/>
        <result property="enabled" column="enabled"/>
        <result property="isHot" column="is_hot"/>
        <result property="remark" column="remark"/>
        <result property="imgUrl" column="img_url"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="videoUrl" column="video_url"/>
        <result property="videoImgUrl" column="video_img_url"/>
        <result property="idForChinaSearch" column="id_for_china_search"/>
        <result property="timeForChinaSearch" column="time_for_china_search"/>
        <result property="articleDetailUrl" column="article_detail_url"/>
        <result property="idForThird" column="id_for_third"/>
        <result property="viewed" column="viewed"/>
        <result property="sort" column="sort"/>
        <result property="isHighLight" column="is_high_light"/>
        <result property="createTime" column="create_time"/>
        <result property="displayMode" column="display_mode"/>
        <result property="sourceId" column="source_id"/>
        <result property="sourceNameForThird" column="source_name_for_third"/>
        <result property="labelId" column="label_id"/>
        <result property="isNotice" column="is_notice"/>
        <result property="isComment" column="is_comment"/>
        <result property="editor" column="editor"/>
        <result property="reporter" column="reporter"/>
        <result property="subjectArticleId" column="subject_article_id"/>
        <result property="subjectCatId" column="subject_cat_id"/>
        <result property="subjectId" column="subject_id"/>
        <result property="recommendHome" column="recommend_home"/>
        <result property="initView" column="init_view"/>
        <result property="viewTimes" column="view_times"/>
        <result property="managerId" column="manager_id"/>
        <result property="score" column="score"/>
        <result property="openType" column="open_type"/>
        <result property="linkUrl" column="link_url"/>
        <result property="cardId" column="card_id"/>
        <result property="guilinType" column="guilin_type"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  article_cat_id,  type,  title,  content,  qr_code_url,  author,  author_email,  keywords,  article_type,  status,  enabled,  is_hot,  remark,  img_url,  audio_url,  video_url,  video_img_url,  id_for_china_search,  time_for_china_search,  article_detail_url,  id_for_third,  viewed,  sort,  is_high_light,  create_time,  display_mode,  source_id,  source_name_for_third,  label_id,  is_notice,  is_comment,  editor,  reporter,  subject_article_id,  subject_cat_id,  subject_id,  recommend_home,  init_view,  view_times,  manager_id,  score,  open_type,  link_url,  card_id
    </sql>


    <select id="list" parameterType="java.util.Map" resultMap="GdmmArticleResult">
        SELECT a.*, c.cat_name, source.name AS source, m.remark AS managerRemark FROM gdmm_article a
        LEFT JOIN gdmm_article_cat c ON a.article_cat_id = c.id
        LEFT JOIN gdmm_dictionary source ON a.source_id = source.id
        LEFT JOIN gdmm_manager m ON a.manager_id = m.id
        <where>
            <if test="param.guilinType != null">
                a.guilin_type = #{param.guilinType}
            </if>
            <if test="param.queryKeyword != null">
                AND (a.id = #{param.queryKeyword} or a.title like CONCAT('%', #{param.queryKeyword}, '%'))
            </if>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.type != null">
                AND a.type = #{param.type}
            </if>
            <if test="param.articleCatId != null">
                AND a.article_cat_id = #{param.articleCatId}
            </if>
            <if test="param.title != null">
                AND a.title LIKE CONCAT('%', #{param.title}, '%')
            </if>
            <if test="param.content != null">
                AND a.content = #{param.content}
            </if>
            <if test="param.author != null">
                AND a.author LIKE CONCAT('%', #{param.author}, '%')
            </if>
            <if test="param.authorEmail != null">
                AND a.author_email = #{param.authorEmail}
            </if>
            <if test="param.keywords != null">
                AND a.keywords = #{param.keywords}
            </if>
            <if test="param.articleType != null">
                AND a.article_type = #{param.articleType}
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}
            </if>
            <if test="param.statusArr != null">
                AND a.status IN
                <foreach collection="param.statusArr" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="param.enabled != null">
                AND a.enabled = #{param.enabled}
            </if>
            <if test="param.isHot != null">
                AND a.is_hot = #{param.isHot}
            </if>
            <if test="param.remark != null">
                AND a.remark = #{param.remark}
            </if>
            <if test="param.imgUrl != null">
                AND a.img_url = #{param.imgUrl}
            </if>
            <if test="param.audioUrl != null">
                AND a.audio_url = #{param.audioUrl}
            </if>
            <if test="param.videoUrl != null">
                AND a.video_url = #{param.videoUrl}
            </if>
            <if test="param.videoImgUrl != null">
                AND a.video_img_url = #{param.videoImgUrl}
            </if>
            <if test="param.articleDetailUrl != null">
                AND a.article_detail_url = #{param.articleDetailUrl}
            </if>
            <if test="param.viewed != null">
                AND a.viewed = #{param.viewed}
            </if>
            <if test="param.sort != null">
                AND a.sort = #{param.sort}
            </if>
            <if test="param.isHighLight != null">
                AND a.is_high_light = #{param.isHighLight}
            </if>
            <if test="param.createTime != null">
                AND a.create_time = #{param.createTime}
            </if>
            <if test="param.sourceId != null">
                AND a.source_id = #{param.sourceId}
            </if>
            <if test="param.after != null">
                <if test="param.after.timeYesterday != null">
                    AND a.create_time > #{param.after.timeYesterday}
                </if>
                <if test="param.after.earliestTime != null">
                    AND a.create_time > #{param.after.earliestTime}
                </if>
            </if>
            <if test="param.latestId != null">
                AND a.id &lt; #{param.latestId}
            </if>
            <if test="param.timebefore != null">
                AND a.create_time &lt; #{param.timebefore}
            </if>
            <if test="param.sortBiggerThanZero != null">
                AND a.sort > 0
            </if>
            <if test="param.score != null">
                <choose>
                    <when test="param.score == 6">
                        AND a.score = 0
                    </when>
                    <when test="param.score == 7">
                        AND a.score > 0
                    </when>
                </choose>
            </if>
            <if test="param.editor != null">
                AND a.editor LIKE CONCAT('%', #{param.editor}, '%')
            </if>
            <if test="param.reporter != null">
                AND a.reporter LIKE CONCAT('%', #{param.reporter}, '%')
            </if>
            <if test="param.beginTime != null">
                AND a.create_time >= #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND a.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.earliestTime != null">
                AND a.create_time >= #{param.earliestTime}
            </if>
            <if test="param.subjectArticleId != null">
                AND a.subject_article_id = #{param.subjectArticleId}
            </if>
            <if test="param.subjectCatId != null">
                AND a.subject_cat_id = #{param.subjectCatId}
            </if>
            <if test="param.subjectId != null">
                AND a.subject_id = #{param.subjectId}
            </if>
            <if test="param.recommendHome != null">
                AND a.recommend_home = #{param.recommendHome}
            </if>
            <if test="param.initView != null">
                AND a.init_view = #{param.initView}
            </if>
            <if test="param.managerIds != null">
                AND a.manager_id IN
                <foreach collection="param.managerIds" item="managerId" open="(" close=")" separator=",">
                    #{managerId}
                </foreach>
            </if>
            <if test="param.isCardId != null">
                <choose>
                    <when test="param.isCardId == 0">
                        AND a.card_id = 0
                    </when>
                    <when test="param.isCardId == 1">
                        AND a.card_id > 0
                    </when>
                </choose>
            </if>
        </where>
        <if test="param.orderProperty != null">
            <if test="param.orderDirection != null">
                ORDER BY ${param.orderProperty} ${param.orderDirection}, a.create_time DESC
            </if>
            <if test="param.orderDirection == null">
                ORDER BY ${param.orderProperty} DESC, a.create_time DESC
            </if>
        </if>
        <if test="param.orderProperty == null">
            <if test="param.orderBy != null">
                <choose>
                    <when test="param.orderBy == 1">
                        ORDER BY CONVERT(a.id_for_china_search, SIGNED) DESC
                    </when>
                    <otherwise>
                        ORDER BY a.id DESC
                    </otherwise>
                </choose>
            </if>
            <if test="param.orderBy == null">
                ORDER BY a.id DESC
            </if>
        </if>
    </select>

</mapper>