<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmLabelMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmLabel" id="GdmmLabelResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="resourceId" column="resource_id"/>
        <result property="pic" column="pic"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="linkUrl" column="link_url"/>
        <result property="sort" column="sort"/>
        <result property="indate" column="indate"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  name,  type,  resource_id,  pic,  create_time,  update_time,  remark,  status,  link_url,  sort,  indate     </sql>

</mapper>