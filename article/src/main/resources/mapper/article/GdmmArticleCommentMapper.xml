<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmArticleCommentMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmArticleComment" id="GdmmArticleCommentResult">
        <result property="id" column="id"/>
        <result property="articleId" column="article_id"/>
        <result property="uid" column="uid"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="parentId" column="parent_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  article_id,  uid,  content,  type,  parent_id,  create_time,  update_time,  sort,  status     </sql>

    <select id="selectGdmmArticleCommentList" parameterType="java.util.Map" resultMap="GdmmArticleCommentResult">
        SELECT
        gac.*,
        gar.title,
        gu.user_name AS userName,
        gu.mobile_phone AS mobilePhone,
        gu.medal_level AS medalLevel,
        ga.address AS imgUrl
        FROM
        gdmm_article_comment gac
        LEFT JOIN gdmm_article gar ON gar.id = gac.article_id
        LEFT JOIN gdmm_users gu ON gu.user_id = gac.uid
        LEFT JOIN gdmm_avatar ga ON ga.uid = gac.uid
        <where>
            <if test="param.id != null">
                AND gac.id = #{param.id}
            </if>
            <if test="param.articleId != null">
                AND gac.article_id = #{param.articleId}
            </if>
            <if test="param.uid != null">
                AND gac.uid = #{param.uid}
            </if>
            <if test="param.content != null">
                AND gac.content LIKE CONCAT('%', #{param.content}, '%')
            </if>
            <if test="param.type != null">
                AND gac.type = #{param.type}
            </if>
            <if test="param.parentId != null">
                AND gac.parent_id = #{param.parentId}
            </if>
            <if test="param.createTime != null">
                AND gac.create_time = #{param.createTime}
            </if>
            <if test="param.updateTime != null">
                AND gac.update_time = #{param.updateTime}
            </if>
            <if test="param.statusArr != null">
                AND gac.status IN
                <foreach collection="param.statusArr" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="param.status != null">
                AND gac.status = #{param.status}
            </if>
            <if test="param.title != null">
                AND gar.title LIKE CONCAT('%', #{param.title}, '%')
            </if>
            <if test="param.userName != null">
                AND gu.user_name = #{param.userName}
            </if>
            <if test="param.mobilePhone != null">
                AND gu.mobile_phone = #{param.mobilePhone}
            </if>
        </where>
        <if test="param.orderBy == null">
            ORDER BY gac.id DESC
        </if>
        <!-- 排序扩展 -->
        <if test="param.orderBy != null">
            <if test="param.orderBy == 0">
                <!-- 这里自定义 -->
            </if>
            <if test="param.orderBy == 1">
                <!-- 这里自定义 -->
            </if>
        </if>
    </select>


    <select id="findById" parameterType="java.util.Map" resultMap="GdmmArticleCommentResult">
        SELECT gc.*,
               CASE
                   WHEN gc.res_type = 0 THEN (SELECT title FROM gdmm_video WHERE id = gc.res_id)
                   WHEN gc.res_type = 1 THEN (SELECT name FROM bc_program_live WHERE id = gc.res_id)
                   ELSE (SELECT name FROM bc_program_tv_live WHERE id = gc.res_id)
                   END         AS title,
               gu.user_name    AS userName,
               gu.mobile_phone AS mobilePhone,
               gu.medal_level  AS medalLevel,
               ga.address      AS imgUrl
        FROM gdmm_comment gc
                 LEFT JOIN gdmm_users gu ON gu.user_id = gc.uid
                 LEFT JOIN gdmm_avatar ga ON ga.uid = gc.uid
        WHERE gc.id = #{id}
    </select>

</mapper>