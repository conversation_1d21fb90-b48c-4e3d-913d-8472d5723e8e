<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmArticleCatMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmArticleCat" id="GdmmArticleCatResult">
        <result property="id" column="id"/>
        <result property="catName" column="cat_name"/>
        <result property="type" column="type"/>
        <result property="remark" column="remark"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="enabled" column="enabled"/>
        <result property="imgUrl" column="img_url"/>
        <result property="chinasoApi" column="chinaso_api"/>
        <result property="linkType" column="link_type"/>
        <result property="linkModule" column="link_module"/>
        <result property="resourceId" column="resource_id"/>
        <result property="linkTo" column="link_to"/>
        <result property="linkUrl" column="link_url"/>
        <result property="isDefault" column="isDefault"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  cat_name,  type,  remark,  sort,  status,  enabled,  img_url,  chinaso_api,  link_type,  link_module,  resource_id,  link_to,  link_url,  isDefault
    </sql>

</mapper>