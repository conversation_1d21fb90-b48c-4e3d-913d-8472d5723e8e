<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmHomeMenuMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmHomeMenu" id="GdmmHomeMenuResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="menuType" column="menu_type"/>
        <result property="resourceType" column="resource_type"/>
        <result property="code" column="code"/>
        <result property="sort" column="sort"/>
        <result property="enabled" column="enabled"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  name,  menu_type,  resource_type,  code,  sort,  enabled,  status
    </sql>

</mapper>