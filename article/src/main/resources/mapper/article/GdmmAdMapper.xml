<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmAdMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmAd" id="GdmmAdResult">
        <result property="id" column="id"/>
        <result property="positionId" column="position_id"/>
        <result property="positionCode" column="position_code"/>
        <result property="imgUrl" column="img_url"/>
        <result property="mediaType" column="media_type"/>
        <result property="name" column="name"/>
        <result property="title" column="title"/>
        <result property="linkUrl" column="link_url"/>
        <result property="rawId" column="raw_id"/>
        <result property="skipUrl" column="skip_url"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="enabled" column="enabled"/>
        <result property="linkType" column="link_type"/>
        <result property="linkModule" column="link_module"/>
        <result property="resourceId" column="resource_id"/>
        <result property="isLink" column="is_link"/>
        <result property="linkName" column="link_name"/>
        <result property="sort" column="sort"/>
        <result property="linkTo" column="link_to"/>
        <result property="areaIds" column="area_ids"/>
        <result property="articleCats" column="article_cats"/>
        <result property="lineNum" column="line_num"/>
        <result property="showTime" column="show_time"/>
        <result property="isAnswer" column="is_answer"/>
        <result property="questions" column="questions"/>
        <result property="option1" column="option1"/>
        <result property="option2" column="option2"/>
        <result property="answer" column="answer"/>
        <result property="clienttype" column="clienttype"/>
        <result property="type" column="type"/>
        <result property="foreignId" column="foreign_id"/>
        <result property="foreignParentId" column="foreign_parent_id"/>
        <result property="viewed" column="viewed"/>
        <result property="resourceParamIds" column="resource_param_ids"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  position_id,  position_code,  img_url,  media_type,  name,  title,  link_url,  raw_id,  skip_url,  start_time,  end_time,  enabled,  link_type,  link_module,  resource_id,  is_link,  link_name,  sort,  link_to,  area_ids,  article_cats,  line_num,  show_time,  is_answer,  questions,  option1,  option2,  answer,  clienttype,  type,  foreign_id,  foreign_parent_id,  viewed,  resource_param_ids
    </sql>

</mapper>