<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmVideoMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmVideo" id="GdmmVideoResult">
        <result property="id" column="id"/>
        <result property="catId" column="cat_id"/>
        <result property="subjectId" column="subject_id"/>
        <result property="subjectCatId" column="subject_cat_id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="viewed" column="viewed"/>
        <result property="initView" column="init_view"/>
        <result property="commentNum" column="comment_num"/>
        <result property="praiseNum" column="praise_num"/>
        <result property="sourceId" column="source_id"/>
        <result property="author" column="author"/>
        <result property="content" column="content"/>
        <result property="videoUrl" column="video_url"/>
        <result property="videoSecond" column="video_second"/>
        <result property="videoImgUrl" column="video_img_url"/>
        <result property="videoImgWidth" column="video_img_width"/>
        <result property="videoImgHeight" column="video_img_height"/>
        <result property="isRecommend" column="is_recommend"/>
        <result property="isComment" column="is_comment"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="enabled" column="enabled"/>
        <result property="managerId" column="manager_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="labelId" column="label_id"/>
        <result property="idForThird" column="id_for_third"/>
        <result property="cardId" column="card_id"/>
        <result property="catName" column="cat_name"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  cat_id,  subject_id,  subject_cat_id,  type,  title,  viewed,  init_view,  comment_num,  praise_num,  source_id,  author,  content,  video_url,  video_second,  video_img_url,  video_img_width,  video_img_height,  is_recommend,  is_comment,  sort,  status,  enabled,  manager_id,  create_time,  update_time,  label_id,  id_for_third,  card_id     </sql>


    <select id="search" parameterType="java.util.Map" resultMap="GdmmVideoResult">
        SELECT gv.*, cat.name AS cat_name
        ,temp.id AS goods_id
        FROM gdmm_video gv
        LEFT JOIN gdmm_dictionary cat ON gv.cat_id = cat.id
        LEFT JOIN (
        SELECT gg.id, gg.short_video_id FROM gdmm_goods gg WHERE gg.short_video_id > 0
        GROUP BY gg.short_video_id
        ) temp ON gv.id = temp.short_video_id
        <where>
            <if test="param.id != null">
                AND gv.id = #{param.id}
            </if>
            <if test="param.catId != null">
                AND gv.cat_id = #{param.catId}
            </if>
            <if test="param.subjectId != null">
                AND gv.subject_id = #{param.subjectId}
            </if>
            <if test="param.subjectCatId != null">
                AND gv.subject_cat_id = #{param.subjectCatId}
            </if>
            <if test="param.type != null">
                AND gv.type = #{param.type}
            </if>
            <if test="param.title != null">
                AND gv.title LIKE CONCAT('%', #{param.title}, '%')
            </if>
            <if test="param.viewed != null">
                AND gv.viewed = #{param.viewed}
            </if>
            <if test="param.initView != null">
                AND gv.init_view = #{param.initView}
            </if>
            <if test="param.commentNum != null">
                AND gv.comment_num = #{param.commentNum}
            </if>
            <if test="param.praiseNum != null">
                AND gv.praise_num = #{param.praiseNum}
            </if>
            <if test="param.sourceId != null">
                AND gv.source_id = #{param.sourceId}
            </if>
            <if test="param.author != null">
                AND gv.author LIKE CONCAT('%', #{param.author}, '%')
            </if>
            <if test="param.content != null">
                AND gv.content = #{param.content}
            </if>
            <if test="param.videoUrl != null">
                AND gv.video_url = #{param.videoUrl}
            </if>
            <if test="param.videoSecond != null">
                AND gv.video_second = #{param.videoSecond}
            </if>
            <if test="param.videoImgUrl != null">
                AND gv.video_img_url = #{param.videoImgUrl}
            </if>
            <if test="param.isRecommend != null">
                AND gv.is_recommend = #{param.isRecommend}
            </if>
            <if test="param.isComment != null">
                AND gv.is_comment = #{param.isComment}
            </if>
            <if test="param.sort != null">
                AND gv.sort = #{param.sort}
            </if>
            <if test="param.status != null">
                AND gv.status = #{param.status}
            </if>
            <if test="param.publishStatus != null">
                AND gv.publish_status = #{param.publishStatus}
            </if>
            <if test="param.statusArr != null">
                AND gv.status IN
                <foreach collection="param.statusArr" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="param.catStatus != null">
                AND cat.status = #{param.catStatus}
            </if>
            <if test="param.catValue != null">
                AND cat.value = #{param.catValue}
            </if>
            <if test="param.goodsId != null">
                AND gv.id NOT IN (SELECT short_video_id FROM gdmm_goods WHERE id != #{param.goodsId})
            </if>
            <if test="param.enabled != null">
                AND gv.enabled = #{param.enabled}
            </if>
            <if test="param.managerId != null">
                AND gv.manager_id = #{param.managerId}
            </if>
            <if test="param.createTime != null">
                AND gv.create_time = #{param.createTime}
            </if>
            <if test="param.updateTime != null">
                AND gv.update_time = #{param.updateTime}
            </if>
            <if test="param.labelId != null">
                AND gv.label_id = #{param.labelId}
            </if>
            <if test="param.beginTime != null">
                AND gv.create_time >= #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND gv.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.isCardId != null">
                <choose>
                    <when test="param.isCardId == 0">
                        AND gv.card_id = 0
                    </when>
                    <when test="param.isCardId == 1">
                        AND gv.card_id > 0
                    </when>
                </choose>
            </if>
        </where>
        <choose>
            <when test="param.orderProperty != null">
                <if test="param.orderDirection != null">
                    ORDER BY gv.#{param.orderProperty} #{param.orderDirection}, gv.id DESC
                </if>
                <if test="param.orderDirection == null">
                    ORDER BY gv.#{param.orderProperty} DESC, gv.id DESC
                </if>
            </when>
            <otherwise>
                ORDER BY gv.id DESC
            </otherwise>
        </choose>
    </select>

    <select id="auditCount" parameterType="java.util.Map" resultType="long">
        SELECT count(*)
        FROM gdmm_video gv
        INNER JOIN gdmm_dictionary cat ON gv.cat_id = cat.id
        <where> cat.status = 0
            <if test="param.status != null">
                AND gv.status = #{param.status}
            </if>
        </where>
    </select>

    <update id="redis2db" parameterType="java.util.Map">
        UPDATE gdmm_video
        <set>
            <if test="clickNum != null">
                viewed = #{clickNum},
            </if>
            <if test="commentNum != null">
                comment_num = #{commentNum},
            </if>
            <if test="praiseNum != null">
                praise_num = #{praiseNum},
            </if>
        </set>
        WHERE
        id = #{id}
    </update>

</mapper>