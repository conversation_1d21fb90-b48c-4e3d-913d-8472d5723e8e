<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmArticleLiveMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmArticleLive" id="GdmmArticleLiveResult">
        <result property="id" column="id"/>
        <result property="articleId" column="article_id"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="imgUrl" column="img_url"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  article_id,  content,  create_time,  status,  img_url
    </sql>

</mapper>