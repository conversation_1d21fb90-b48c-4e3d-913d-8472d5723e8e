<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmArticleSubjectMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmArticleSubject" id="GdmmArticleSubjectResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="remark" column="remark"/>
        <result property="type" column="type"/>
        <result property="imgUrl" column="img_url"/>
        <result property="enabled" column="enabled"/>
        <result property="sourceId" column="source_id"/>
        <result property="recommendHome" column="recommend_home"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="managerId" column="manager_id"/>
        <result property="status" column="status"/>
        <result property="cardId" column="card_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  title,  remark,  type,  img_url,  enabled,  source_id,  recommend_home,  sort,  create_time,  update_time,  manager_id,  status,  card_id     </sql>

</mapper>