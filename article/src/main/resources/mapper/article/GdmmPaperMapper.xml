<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmPaperMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmPaper" id="GdmmPaperResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="imgUrl" column="img_url"/>
        <result property="linkUrl" column="link_url"/>
        <result property="enabled" column="enabled"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="managerId" column="manager_id"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  title,  img_url,  link_url,  enabled,  sort,  create_time,  update_time,  manager_id,  status     </sql>

</mapper>