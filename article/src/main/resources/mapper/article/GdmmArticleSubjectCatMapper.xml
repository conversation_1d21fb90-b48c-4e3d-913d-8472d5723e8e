<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmArticleSubjectCatMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmArticleSubjectCat" id="GdmmArticleSubjectCatResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="enabled" column="enabled"/>
        <result property="articleSubjectId" column="article_subject_id"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  name,  enabled,  article_subject_id,  sort,  status     </sql>

</mapper>