<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmCommentMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmComment" id="GdmmCommentResult">
        <result property="id" column="id"/>
        <result property="resId" column="res_id"/>
        <result property="uid" column="uid"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="resType" column="res_type"/>
        <result property="floor" column="floor"/>
        <result property="parentId" column="parent_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditUid" column="audit_uid"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="msgId" column="msg_id"/>
        <result property="msgType" column="msg_type"/>
        <result property="auditRejectReason" column="audit_reject_reason"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  res_id,  uid,  content,  type,  res_type,  floor,  parent_id,  create_time,  update_time,  sort,  status,  audit_time,  audit_uid,  is_anonymous,  msg_id,  msg_type,  audit_reject_reason
    </sql>

    <select id="selectGdmmCommentList" parameterType="java.util.Map" resultMap="GdmmCommentResult">
        SELECT
        gc.*,
        IF(gc.res_type = 0,
        (SELECT title FROM gdmm_video WHERE id = gc.res_id),
        IF(gc.res_type = 1,
        (SELECT name FROM bc_program_live WHERE id = gc.res_id),
        (SELECT name FROM bc_program_tv_live WHERE id = gc.res_id))
        ) AS "title",
        gu.user_name AS userName,
        gu.mobile_phone AS mobilePhone,
        gu.medal_level AS medalLevel,
        ga.address AS imgUrl
        FROM
        gdmm_comment gc
        LEFT JOIN gdmm_users gu ON gu.user_id = gc.uid
        LEFT JOIN gdmm_avatar ga ON ga.uid = gc.uid
        <where>
            <if test="param.id != null">
                AND gc.id = #{param.id}
            </if>
            <if test="param.resId != null">
                AND gc.res_id = #{param.resId}
            </if>
            <if test="param.uid != null">
                AND gc.uid = #{param.uid}
            </if>
            <if test="param.content != null">
                AND gc.content LIKE CONCAT('%', #{param.content}, '%')
            </if>
            <if test="param.type != null">
                AND gc.type = #{param.type}
            </if>
            <if test="param.resType != null">
                AND gc.res_type = #{param.resType}
            </if>
            <if test="param.floor != null">
                AND gc.floor = #{param.floor}
            </if>
            <if test="param.parentId != null">
                AND gc.parent_id = #{param.parentId}
            </if>
            <if test="param.createTime != null">
                AND gc.create_time = #{param.createTime}
            </if>
            <if test="param.updateTime != null">
                AND gc.update_time = #{param.updateTime}
            </if>
            <if test="param.statusArr != null and param.statusArr.size() > 0">
                AND gc.status IN
                <foreach item="statusItem" collection="param.statusArr" open="(" close=")" separator=",">
                    #{statusItem}
                </foreach>
            </if>
            <if test="param.status != null">
                AND gc.status = #{param.status}
            </if>
            <if test="param.title != null">
                AND IF(gc.res_type = 0,
                (SELECT title FROM gdmm_video WHERE id = gc.res_id),
                IF(gc.res_type = 1,
                (SELECT name FROM bc_program_live WHERE id = gc.res_id),
                (SELECT name FROM bc_program_tv_live WHERE id = gc.res_id))
                ) LIKE CONCAT('%', #{param.title}, '%')
            </if>
            <if test="param.userName != null">
                AND gu.user_name = #{param.userName}
            </if>
            <if test="param.mobilePhone != null">
                AND gu.mobile_phone = #{param.mobilePhone}
            </if>
        </where>
        <choose>
            <when test="param.orderBy == 0">
                ORDER BY gc.id DESC
            </when>
            <when test="param.orderBy == 1">
                <!-- 自定义排序条件 -->
            </when>
            <otherwise>
                <!-- 默认排序条件 -->
                ORDER BY gc.id DESC
            </otherwise>
        </choose>
    </select>


    <update id="updateByMap" parameterType="java.util.Map">
        UPDATE gdmm_comment
        <set>
            <if test="resId != null">
                res_id = #{resId},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="floor != null">
                floor = #{floor},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
        </set>
        <where>
            <if test="id != null">
                AND (id = #{id}
                <if test="parentId != null">
                    OR parent_id = #{parentId}
                </if>)
            </if>
            <if test="uid != null">
                AND uid = #{uid}
            </if>
            <if test="resType != null">
                AND res_type = #{resType}
            </if>
            <if test="commentId != null">
                AND id = #{commentId}
            </if>
            <if test="ids != null and ids.size() > 0">
                AND id IN
                <foreach item="idItem" collection="ids" open="(" close=")" separator=",">
                    #{idItem}
                </foreach>
            </if>
        </where>
    </update>

    <select id="count" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        gdmm_comment gc
        LEFT JOIN gdmm_video gv ON gv.id = gc.res_id
        LEFT JOIN gdmm_users gu ON gu.user_id = gc.uid
        LEFT JOIN gdmm_avatar ga ON ga.uid = gc.uid
        <where>
            <if test="param.id != null">
                AND gc.id = #{param.id}
            </if>
            <if test="param.isDelete != null">
                AND (gc.id = #{param.idDelete} OR gc.parent_id = #{param.idDelete})
            </if>
            <if test="param.resId != null">
                AND gc.res_id = #{param.resId}
            </if>
            <if test="param.uid != null">
                AND gc.uid = #{param.uid}
            </if>
            <if test="param.content != null">
                AND gc.content LIKE CONCAT('%', #{param.content}, '%')
            </if>
            <if test="param.type != null">
                AND gc.type = #{param.type}
            </if>
            <if test="param.resType != null">
                <choose>
                    <when test="param.resType == 1">
                        AND gc.res_type IN (1, 2)
                    </when>
                    <otherwise>
                        AND gc.res_type = #{param.resType}
                    </otherwise>
                </choose>
            </if>
            <if test="param.floor != null">
                AND gc.floor = #{param.floor}
            </if>
            <if test="param.idForFloor != null">
                AND gc.id &lt;= #{param.idForFloor}
            </if>
            <if test="param.parentId != null">
                AND gc.parent_id = #{param.parentId}
            </if>
            <if test="param.createTime != null">
                AND gc.create_time = #{param.createTime}
            </if>
            <if test="param.updateTime != null">
                AND gc.update_time = #{param.updateTime}
            </if>
            <if test="param.statusArr != null and param.statusArr.size() > 0">
                AND gc.status IN
                <foreach item="statusItem" collection="param.statusArr" open="(" close=")" separator=",">
                    #{statusItem}
                </foreach>
            </if>
            <if test="param.status != null">
                AND gc.status = #{param.status}
            </if>
            <if test="param.title != null">
                AND IF(gc.res_type = 0,
                (SELECT title FROM gdmm_video WHERE id = gc.res_id),
                IF(gc.res_type = 1,
                (SELECT name FROM bc_program_live WHERE id = gc.res_id),
                (SELECT name FROM bc_program_tv_live WHERE id = gc.res_id))
                ) LIKE CONCAT('%', #{param.title}, '%')
            </if>
            <if test="param.userName != null">
                AND gu.user_name = #{param.userName}
            </if>
            <if test="param.mobilePhone != null">
                AND gu.mobile_phone = #{param.mobilePhone}
            </if>
        </where>
    </select>

</mapper>