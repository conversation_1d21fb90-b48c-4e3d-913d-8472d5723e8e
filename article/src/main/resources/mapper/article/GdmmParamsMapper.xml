<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmParamsMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmParams" id="GdmmParamsResult">
        <result property="id" column="id"/>
        <result property="module" column="module"/>
        <result property="code" column="code"/>
        <result property="value" column="value"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="display" column="display"/>
        <result property="isOpen" column="is_open"/>
        <result property="isExtend" column="is_extend"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  module,  code,  value,  remark,  status,  create_time,  update_time,  display,  is_open,  is_extend     </sql>

</mapper>