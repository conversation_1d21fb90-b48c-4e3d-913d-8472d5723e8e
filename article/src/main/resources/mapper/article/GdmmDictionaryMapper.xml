<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmDictionaryMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmDictionary" id="GdmmDictionaryResult">
        <result property="id" column="id"/>
        <result property="module" column="module"/>
        <result property="code" column="code"/>
        <result property="value" column="value"/>
        <result property="name" column="name"/>
        <result property="imgUrl" column="img_url"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  module,  code,  value,  name,  img_url,  status,  create_time,  update_time,  sort
    </sql>

</mapper>