<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zainanjing.article.mapper.GdmmNoticeMapper">

    <resultMap type="com.zainanjing.article.domain.GdmmNotice" id="GdmmNoticeResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="uid" column="uid"/>
        <result property="content" column="content"/>
        <result property="imgUrl" column="img_url"/>
        <result property="status" column="status"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="type" column="type"/>
        <result property="sourceId" column="source_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  title,  uid,  content,  img_url,  status,  sort,  create_time,  update_time,  type,  source_id     </sql>

</mapper>